service: ${self:custom.service}-common-resources

provider:
  name: aws
  runtime: nodejs20.x
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}
  stackTags: ${self:custom.tags}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}
  tracing:
    lambda: true
  environment:
    DISTRIBUTION_BUCKET_NAME: ${self:custom.params.DISTRIBUTION_BUCKET_NAME}
    VENDOR_GENERATING_FILE_PATH: ${self:custom.params.VENDOR_GENERATING_FILE_PATH}
    VENDOR_UPLOADING_FILE_PATH: ${self:custom.params.VENDOR_UPLOADING_FILE_PATH}
    WO_GENERATING_FILE_PATH: ${self:custom.params.WO_GENERATING_FILE_PATH}
    WO_UPLOADING_FILE_PATH: ${self:custom.params.WO_UPLOADING_FILE_PATH}
    PO_GENERATING_FILE_PATH: ${self:custom.params.PO_GENERATING_FILE_PATH}
    PO_UPLOADING_FILE_PATH: ${self:custom.params.PO_UPLOADING_FILE_PATH}
    TASK_CARD_GENERATING_FILE_PATH: ${self:custom.params.TASK_CARD_GENERATING_FILE_PATH}
    TASK_CARD_UPLOADING_FILE_PATH: ${self:custom.params.TASK_CARD_UPLOADING_FILE_PATH}
    ENGINE_DATA_GENERATING_FILE_PATH: ${self:custom.params.ENGINE_DATA_GENERATING_FILE_PATH}
    ENGINE_DATA_UPLOADING_FILE_PATH: ${self:custom.params.ENGINE_DATA_UPLOADING_FILE_PATH}
    PENDING_FILE_GENERATING_PATH: ${self:custom.params.PENDING_FILE_GENERATING_PATH}
    PROCESSED_FILE_GENERATING_PATH: ${self:custom.params.PROCESSED_FILE_GENERATING_PATH}

  plugins:
    - serverless-offline
    - serverless-prune-plugin
    - serverless-plugin-log-retention
    - serverless-webpack

package:
  individually: true

custom:
  defaults: ${file(../../defaults.yml)}
  tags: ${self:custom.defaults.custom.tags}
  service: ${self:custom.tags.service}
  base: ${self:service}-${self:provider.stage}
  params: ${self:custom.defaults.custom.params.${self:provider.stage}}
  layerStackName: dbaas-sre-infra-v3-layer-${self:provider.stage}
  webpack:
    webpackConfig: webpack.config.js
    packager: "npm"
  prune: ${self:custom.defaults.custom.prune}
  serverless-offline:
    httpPort: 4000

resources:
  Resources:
    S3UploaderLambdaRole: ${file(./resources/s3UploaderLambdaRole.yml)}
    ErrorNotifyTopic: ${file(./resources/errorNotifyTopic.yml)}

    S3UploaderLambdaRule:
      Type: AWS::Events::Rule
      Properties:
        Name: ac-odh-webfocus-common-resources-${self:provider.stage}-s3-uploader-rule
        Description: EventBridge rule to trigger S3 Uploader Lambda function when an object is created
        EventPattern: 
          source: 
            - aws.s3
          detail-type: 
            - Object Created
          detail:
            bucket:
              name: 
                - ${self:custom.params.DISTRIBUTION_BUCKET_NAME}
            object:
              key: 
                - prefix: ${self:custom.params.VENDOR_GENERATING_FILE_PATH}${self:custom.params.PENDING_FILE_GENERATING_PATH}
                - prefix: ${self:custom.params.WO_GENERATING_FILE_PATH}${self:custom.params.PENDING_FILE_GENERATING_PATH}
                - prefix: ${self:custom.params.PO_GENERATING_FILE_PATH}${self:custom.params.PENDING_FILE_GENERATING_PATH}
                - prefix: ${self:custom.params.TASK_CARD_GENERATING_FILE_PATH}${self:custom.params.PENDING_FILE_GENERATING_PATH}
                - prefix: ${self:custom.params.ENGINE_DATA_GENERATING_FILE_PATH}${self:custom.params.PENDING_FILE_GENERATING_PATH}
        State: ENABLED
        EventBusName: default
        Targets:
          - Id: Target_A
            Arn:
              Fn::GetAtt:
                - S3DashuploaderLambdaFunction
                - Arn

    S3UploaderLambdaInvokePermission:
      Type: AWS::Lambda::Permission
      DependsOn: S3UploaderLambdaRule
      Properties: 
        FunctionName:
          Ref: S3DashuploaderLambdaFunction
        Action: "lambda:InvokeFunction"
        Principal: "events.amazonaws.com"
        SourceArn:
          Fn::GetAtt:
            - S3UploaderLambdaRule
            - Arn

  Outputs:
    ErrorNotifyTopicArn:
      Value:
        Ref: ErrorNotifyTopic
      Export:
        Name: error-notify-topic-arn

functions:
  s3-uploader: 
    name: ac-odh-webfocus-common-resources-uploader
    handler: functions/s3-uploader.handler
    timeout: ${self:custom.params.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.params.LAMBDA_MEMORY}
    logRetentionInDays: ${self:custom.params.LOG_RETENTION_IN_DAYS}
    role: S3UploaderLambdaRole
    vpc:
      securityGroupIds:
        - ${cf:ac-odh-common-resources-sg-${self:provider.stage}.sgIdDigitalOdsRdsCredentials}
      subnetIds:
        - ${self:custom.params.RDS_CONNECTION_SUBNET_ID_A}
        - ${self:custom.params.RDS_CONNECTION_SUBNET_ID_B}
        - ${self:custom.params.RDS_CONNECTION_SUBNET_ID_D}
    layers:
      - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
      - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_223_1_20210716-104735_nodejs:1
    environment:
      DEFAULT_LANG: ${self:custom.params.DEFAULT_LANG}
      DEBUG_MODE: ${self:custom.params.DEBUG_MODE}
      LAYER_DEBUG_MODE: ${self:custom.params.LAYER_DEBUG_MODE}
      LAYER_ENABLED: ${self:custom.params.LAYER_ENABLED}
      EXTENSION_LOG_MAX_ITEMS: ${self:custom.params.EXTENSION_LOG_MAX_ITEMS}
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: ${self:custom.params.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME}
      EXTENSION_LOG_MAX_BYTES: ${self:custom.params.EXTENSION_LOG_MAX_BYTES}
      EXTENSION_LOG_TIMEOUT_MS: ${self:custom.params.EXTENSION_LOG_TIMEOUT_MS}
      EXTENSION_LOG_DEBUG_MODE: ${self:custom.params.EXTENSION_LOG_DEBUG_MODE}
      EXTENSION_LOG_ENVIRONMENT: ${self:custom.params.EXTENSION_LOG_ENVIRONMENT}
      EXTENSION_LOG_S3_BUCKET_REGION: ${self:custom.params.EXTENSION_LOG_S3_BUCKET_REGION}
      LOG_STREAM_NAME: ${self:custom.params.LOG_STREAM_NAME}
      LAMBDA_LOG_MODE: ${self:custom.params.LAMBDA_LOG_MODE}
      WEBFOCUS_S3_BUCKET: ${self:custom.params.WEBFOCUS_S3_BUCKET}
      SNS_TOPIC_ARN: !Ref ErrorNotifyTopic

