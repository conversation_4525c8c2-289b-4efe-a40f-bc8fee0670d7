Type: AWS::IAM::Role
Properties:
  RoleName: ac-odh-webfocus-common-resources-${self:provider.stage}-uploader-lr
  AssumeRolePolicyDocument:
    Version: "2012-10-17"
    Statement:
      - Effect: Allow
        Principal:
          Service: lambda.amazonaws.com
        Action: sts:AssumeRole
  Path: "/"
  Policies:
    - PolicyName: S3UploaderPolicy
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Action:
              - s3:GetObject
              - s3:PutObject
              - s3:CopyObject
              - s3:DeleteObject
              - s3:ListBucket
            Resource:
              - arn:aws:s3:::${self:custom.params.DISTRIBUTION_BUCKET_NAME}
              - arn:aws:s3:::${self:custom.params.DISTRIBUTION_BUCKET_NAME}/*
              - arn:aws:s3:::${self:custom.params.WEBFOCUS_S3_BUCKET}/*
              - "arn:aws:s3:::${self:custom.params.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME}"
              - "arn:aws:s3:::${self:custom.params.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME}/*"
    -
      PolicyName: "WriteLogPolicy"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: ${self:custom.params.CW_PERMISSION}
            Action:
              - "logs:CreateLogGroup"
              - "logs:CreateLogStream"
              - "logs:PutLogEvents"
            Resource:
              - "arn:aws:logs:*:*:/aws/lambda/*"
    
    -
      PolicyName: "CloudwatchAccess"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "cloudwatch:PutMetricData"
            Resource:
              - "*"
    
    -
      PolicyName: "NetworkInterface"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "ec2:DescribeNetworkInterfaces"
              - "ec2:CreateNetworkInterface"
              - "ec2:DeleteNetworkInterface"
              - "ec2:DescribeInstances"
              - "ec2:AttachNetworkInterface"
            Resource:
              - "*"
    -
      PolicyName: "Kinesis"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - 'kinesis:*'
            Resource:
              - "*"
    -
      PolicyName: "SNSPublish"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "sns:Publish"
            Resource:
              - !Ref ErrorNotifyTopic
