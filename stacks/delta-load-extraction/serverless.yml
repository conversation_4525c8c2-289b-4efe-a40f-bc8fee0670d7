service: ${self:custom.service}-delta-load

provider:
  name: aws
  runtime: nodejs20.x
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}
  stackTags: ${self:custom.tags}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}
  tracing:
    lambda: true
  environment:
    TRAX_DB_SECRET: ${self:custom.params.TRAX_DB_SECRET}
    DB_CONNECTION_RETRY_ATTEMPTS: ${self:custom.params.DB_CONNECTION_RETRY_ATTEMPTS}
    DB_CONNECTION_MIN_POOL_SIZE: ${self:custom.params.DB_CONNECTION_MIN_POOL_SIZE}
    DB_CONNECTION_MAX_POOL_SIZE: ${self:custom.params.DB_CONNECTION_MAX_POOL_SIZE}
    DISTRIBUTION_BUCKET_NAME: ${self:custom.params.DISTRIBUTION_BUCKET_NAME}
    KNEX_DEBUG: ${self:custom.params.KNEX_DEBUG}
    AWS_LAMBDA_EXEC_WRAPPER: ${self:custom.params.AWS_LAMBDA_EXEC_WRAPPER}
    DT_TENANT: ${self:custom.params.DT_TENANT}
    DT_CLUSTER_ID: ${self:custom.params.DT_CLUSTER_ID}
    DT_CONNECTION_BASE_URL: ${self:custom.params.DT_CONNECTION_BASE_URL}
    DT_CONNECTION_AUTH_TOKEN: ${self:custom.params.DT_CONNECTION_AUTH_TOKEN}
    SNS_TOPIC_ARN: ${cf:ac-odh-batch-distribution-webfocus-common-resources-${self:provider.stage}.ErrorNotifyTopicArn}
    PENDING_FILE_GENERATING_PATH: ${self:custom.params.PENDING_FILE_GENERATING_PATH}
    #dbaas logs
    DEFAULT_LANG: ${self:custom.params.DEFAULT_LANG}
    DEBUG_MODE: ${self:custom.params.DEBUG_MODE}
    LAYER_DEBUG_MODE: ${self:custom.params.LAYER_DEBUG_MODE}
    LAYER_ENABLED: ${self:custom.params.LAYER_ENABLED}
    EXTENSION_LOG_MAX_ITEMS: ${self:custom.params.EXTENSION_LOG_MAX_ITEMS}
    EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: ${self:custom.params.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME}
    EXTENSION_LOG_MAX_BYTES: ${self:custom.params.EXTENSION_LOG_MAX_BYTES}
    EXTENSION_LOG_TIMEOUT_MS: ${self:custom.params.EXTENSION_LOG_TIMEOUT_MS}
    EXTENSION_LOG_DEBUG_MODE: ${self:custom.params.EXTENSION_LOG_DEBUG_MODE}
    EXTENSION_LOG_ENVIRONMENT: ${self:custom.params.EXTENSION_LOG_ENVIRONMENT}
    EXTENSION_LOG_S3_BUCKET_REGION: ${self:custom.params.EXTENSION_LOG_S3_BUCKET_REGION}
    LOG_STREAM_NAME: ${self:custom.params.LOG_STREAM_NAME}
    LAMBDA_LOG_MODE: ${self:custom.params.LAMBDA_LOG_MODE}
  vpc:
    subnetIds:
      - ${self:custom.params.RDS_CONNECTION_SUBNET_ID_A}
      - ${self:custom.params.RDS_CONNECTION_SUBNET_ID_B}
      - ${self:custom.params.RDS_CONNECTION_SUBNET_ID_D}
    securityGroupIds:
      - ${cf:ac-odh-common-resources-sg-${self:provider.stage}.sgIdDigitalOdsRdsCredentials}

build:
  esbuild:
    bundle: true
    minify: false
    packages: external

custom:
  defaults: ${file(../../defaults.yml)}
  tags: ${self:custom.defaults.custom.tags}
  service: ${self:custom.tags.service}
  base: ${self:service}-${self:provider.stage}
  params: ${self:custom.defaults.custom.params.${self:provider.stage}}
  layerStackName: dbaas-sre-infra-v3-layer-${self:provider.stage}
  esbuild:
    bundle: true
    minify: false
    sourcemap: false

functions:
  vendor-extraction:
    handler: functions/vendor-extraction.handler
    name: ${self:custom.base}-vendor
    role: ${cf:ac-odh-common-resources-roles-${self:provider.stage}.ingestionLambdaRoleArn}
    timeout: ${self:custom.params.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.params.LAMBDA_MEMORY}
    reservedConcurrency: ${self:custom.params.LAMBDA_RESERVED_CONCURRENCY}
    layers:
      - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
      - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_223_1_20210716-104735_nodejs:1
    environment:
      VENDOR_GENERATING_FILE_PATH: ${self:custom.params.VENDOR_GENERATING_FILE_PATH}
      FILE_PREFIX_VENDOR: ${self:custom.params.FILE_PREFIX_VENDOR}
  wo-extraction:
    handler: functions/wo-extraction.handler
    name: ${self:custom.base}-wo
    role: ${cf:ac-odh-common-resources-roles-${self:provider.stage}.ingestionLambdaRoleArn}
    timeout: ${self:custom.params.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.params.LAMBDA_MEMORY}
    reservedConcurrency: ${self:custom.params.LAMBDA_RESERVED_CONCURRENCY}
    layers:
      - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
      - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_223_1_20210716-104735_nodejs:1
    environment:
      WO_GENERATING_FILE_PATH: ${self:custom.params.WO_GENERATING_FILE_PATH}
      FILE_PREFIX_WO: ${self:custom.params.FILE_PREFIX_WO}
  po-extraction:
    handler: functions/po-extraction.handler
    name: ${self:custom.base}-po
    role: ${cf:ac-odh-common-resources-roles-${self:provider.stage}.ingestionLambdaRoleArn}
    timeout: ${self:custom.params.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.params.LAMBDA_MEMORY}
    reservedConcurrency: ${self:custom.params.LAMBDA_RESERVED_CONCURRENCY}
    layers:
      - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
      - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_223_1_20210716-104735_nodejs:1
    environment:
      PO_GENERATING_FILE_PATH: ${self:custom.params.PO_GENERATING_FILE_PATH}
      FILE_PREFIX_PO: ${self:custom.params.FILE_PREFIX_PO}
  task-card-extraction:
    handler: functions/task-card-extraction.handler
    name: ${self:custom.base}-tc
    role: ${cf:ac-odh-common-resources-roles-${self:provider.stage}.ingestionLambdaRoleArn}
    timeout: ${self:custom.params.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.params.LAMBDA_MEMORY}
    reservedConcurrency: ${self:custom.params.LAMBDA_RESERVED_CONCURRENCY}
    layers:
      - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
      - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_223_1_20210716-104735_nodejs:1
    environment:
      TASK_CARD_GENERATING_FILE_PATH: ${self:custom.params.TASK_CARD_GENERATING_FILE_PATH}
      FILE_PREFIX_TASK_CARD: ${self:custom.params.FILE_PREFIX_TASK_CARD}
  engine-data-extraction:
    handler: functions/engine-data-extraction.handler
    name: ${self:custom.base}-engine
    role: ${cf:ac-odh-common-resources-roles-${self:provider.stage}.ingestionLambdaRoleArn}
    timeout: ${self:custom.params.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.params.LAMBDA_MEMORY}
    reservedConcurrency: ${self:custom.params.LAMBDA_RESERVED_CONCURRENCY}
    layers:
      - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
      - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_223_1_20210716-104735_nodejs:1
    environment:
      ENGINE_DATA_GENERATING_FILE_PATH: ${self:custom.params.ENGINE_DATA_GENERATING_FILE_PATH}
      FILE_PREFIX_ENGINE_DATA: ${self:custom.params.FILE_PREFIX_ENGINE_DATA}
      
stepFunctions:
  stateMachines:
    dataExtractionWorkflow:
      name: ${self:custom.base}-data-extraction-workflow
      definition:
        Comment: "Sequential data extraction workflow: vendor -> wo -> po"
        StartAt: VendorExtraction
        States:
          VendorExtraction:
            Type: Task
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              FunctionName: !Ref vendor-extraction
              Payload.$: $
            Retry:
              - ErrorEquals:
                  - Lambda.ServiceException
                  - Lambda.AWSLambdaException
                  - Lambda.SdkClientException
                  - Lambda.TooManyRequestsException
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2.0
            Catch:
              - ErrorEquals:
                  - States.ALL
                Next: NotifyError
                ResultPath: $.error
            Next: WoExtraction
          
          WoExtraction:
            Type: Task
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              FunctionName: !Ref wo-extraction
              Payload.$: $
            Retry:
              - ErrorEquals:
                  - Lambda.ServiceException
                  - Lambda.AWSLambdaException
                  - Lambda.SdkClientException
                  - Lambda.TooManyRequestsException
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2.0
            Catch:
              - ErrorEquals:
                  - States.ALL
                Next: NotifyError
                ResultPath: $.error
            Next: PoExtraction
          
          PoExtraction:
            Type: Task
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              FunctionName: !Ref po-extraction
              Payload.$: $
            Retry:
              - ErrorEquals:
                  - Lambda.ServiceException
                  - Lambda.AWSLambdaException
                  - Lambda.SdkClientException
                  - Lambda.TooManyRequestsException
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2.0
            Catch:
              - ErrorEquals:
                  - States.ALL
                Next: NotifyError
                ResultPath: $.error
            Next: TaskCardExtraction

          TaskCardExtraction:
            Type: Task
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              FunctionName: !Ref task-card-extraction
              Payload.$: $
            Retry:
              - ErrorEquals:
                  - Lambda.ServiceException
                  - Lambda.AWSLambdaException
                  - Lambda.SdkClientException
                  - Lambda.TooManyRequestsException
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2.0
            Catch:
              - ErrorEquals:
                  - States.ALL
                Next: NotifyError
                ResultPath: $.error
            Next: EngineDataExtraction
          
          EngineDataExtraction:
            Type: Task
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              FunctionName: !Ref engine-data-extraction
              Payload.$: $
            Retry:
              - ErrorEquals:
                  - Lambda.ServiceException
                  - Lambda.AWSLambdaException
                  - Lambda.SdkClientException
                  - Lambda.TooManyRequestsException
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2.0
            Catch:
              - ErrorEquals:
                  - States.ALL
                Next: NotifyError
                ResultPath: $.error
            Next: Success
          
          Success:
            Type: Succeed
            Comment: "All extraction processes completed successfully"
          
          NotifyError:
            Type: Task
            Resource: arn:aws:states:::sns:publish
            Parameters:
              TopicArn: ${self:provider.environment.SNS_TOPIC_ARN}
              Subject: "Data Extraction Workflow Failed"
              Message.$: "States.Format('Execution {} of state machine {} failed with error: {}. Input: {}', $$.Execution.Name, $$.StateMachine.Name, $.error, $$.Execution.Input)"
            End: true
      
      events:
        - schedule:
            name: ${self:custom.base}-sched
            rate: ${self:custom.params.STEP_FUNCTION_RATE}
            enabled: true



plugins:
  - serverless-step-functions

