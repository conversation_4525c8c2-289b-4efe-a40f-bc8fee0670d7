const { dataExtractionService,fileHandleServive } = require("../services");
const { LOGGER,flowUtil } = require("../utils");

module.exports.handler = async (event, context) => {
  try {
    flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME_WO);
    LOGGER.start(event, "Trigger workorder extraction lambda");

    let endDate = new Date();
    let startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 1); 

    const workorderData = await dataExtractionService.getWorkorderData(startDate, endDate);
    LOGGER.info(`Workorder data retrieved. Count: ${workorderData.length}`)

    const csvStream = await fileHandleServive.generateCsv(workorderData);
    LOGGER.info("CSV stream generated");

    await fileHandleServive.woFileUpload(csvStream);

    LOGGER.info("CSV uploaded to S3");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Workorder data extracted successfully",
        count: workorderData.length,
      }),
    };
  } catch (error) {
    LOGGER.error("Error extracting workorder data:", error);

    await fileHandleServive.notifyFailedEvents("Error extracting workorder data", error.message);
    
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error extracting workorder data",
        error: error.message,
      }),
    };
  }
}