const { dataExtractionService, fileHandleServive } = require("../services");
const { LOGGER, flowUtil } = require("../utils");

module.exports.handler = async (event, context) => {
  try {
    flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME_ENGINE_DATA);
    LOGGER.start(event, "Trigger engine data extraction lambda");

    const enginesData = await dataExtractionService.getEnginesData();

    LOGGER.info(`Engines data retrieved. Count: ${enginesData.length}`);

    const csvStream = await fileHandleServive.generateCsv(enginesData);
    LOGGER.info("CSV stream generated");

    await fileHandleServive.engineDataFileUpload(csvStream);

    LOGGER.info("CSV uploaded to S3");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Engine data extracted successfully",
        count: enginesData.length,
      }),
    };
  } catch (error) {
    LOGGER.error("Error extracting PO data:", error);
    await fileHandleServive.notifyFailedEvents(
      "Error extracting Engine data",
      error.message
    );
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error extracting Engine data",
        error: error.message,
      }),
    };
  }
};
