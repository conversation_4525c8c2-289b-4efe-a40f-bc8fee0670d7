const { dataExtractionService, fileHandleServive } = require("../services");
const { LOGGER, flowUtil } = require("../utils");

module.exports.handler = async (event, context) => {
  try {
    flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME_PO);
    LOGGER.start(event, "Trigger workorder extraction lambda");

    const poData = await dataExtractionService.getPoData();

    LOGGER.info(`PO data retrieved. Count: ${poData.length}`);

    const csvStream = await fileHandleServive.generateCsv(poData);
    LOGGER.info("CSV stream generated");

    await fileHandleServive.poFileUpload(csvStream);

    LOGGER.info("CSV uploaded to S3");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "PO data extracted successfully",
        count: poData.length,
      }),
    };
  } catch (error) {
    LOGGER.error("Error extracting PO data:", error);
    await fileHandleServive.notifyFailedEvents(
      "Error extracting PO data",
      error.message
    );
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error extracting PO data",
        error: error.message,
      }),
    };
  }
};
