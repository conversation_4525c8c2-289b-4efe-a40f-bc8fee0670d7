const { dataExtractionService, fileHandleServive } = require("../services");
const { LOGGER, flowUtil } = require("../utils");

module.exports.handler = async (event, context) => {
  try {
    flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME_TASK_CARD);
    LOGGER.start(event, "Trigger workorder extraction lambda");

    const taskCardData = await dataExtractionService.getTaskCardData();

    LOGGER.info(`Task Card data retrieved. Count: ${taskCardData.length}`);

    const csvStream = await fileHandleServive.generateCsv(taskCardData);
    LOGGER.info("CSV stream generated");

    await fileHandleServive.taskCardFileUpload(csvStream);
  } catch (error) {
    LOGGER.error("Error extracting Task Card data:", error);
    await fileHandleServive.notifyFailedEvents(
      "Error extracting Task Card data",
      error.message
    );
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error extracting Task Card data",
        error: error.message,
      }),
    };
  }
};