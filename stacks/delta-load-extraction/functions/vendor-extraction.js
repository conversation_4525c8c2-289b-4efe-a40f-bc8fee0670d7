const { dataExtractionService,fileHandleServive } = require("../services");
const { LOGGER,flowUtil } = require("../utils");

module.exports.handler = async (event, context) => {
  try {
    flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME_VENDOR);
    LOGGER.start(event, "Trigger vendor extraction lambda");

    const vendorData = await dataExtractionService.getVendorData();
    LOGGER.info(`Vendor data retrieved. Count: ${vendorData.length}`)
    
    const csvStream = await fileHandleServive.generateCsv(vendorData);
    LOGGER.info("CSV stream generated");

    await fileHandleServive.vendorFileUpload(csvStream);

    LOGGER.info("CSV uploaded to S3");

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Vendor data extracted successfully",
        count: vendorData.length,
      }),
    };
  } catch (error) {
    LOGGER.error("Error extracting vendor data:", error);
    
    await fileHandleServive.notifyFailedEvents("Error extracting vendor data", error.message);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error extracting vendor data",
        error: error.message,
      }),
    };
  }
};
