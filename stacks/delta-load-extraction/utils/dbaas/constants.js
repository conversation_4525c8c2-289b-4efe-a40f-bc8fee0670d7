const LAMBDA_EXIT_STATUS_SUCCESS = "Success";
const LAMBDA_EXIT_STATUS_VALID_ERROR = "Valid-Error";
const LAMBDA_EXIT_STATUS_EXCEPTION = "Exception";

const SERVICE_TYPE = "ODH batch distribution webfocus delta load";
const SERVICE_NAME = "ac-odh-batch-distribution-wenfocus-delta-load-extraction";

const SERVICE_NAME_WO= "ac-odh-batch-distribution-wenfocus-delta-load-extraction-wo";
const SERVICE_NAME_VENDOR = "ac-odh-batch-distribution-webfocus-delta-load-extraction-vendor";
const SERVICE_NAME_PO = "ac-odh-batch-distribution-wenfocus-delta-load-extraction-po";
const SERVICE_NAME_TASK_CARD = "ac-odh-batch-distribution-wenfocus-delta-load-extraction-task-card";
const SERVICE_NAME_ENGINE_DATA = "ac-odh-batch-distribution-wenfocus-delta-load-extraction-engine-data";

module.exports = {
  LAMBDA_EXIT_STATUS_SUCCESS,
  LAMBDA_EXIT_STATUS_VALID_ERROR,
  LAMBDA_EXIT_STATUS_EXCEPTION,
  SERVICE_TYPE,
  SERVICE_NAME,
  SERVICE_NAME_WO,
  SERVICE_NAME_VENDOR,
  SERVICE_NAME_PO,
  SERVICE_NAME_TASK_CARD,
  SERVICE_NAME_ENGINE_DATA
}