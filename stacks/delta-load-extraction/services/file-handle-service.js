const { csvGenerator } = require("../utils");
const { fileStorageConnector, snsConnector } = require("../connectors");

const generateCsv = async (records) => {
  return csvGenerator.generateCsv(records);
};

const vendorFileUpload = async (file) => {
  return fileStorageConnector.putVendorFile(file);
};

const woFileUpload = async (file) => {
  return fileStorageConnector.putWOFile(file);
};

const poFileUpload = async (file) => {
  return fileStorageConnector.putPOFile(file);
};

const taskCardFileUpload = async (file) => {
  return fileStorageConnector.putTaskCardFile(file);
};

const engineDataFileUpload = async (file) => {
  return fileStorageConnector.putEngineDataFile(file);
};

const notifyFailedEvents = async (subject, errorMessage) => {
  return snsConnector.publishFailedEvents(subject, errorMessage);
};

module.exports = {
  generateCsv,
  vendorFileUpload,
  woFileUpload,
  notifyFailedEvents,
  poFileUpload,
  taskCardFileUpload,
  engineDataFileUpload
};
