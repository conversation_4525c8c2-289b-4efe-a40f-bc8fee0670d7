const { dbConnector } = require("../connectors");
const { LOGGER } = require("../utils");

const getVendorData = async () => {
  const startTime = Date.now();

  const queryBuilder = await dbConnector.getQueryBuilder();
  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  // Optimize query - only get vendor records and specific columns
  const vendorData = await queryBuilder
    .select(
      "RELATION_CODE",
      "NAME",
      "MAIL_EMAIL",
      "MAIL_PHONE",
      "MAIL_ADDRESS_1",
      "MAIL_ADDRESS_2",
      "MAIL_CITY",
      "MAIL_STATE",
      "MAIL_COUNTRY",
      "MAIL_POST"
    )
    .from("ODB.RELATION_MASTER")
    .where("RELATION_TRANSACTION", "VENDOR");

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      vendorData.length
    } vendor records`
  );
  return vendorData;
};

const getWorkorderData = async (startDate, endDate) => {
  const startTime = Date.now();

  const queryBuilder = await dbConnector.getQueryBuilder();
  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  // Format dates for Oracle (YYYY-MM-DD)
  const startDateStr = startDate.toISOString().slice(0, 10);
  const endDateStr = endDate.toISOString().slice(0, 10);
  LOGGER.info(`Date range: ${startDateStr} to ${endDateStr}`);

  const workorderData = await queryBuilder.raw(`
    WITH
        WO_INIT AS (
            SELECT
                WO.WO,
                WO.AC,
                WO.AC_TYPE,
                WO.PROJECT,
                WO.EXTERNAL_REFERENCE,
                WO.WO_CATEGORY,
                WO.PRIORITY,
                WO.STATUS,
                WO.LOCATION,
                WO.VENDOR,
                WO.SITE,
                REPLACE (REPLACE (WO.WO_DESCRIPTION, '"', ' '), '&', 'and') AS "WO_DESCRIPTION",
                WO.SCHEDULE_ORG_COMPLETION_DATE,
                WO.SCHEDULE_ORG_COMPLETION_HOUR,
                WO.SCHEDULE_ORG_COMPLETION_MINUTE,
                WO.SCHEDULE_COMPLETION_DATE,
                WO.SCHEDULE_COMPLETION_HOUR,
                WO.SCHEDULE_COMPLETION_MINUTE,
                WO.SCHEDULE_START_DATE,
                WO.SCHEDULE_START_HOUR,
                WO.SCHEDULE_START_MINUTE,
                WO.ACTUAL_START_DATE,
                WO.ACTUAL_START_HOUR,
                WO.ACTUAL_START_MINUTE,
                WO.CREATED_DATE,
                WO.CREATED_BY,
                WO.MODIFIED_DATE,
                WO.MODIFIED_BY
            FROM
                ODB.WO "WO"
            WHERE
                SUBSTR (WO.LOCATION, 4, 4) IN ('1', '2', '3', '4', '5', '6', '7', '8', '9')
                AND WO.STATUS IN ('CLOSED', 'COMPLETED', 'POSTCOMPLT', 'OPEN')
                AND (
                    WO.CREATED_BY = 'TRAXIFACE'
                    OR WO.PROJECT = 'EMS'
                )
                AND WO.SCHEDULE_COMPLETION_DATE BETWEEN SYSDATE - 365 AND SYSDATE  + 730
        ),
        WO_INFO AS (
            SELECT
                WO.WO,
                WO.AC,
                CASE
                    WHEN WO.PROJECT = 'EMS'
                    AND WO.EXTERNAL_REFERENCE IS NULL THEN WO.WO || '_' || WO.AC
                    ELSE TRIM(SUBSTR (WO.EXTERNAL_REFERENCE, 1, 10))
                END AS "EVENT_ID",
                CASE
                    WHEN WO.PROJECT = 'EMS' THEN 'EMS'
                    ELSE (
                        CASE
                            WHEN WO.WO_CATEGORY = 'HMV'
                            AND WO.PRIORITY = 'LOW' THEN 'HML'
                            ELSE (
                                CASE
                                    WHEN WO.WO_CATEGORY IN ('OOS', 'PRK') THEN WO.WO_CATEGORY
                                END
                            )
                        END
                    )
                END AS "EVENT_TYPE",
                CASE
                    WHEN WO.STATUS IN ('OPEN', 'COMPLETED') THEN TO_CHAR (WO.SCHEDULE_ORG_COMPLETION_DATE, 'YYYY-MM-DD') || ' ' || TRIM(
                        SUBSTR (
                            LPAD (WO.SCHEDULE_ORG_COMPLETION_HOUR, 2, '0') || ':' || LPAD (WO.SCHEDULE_ORG_COMPLETION_MINUTE, 2, '0'),
                            1,
                            8
                        )
                    )
                    ELSE TO_CHAR (WO.SCHEDULE_COMPLETION_DATE, 'YYYY-MM-DD') || ' ' || TRIM(
                        SUBSTR (
                            LPAD (WO.SCHEDULE_COMPLETION_HOUR, 2, '0') || ':' || LPAD (WO.SCHEDULE_COMPLETION_MINUTE, 2, '0'),
                            1,
                            8
                        )
                    )
                END AS "ACTUAL_COMPLETION_DATETIME",
                WO.SCHEDULE_START_DATE,
                TO_CHAR (WO.ACTUAL_START_DATE, 'YYYY-MM-DD') || ' ' || TRIM(
                    SUBSTR (
                        LPAD (WO.ACTUAL_START_HOUR, 2, '0') || ':' || LPAD (WO.ACTUAL_START_MINUTE, 2, '0'),
                        1,
                        8
                    )
                ) AS "ACTUAL_START_DATETIME"
            FROM
                WO_INIT "WO"
        ),
        TASK_COUNTS AS (
            SELECT
                WOTC.WO,
                COUNT(
                    CASE
                        WHEN NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                    END
                ) AS "TOTAL_TC",
                COUNT(
                    CASE
                        WHEN WOTC.STATUS = 'CANCEL'
                        AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                    END
                ) AS "TOTAL_CANCEL_TC",
                COUNT(
                    CASE
                        WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                        AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                    END
                ) AS "TOTAL_ROUTINE_TC",
                COUNT(
                    CASE
                        WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                        AND WOTC.STATUS = 'OPEN'
                        AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                    END
                ) AS "OPEN_ROUTINE_TC",
                COUNT(
                    CASE
                        WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                        AND WOTC.STATUS = 'CLOSED'
                        AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                    END
                ) AS "CLOSED_ROUTINE_TC",
                COUNT(
                    CASE
                        WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                        AND WOTC.STATUS = 'CANCEL'
                        AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                    END
                ) AS "CANCEL_ROUTINE_TC",
                COUNT(
                    CASE
                        WHEN WOTC.TASK_CARD LIKE 'NR-%'
                        AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                    END
                ) AS "TOTAL_NON_ROUTINE_TC",
                COUNT(
                    CASE
                        WHEN WOTC.TASK_CARD LIKE 'NR-%'
                        AND WOTC.STATUS = 'OPEN'
                        AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                    END
                ) AS "OPEN_NON_ROUTINE_TC",
                COUNT(
                    CASE
                        WHEN WOTC.TASK_CARD LIKE 'NR-%'
                        AND WOTC.STATUS = 'CLOSED'
                        AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                    END
                ) AS "CLOSED_NON_ROUTINE_TC",
                COUNT(
                    CASE
                        WHEN WOTC.TASK_CARD LIKE 'NR-%'
                        AND WOTC.STATUS = 'CANCEL'
                        AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                    END
                ) AS "CANCEL_NON_ROUTINE_TC",
                SUM(
                    CASE
                        WHEN WOTC.STATUS IN ('CLOSED', 'CANCEL')
                        AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN 1
                        ELSE 0
                    END
                ) AS "COMPLETED_OR_CANCELED_TC"
            FROM
                ODB.WO_TASK_CARD WOTC
            WHERE
                WOTC.WO IN (
                    SELECT
                        WO
                    FROM
                        WO_INFO
                )
            GROUP BY
                WOTC.WO
        ),
        MAN_HOURS AS (
            SELECT
                WOTC.WO,
                NVL (
                    SUM(
                        CASE
                            WHEN VCT.MAN_HOURS <> 0 THEN VCT.MAN_HOURS
                        END
                    ),
                    0
                ) AS "TOTAL_MH",
                NVL (
                    SUM(
                        CASE
                            WHEN WOTC.STATUS <> 'OPEN'
                            AND VCT.MAN_HOURS <> 0 THEN VCT.MAN_HOURS
                        END
                    ),
                    0
                ) AS "TOTAL_MH_COMPLETED"
            FROM
                ODB.WO_TASK_CARD WOTC
                INNER JOIN ODB.VENDOR_CONTRACT_TASK VCT ON WOTC.TASK_CARD = VCT.TASK_CARD
            WHERE
                WOTC.WO IN (
                    SELECT
                        WO
                    FROM
                        WO_INFO
                )
            GROUP BY
                WOTC.WO
        ),
        LATEST_FLIGHT AS (
            SELECT
                AF.AC,
                AF.FLIGHT,
                AF.ORIGIN,
                AF.DESTINATION,
                AF.FLIGHT_DATE,
                AF.ON_HOUR,
                AF.ON_MINUTE
            FROM
                ODB.AC_ACTUAL_FLIGHTS AF
            WHERE
                (AF.AC, AF.CREATED_DATE) IN (
                    SELECT
                        AC,
                        MAX(CREATED_DATE)
                    FROM
                        ODB.AC_ACTUAL_FLIGHTS
                    WHERE
                        AC IN (
                            SELECT
                                AC
                            FROM
                                WO_INFO
                        )
                    GROUP BY
                        AC
                )
        )
    SELECT
        WO_INFO.WO,
        WO_INFO.AC,
        WO_INIT.AC_TYPE,
        WO_INFO.EVENT_ID,
        WO_INIT.LOCATION,
        WO_INIT.WO_DESCRIPTION,
        WO_INIT.VENDOR,
        WO_INIT.SITE,
        WO_INIT.PROJECT,
        WO_INIT.WO_CATEGORY,
        WO_INIT.PRIORITY,
        WO_INIT.EXTERNAL_REFERENCE,
        WO_INIT.STATUS,
        TO_DATE (
            WO_INFO.ACTUAL_COMPLETION_DATETIME,
            'YYYY-MM-DD HH24:MI:SS'
        ) - TO_DATE (
            WO_INFO.ACTUAL_START_DATETIME,
            'YYYY-MM-DD HH24:MI:SS'
        ) AS "DURATION",
        CASE
            WHEN TC.TOTAL_TC = 0
            OR TC.COMPLETED_OR_CANCELED_TC = 0 THEN 0
            WHEN WO_INFO.SCHEDULE_START_DATE > SYSDATE THEN 0
            ELSE ROUND(TC.COMPLETED_OR_CANCELED_TC / TC.TOTAL_TC * 100)
        END AS "PERCENT_COMPLETED",
        TC.TOTAL_TC,
        TC.TOTAL_CANCEL_TC,
        TC.TOTAL_ROUTINE_TC,
        TC.OPEN_ROUTINE_TC,
        TC.CLOSED_ROUTINE_TC,
        TC.CANCEL_ROUTINE_TC,
        TC.TOTAL_NON_ROUTINE_TC,
        TC.OPEN_NON_ROUTINE_TC,
        TC.CLOSED_NON_ROUTINE_TC,
        TC.CANCEL_NON_ROUTINE_TC,
        MH.TOTAL_MH,
        MH.TOTAL_MH_COMPLETED,
        CASE
            WHEN MH.TOTAL_MH = 0 THEN 0
            ELSE ROUND(MH.TOTAL_MH_COMPLETED / MH.TOTAL_MH * 100)
        END AS "MH_ROUTINE_CHECK_COMPLETION",
        AC.AC_TYPE AS "TYPE",
        AC.AC_SN,
        AC.AC_FLIGHT_HOURS,
        AC.AC_FLIGHT_MINUTES,
        AC.AC_CYCLES,
        AC.LAST_AC_REGISTRATION,
        SUBSTR (AC.BASIC_NUMBER, 1, 2) AS "OPERATOR",
        LF.FLIGHT,
        LF.ORIGIN,
        LF.DESTINATION,
        LF.FLIGHT_DATE,
        LF.ON_HOUR,
        LF.ON_MINUTE,
        WO_INIT.SCHEDULE_START_DATE,
        WO_INIT.SCHEDULE_START_HOUR,
        WO_INIT.SCHEDULE_START_MINUTE,
        WO_INIT.SCHEDULE_COMPLETION_DATE,
        WO_INIT.SCHEDULE_COMPLETION_HOUR,
        WO_INIT.SCHEDULE_COMPLETION_MINUTE,
        WO_INIT.SCHEDULE_ORG_COMPLETION_DATE,
        WO_INIT.SCHEDULE_ORG_COMPLETION_HOUR,
        WO_INIT.SCHEDULE_ORG_COMPLETION_MINUTE,
        WO_INIT.ACTUAL_START_DATE,
        WO_INIT.ACTUAL_START_HOUR,
        WO_INIT.ACTUAL_START_MINUTE,
        WO_INIT.CREATED_DATE,
        WO_INIT.CREATED_BY,
        WO_INIT.MODIFIED_DATE,
        WO_INIT.MODIFIED_BY,
        LM.TIME_ZONE_NAME
    FROM
        WO_INFO WO_INFO
        LEFT JOIN WO_INIT WO_INIT ON WO_INFO.WO = WO_INIT.WO
        LEFT JOIN TASK_COUNTS TC ON WO_INFO.WO = TC.WO
        LEFT JOIN MAN_HOURS MH ON WO_INFO.WO = MH.WO
        LEFT JOIN ODB.AC_MASTER AC ON WO_INFO.AC = AC.AC
        LEFT JOIN LATEST_FLIGHT LF ON WO_INFO.AC = LF.AC
        LEFT JOIN ODB.LOCATION_MASTER LM ON LM.LOCATION = WO_INIT.LOCATION
    ORDER BY
        WO_INFO.SCHEDULE_START_DATE
  `);

  // Extract the rows from the raw query result
  const workorderDataRows = workorderData.rows || workorderData;

  // Format date columns to ISO format for CSV export
  const formatDateColumns = (data) => {
    const dateColumns = [
      "SCHEDULE_START_DATE",
      "SCHEDULE_COMPLETION_DATE",
      "SCHEDULE_ORG_COMPLETION_DATE",
      "ACTUAL_START_DATE",
      "CREATED_DATE",
      "MODIFIED_DATE",
      "FLIGHT_DATE",
    ];

    return data.map((row) => {
      const formattedRow = { ...row };
      dateColumns.forEach((column) => {
        if (formattedRow[column] && formattedRow[column] instanceof Date) {
          formattedRow[column] = formattedRow[column].toISOString();
        }
      });
      return formattedRow;
    });
  };

  const formattedData = formatDateColumns(workorderDataRows);

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      formattedData.length
    } workorder records`
  );
  return formattedData;
};

const getPoData = async () => {
  const startTime = Date.now();

  const queryBuilder = await dbConnector.getQueryBuilder();
  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  const poData = await queryBuilder
    .select([
      "WO.WO",
      "VC.CURRENCY as CURRENCY_CODE",
      "RV.RELATION_CODE as SUPPLIER_CODE",
      "RV.MAIL_ADDRESS_1 as SUPPLIER_ADDRESS1",
      "RV.MAIL_ADDRESS_2 as SUPPLIER_ADDRESS2",
      "RV.MAIL_CITY as SUPPLIER_CITY",
      "RV.MAIL_POST as SUPPLIER_POST",
      "RV.MAIL_STATE as SUPPLIER_STATE",
      "RV.MAIL_COUNTRY as SUPPLIER_COUNTRY",
      "RV.MAIL_PHONE as SUPPLIER_PHONE",
      "RV.MAIL_FAX as SUPPLIER_FAX",
      "RV.MAIL_CELL as SUPPLIER_CELL",
      "RV.MAIL_EMAIL as SUPPLIER_EMAIL",
      queryBuilder.raw(
        `SUBSTR(NVL(RV.NAME,' - NO SUPPLIER - '),1,40) as "SUPPLIER"`
      ),
    ])
    .from("ODB.WO as WO")
    .leftJoin("ODB.WO_VENDOR_CONTRACT as WVC", "WO.WO", "=", "WVC.WO")
    .leftJoin("ODB.VENDOR_CONTRACT as VC", function () {
      this.on("WO.VENDOR", "=", "VC.VENDOR")
        .andOn("WO.LOCATION", "=", "VC.LOCATION")
        .andOn("WVC.CONTRACT_TYPE", "=", "VC.CONTRACT_TYPE");
    })
    .leftJoin("ODB.RELATION_MASTER as RV", function () {
      this.on("WO.VENDOR", "=", "RV.RELATION_CODE").andOn(
        queryBuilder.raw("NVL(RV.RELATION_TRANSACTION, 'VENDOR')"),
        "=",
        queryBuilder.raw("'VENDOR'")
      );
    })
    .whereRaw(
      `SUBSTR(WO.LOCATION,4,4) IN ('1','2','3','4','5','6','7','8','9')`
    )
    .whereIn("WO.STATUS", ["COMPLETED", "POSTCOMPLT", "OPEN", "CLOSED"])
    .where(function () {
      this.where("WO.CREATED_BY", "TRAXIFACE").orWhere("WO.PROJECT", "EMS");
    })
    .whereRaw(
      `WO.SCHEDULE_COMPLETION_DATE BETWEEN SYSDATE - 365 AND SYSDATE + 730`
    );

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      poData.length
    } PO records`
  );
  return poData;
};

const getTaskCardData = async () => {
  const startTime = Date.now();

  const queryBuilder = await dbConnector.getQueryBuilder();
  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  const taskCardData = await queryBuilder.raw(`
    WITH WO_INFO AS (
        SELECT
            WO.WO,
            CASE 
                WHEN PROJECT = 'EMS' THEN WO.WO || '_' || WO.AC 
                ELSE TRIM(SUBSTR(WO.EXTERNAL_REFERENCE, 1, 10))
            END AS EVENT_ID,
            WO.SCHEDULE_START_DATE,
            WO.SCHEDULE_START_DATE AS ACTUAL_START_DATE,
            WO.SCHEDULE_COMPLETION_DATE
        FROM ODB.WO
        WHERE SUBSTR(WO.LOCATION, 4, 4) IN ('1', '2', '3', '4', '5', '6', '7', '8', '9')
            AND (WO.CREATED_BY = 'TRAXIFACE' OR WO.PROJECT = 'EMS')
            AND WO.STATUS IN ('POSTCOMPLT', 'OPEN')
            AND WO.SCHEDULE_COMPLETION_DATE BETWEEN SYSDATE - 365 AND SYSDATE + 730
    ),
    TC_INFO AS (
        SELECT 
            WOTC.WO,
            WOTC.TASK_CARD,
            CASE 
                WHEN PID.INSTALLED_POSITION = 'ONLY' THEN SUBSTR(PN.PN_DESCRIPTION, 1, 3) 
                ELSE PID.INSTALLED_POSITION 
            END AS POSITION,
            WOTC.TASK_CARD_DESCRIPTION,
            WOTC.STATUS,
            WOTC.TASK_CARD_NUMBERING_SYSTEM,
            WOTC.REVISION,
            WOTC.PN,
            WOTC.PN_SN,
            TCI.SKILL
        FROM WO_INFO WO
            LEFT JOIN ODB.WO_TASK_CARD WOTC ON WO.WO = WOTC.WO
            LEFT JOIN ODB.PN_INVENTORY_DETAIL PID ON WOTC.PN = PID.PN AND WOTC.PN_SN = PID.SN
            LEFT JOIN ODB.PN_MASTER PN ON PID.PN = PN.PN,
            ODB.TASK_CARD_ITEM TCI
        WHERE WOTC.TASK_CARD = TCI.TASK_CARD
            AND TCI.TASK_CARD_ITEM = 1

        UNION

        SELECT 
            WOTC.WO,
            WOTC.TASK_CARD,
            NULL AS POSITION,
            WOTC.TASK_CARD_DESCRIPTION,
            WOTC.STATUS,
            WOTC.TASK_CARD_NUMBERING_SYSTEM,
            WOTC.REVISION,
            WOTC.PN,
            WOTC.PN_SN,
            NULL AS SKILL
        FROM WO_INFO WO
            LEFT JOIN ODB.WO_TASK_CARD WOTC ON WO.WO = WOTC.WO
        WHERE WOTC.TASK_CARD LIKE 'NR%'
        ORDER BY 6
    )
    SELECT 
        WO.WO,
        WO.EVENT_ID,
        'T35' AS PERIOD,
        WTC.TASK_CARD,
        TRIM(WTC.PN) AS PN,
        TRIM(WTC.PN_SN) AS PN_SN,
        WO.EVENT_ID || '-T35' AS PID,
        WO.EVENT_ID || '-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS TID,
        WO.EVENT_ID || '-T35-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS UNIQUE_KEY
    FROM TC_INFO WTC
        INNER JOIN WO_INFO WO ON WTC.WO = WO.WO
    WHERE TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 35, 'YYYY-MM-DD')

    UNION ALL

    SELECT 
        WO.WO,
        WO.EVENT_ID,
        'T10' AS PERIOD,
        WTC.TASK_CARD,
        TRIM(WTC.PN) AS PN,
        TRIM(WTC.PN_SN) AS PN_SN,
        WO.EVENT_ID || '-T10' AS PID,
        WO.EVENT_ID || '-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS TID,
        WO.EVENT_ID || '-T10-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS UNIQUE_KEY
    FROM TC_INFO WTC
        INNER JOIN WO_INFO WO ON WTC.WO = WO.WO
    WHERE TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 35, 'YYYY-MM-DD')
        OR (TO_CHAR(SYSDATE, 'YYYY-MM-DD') > TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 35, 'YYYY-MM-DD') 
            AND TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 10, 'YYYY-MM-DD'))

    UNION ALL

    SELECT 
        WO.WO,
        WO.EVENT_ID,
        'T00' AS PERIOD,
        WTC.TASK_CARD,
        TRIM(WTC.PN) AS PN,
        TRIM(WTC.PN_SN) AS PN_SN,
        WO.EVENT_ID || '-T00' AS PID,
        WO.EVENT_ID || '-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS TID,
        WO.EVENT_ID || '-T00-' || WTC.TASK_CARD || '-' || TRIM(WTC.PN) || '-' || TRIM(WTC.PN_SN) AS UNIQUE_KEY
    FROM TC_INFO WTC
        INNER JOIN WO_INFO WO ON WTC.WO = WO.WO
    WHERE TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 35, 'YYYY-MM-DD')
        OR (TO_CHAR(SYSDATE, 'YYYY-MM-DD') > TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 35, 'YYYY-MM-DD') 
            AND TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 10, 'YYYY-MM-DD'))
        OR (TO_CHAR(SYSDATE, 'YYYY-MM-DD') > TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE) - 10, 'YYYY-MM-DD') 
            AND TO_CHAR(SYSDATE, 'YYYY-MM-DD') <= TO_CHAR(NVL(WO.ACTUAL_START_DATE, WO.SCHEDULE_START_DATE), 'YYYY-MM-DD'))
    ORDER BY UNIQUE_KEY
  `);

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      taskCardData.length
    } task card records`
  );
  return taskCardData;
};

const getEnginesData = async () => {
  const startTime = Date.now();

  const queryBuilder = await dbConnector.getQueryBuilder();
  LOGGER.info(`Database connection established in ${Date.now() - startTime}ms`);

  const engineData = await queryBuilder.raw(`
    WITH
        AC_INFO AS (
            SELECT
                AC.AC,
                AC_SN AS MSN,
                AC_TYPE,
                AC_SERIES,
                PM.ENGINE,
                PM.PN_DESCRIPTION,
                PID.PN,
                PI.PN AS MASTER_PN,
                PID.SN,
                NHA_PN AS TOP_PN,
                INSTALLED_AC,
                INSTALLED_POSITION,
                NHA_SN AS ALIAS,
                INSTALLED_DATE,
                PRORATED_FLAG,
                GOODS_RCVD_BATCH AS GRB
            FROM
                ODB.AC_MASTER AC
                INNER JOIN ODB.PN_INVENTORY_DETAIL PID ON AC.AC = PID.INSTALLED_AC
                INNER JOIN ODB.PN_INTERCHANGEABLE PI ON PID.PN = PI.PN_INTERCHANGEABLE
                INNER JOIN ODB.PN_MASTER PM ON PI.PN = PM.PN
            WHERE
                (
                    (
                        PM.ENGINE IN ('ENGINE')
                        AND CHAPTER = '72'
                    )
                    OR (
                        PM.ENGINE = 'APU'
                        AND CHAPTER = '49'
                        AND NHA_PN IS NOT NULL
                    )
                )
        ),
        PITA_INFO AS (
            SELECT
                GOODS_RCVD_BATCH AS GRB,
                SUM(NVL (HOURS, 0)) * 60 + SUM(NVL (MINUTES, 0)) AS ACCRUAL_TOT_M,
                SUM(NVL (CYCLES, 0)) AS ACCRUAL_TOT_C,
                SUM(NVL (DAYS, 0)) AS ACCRUAL_TOT_D
            FROM
                ODB.PN_INVENTORY_TIMES_ACCRUAL A
                INNER JOIN AC_INFO P ON P.GRB = A.GOODS_RCVD_BATCH
            GROUP BY
                GOODS_RCVD_BATCH
        ),
        APTH_INFO AS (
            SELECT
                GOODS_RCVD_BATCH AS GRB,
                SUM(NVL (HOURS_INSTALLED, 0)) * 60 + SUM(NVL (MINUTES_INSTALLED, 0)) AS RMV_TOT_M,
                SUM(NVL (CYCLES_INSTALLED, 0)) AS RMV_TOT_C,
                SUM(NVL (DAYS_INSTALLED, 0)) AS RMV_TOT_D
            FROM
                ODB.AC_PN_TRANSACTION_HISTORY A
                INNER JOIN AC_INFO P ON P.GRB = A.GOODS_RCVD_BATCH
            WHERE
                TRANSACTION_TYPE = 'REMOVE'
            GROUP BY
                GOODS_RCVD_BATCH
        ),
        FLTS_INFO AS (
            SELECT
                P.PN,
                P.SN,
                P.PRORATED_FLAG,
                CASE NVL (FACTOR_CONTROL, 0)
                    WHEN 0 THEN 1
                    ELSE FACTOR_CONTROL
                END AS FACT_CTRL,
                CASE NVL (PMF.FACTOR_CONTROL_CYCLES, 0)
                    WHEN 0 THEN 1
                    ELSE PMF.FACTOR_CONTROL_CYCLES
                END AS FACT_CTRL_CYC,
                P.INSTALLED_AC,
                P.INSTALLED_DATE,
                FLIGHT_HOURS * 60 + FLIGHT_MINUTES AS FLY_M,
                CYCLES AS FLY_C,
                OFF_DATETIME
            FROM
                ODB.AC_ACTUAL_FLIGHTS F
                INNER JOIN AC_INFO P ON F.AC = P.AC
                LEFT JOIN ODB.PN_MASTER_FACTOR PMF ON P.TOP_PN = PMF.PN
                AND P.AC_SERIES = PMF.AC_SERIES
                AND P.AC_TYPE = PMF.AC_TYPE
            WHERE
                CYCLES > 0
                AND OFF_DATETIME >= P.INSTALLED_DATE
        ),
        TSI_INFO AS (
            SELECT
                PN,
                SN,
                INSTALLED_AC,
                FACT_CTRL,
                FACT_CTRL_CYC,
                SUM(FACT_CTRL * FLY_M) AS TSI_M,
                SUM(FACT_CTRL_CYC * FLY_C) AS TSI_C
            FROM
                FLTS_INFO
            GROUP BY
                PN,
                SN,
                INSTALLED_AC,
                FACT_CTRL,
                FACT_CTRL_CYC
        )
    SELECT
        AC.*,
        TRUNC (
            (
                NVL (ACCRUAL_TOT_M, 0) + NVL (RMV_TOT_M, 0) + NVL (TSI_M, 0)
            ) / 60
        ) AS HSN,
        TRUNC (
            NVL (ACCRUAL_TOT_C, 0) + NVL (RMV_TOT_C, 0) + NVL (TSI_C, 0)
        ) AS CSN
    FROM
        AC_INFO AC
        LEFT JOIN PITA_INFO PITA ON AC.GRB = PITA.GRB
        LEFT JOIN APTH_INFO APTH ON AC.GRB = APTH.GRB
        LEFT JOIN TSI_INFO TSI ON AC.AC = TSI.INSTALLED_AC
        AND AC.PN = TSI.PN
        AND AC.SN = TSI.SN
    ORDER BY
        AC,
        ENGINE,
        INSTALLED_POSITION
  `);

  LOGGER.info(
    `Query completed in ${Date.now() - startTime}ms. Retrieved ${
      engineData.length
    } engine data records`
  );
  return engineData;
};

module.exports = {
  getVendorData,
  getWorkorderData,
  getPoData,
  getTaskCardData,
  getEnginesData,
};
