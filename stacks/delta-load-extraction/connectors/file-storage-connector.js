const { s3Adaptor } = require("../adaptors");
const { dateUtil } = require("../utils");

const DISTRIBUTION_BUCKET_NAME = process.env.DISTRIBUTION_BUCKET_NAME;
const VENDOR_GENERATING_FILE_PATH = process.env.VENDOR_GENERATING_FILE_PATH;
const FILE_PREFIX_VENDOR = process.env.FILE_PREFIX_VENDOR;
const WO_GENERATING_FILE_PATH = process.env.WO_GENERATING_FILE_PATH;
const FILE_PREFIX_WO = process.env.FILE_PREFIX_WO;
const PO_GENERATING_FILE_PATH = process.env.PO_GENERATING_FILE_PATH;
const FILE_PREFIX_PO = process.env.FILE_PREFIX_PO;
const PENDING_FILE_GENERATING_PATH = process.env.PENDING_FILE_GENERATING_PATH;
const TASK_CARD_GENERATING_FILE_PATH = process.env.TASK_CARD_GENERATING_FILE_PATH;
const FILE_PREFIX_TASK_CARD = process.env.FILE_PREFIX_TASK_CARD;
const FILE_PREFIX_ENGINE_DATA = process.env.FILE_PREFIX_ENGINE_DATA;
const ENGINE_DATA_GENERATING_FILE_PATH = process.env.ENGINE_DATA_GENERATING_FILE_PATH;

const putVendorFile = async (fileContent) => {
  const now = new Date();
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, "");
  const timeStr = now.toISOString().slice(11, 19).replace(/:/g, "");
  const fileName = `${FILE_PREFIX_VENDOR}_${dateStr}_${timeStr}.csv`;

  const key =  VENDOR_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

const putWOFile = async (fileContent) => {
  const now = new Date();
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, "");
  const timeStr = now.toISOString().slice(11, 19).replace(/:/g, "");
  const fileName = `${FILE_PREFIX_WO}_${dateStr}_${timeStr}.csv`;
  const key = WO_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

const putPOFile = async (fileContent) => {
  const dateTimeString = dateUtil.getDateTimeString();
  const fileName = `${FILE_PREFIX_PO}_${dateTimeString}.csv`;

  const key = PO_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

const putTaskCardFile = async (fileContent) => {
  const dateTimeString = dateUtil.getDateTimeString();
  const fileName = `${FILE_PREFIX_TASK_CARD}_${dateTimeString}.csv`;

  const key = TASK_CARD_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};

const putEngineDataFile = async (fileContent) => {
  const dateTimeString = dateUtil.getDateTimeString();
  const fileName = `${FILE_PREFIX_ENGINE_DATA}_${dateTimeString}.csv`;

  const key = ENGINE_DATA_GENERATING_FILE_PATH + PENDING_FILE_GENERATING_PATH + fileName;

  const params = {
    Bucket: DISTRIBUTION_BUCKET_NAME,
    Key: key,
    Body: fileContent,
    ContentType: "text/csv",
  };
  await s3Adaptor.putObject(params);
  return params;
};


module.exports = {
  putVendorFile,
  putWOFile,
  putPOFile,
  putTaskCardFile,
  putEngineDataFile
};
