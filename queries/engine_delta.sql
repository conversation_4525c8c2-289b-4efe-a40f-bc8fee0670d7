WITH
    AC_INFO AS (
        SELECT
            AC.AC,
            AC_SN AS MSN,
            AC_TYPE,
            AC_SERIES,
            PM.ENGINE,
            PM.PN_DESCRIPTION,
            PID.PN,
            PI.PN AS MASTER_PN,
            PID.SN,
            NHA_PN AS TOP_PN,
            INSTALLED_AC,
            INSTALLED_POSITION,
            NHA_SN AS ALIAS,
            INSTALLED_DATE,
            PRORATED_FLAG,
            GOODS_RCVD_BATCH AS GRB
        FROM
            ODB.AC_MASTER AC
            INNER JOIN ODB.PN_INVENTORY_DETAIL PID ON AC.AC = PID.INSTALLED_AC
            INNER JOIN ODB.PN_INTERCHANGEABLE PI ON PID.PN = PI.PN_INTERCHANGEABLE
            INNER JOIN ODB.PN_MASTER PM ON PI.PN = PM.PN
        WHERE
            (
                (
                    PM.ENGINE IN ('ENGINE')
                    AND CHAPTER = '72'
                )
                OR (
                    PM.ENGINE = 'APU'
                    AND CHAPTER = '49'
                    AND NHA_PN IS NOT NULL
                )
            )
    ),
    PITA_INFO AS (
        SELECT
            GOODS_RCVD_BATCH AS GRB,
            SUM(NVL (HOURS, 0)) * 60 + SUM(NVL (MINUTES, 0)) AS ACCRUAL_TOT_M,
            SUM(NVL (CYCLES, 0)) AS ACCRUAL_TOT_C,
            SUM(NVL (DAYS, 0)) AS ACCRUAL_TOT_D
        FROM
            ODB.PN_INVENTORY_TIMES_ACCRUAL A
            INNER JOIN AC_INFO P ON P.GRB = A.GOODS_RCVD_BATCH
        GROUP BY
            GOODS_RCVD_BATCH
    ),
    APTH_INFO AS (
        SELECT
            GOODS_RCVD_BATCH AS GRB,
            SUM(NVL (HOURS_INSTALLED, 0)) * 60 + SUM(NVL (MINUTES_INSTALLED, 0)) AS RMV_TOT_M,
            SUM(NVL (CYCLES_INSTALLED, 0)) AS RMV_TOT_C,
            SUM(NVL (DAYS_INSTALLED, 0)) AS RMV_TOT_D
        FROM
            ODB.AC_PN_TRANSACTION_HISTORY A
            INNER JOIN AC_INFO P ON P.GRB = A.GOODS_RCVD_BATCH
        WHERE
            TRANSACTION_TYPE = 'REMOVE'
        GROUP BY
            GOODS_RCVD_BATCH
    ),
    FLTS_INFO AS (
        SELECT
            P.PN,
            P.SN,
            P.PRORATED_FLAG,
            CASE NVL (FACTOR_CONTROL, 0)
                WHEN 0 THEN 1
                ELSE FACTOR_CONTROL
            END AS FACT_CTRL,
            CASE NVL (PMF.FACTOR_CONTROL_CYCLES, 0)
                WHEN 0 THEN 1
                ELSE PMF.FACTOR_CONTROL_CYCLES
            END AS FACT_CTRL_CYC,
            P.INSTALLED_AC,
            P.INSTALLED_DATE,
            FLIGHT_HOURS * 60 + FLIGHT_MINUTES AS FLY_M,
            CYCLES AS FLY_C,
            OFF_DATETIME
        FROM
            ODB.AC_ACTUAL_FLIGHTS F
            INNER JOIN AC_INFO P ON F.AC = P.AC
            LEFT JOIN ODB.PN_MASTER_FACTOR PMF ON P.TOP_PN = PMF.PN
            AND P.AC_SERIES = PMF.AC_SERIES
            AND P.AC_TYPE = PMF.AC_TYPE
        WHERE
            CYCLES > 0
            AND OFF_DATETIME >= P.INSTALLED_DATE
    ),
    TSI_INFO AS (
        SELECT
            PN,
            SN,
            INSTALLED_AC,
            FACT_CTRL,
            FACT_CTRL_CYC,
            SUM(FACT_CTRL * FLY_M) AS TSI_M,
            SUM(FACT_CTRL_CYC * FLY_C) AS TSI_C
        FROM
            FLTS_INFO
        GROUP BY
            PN,
            SN,
            INSTALLED_AC,
            FACT_CTRL,
            FACT_CTRL_CYC
    )
SELECT
    AC.*,
    TRUNC (
        (
            NVL (ACCRUAL_TOT_M, 0) + NVL (RMV_TOT_M, 0) + NVL (TSI_M, 0)
        ) / 60
    ) AS HSN,
    TRUNC (
        NVL (ACCRUAL_TOT_C, 0) + NVL (RMV_TOT_C, 0) + NVL (TSI_C, 0)
    ) AS CSN
FROM
    AC_INFO AC
    LEFT JOIN PITA_INFO PITA ON AC.GRB = PITA.GRB
    LEFT JOIN APTH_INFO APTH ON AC.GRB = APTH.GRB
    LEFT JOIN TSI_INFO TSI ON AC.AC = TSI.INSTALLED_AC
    AND AC.PN = TSI.PN
    AND AC.SN = TSI.SN
ORDER BY
    AC,
    ENGINE,
    INSTALLED_POSITION;