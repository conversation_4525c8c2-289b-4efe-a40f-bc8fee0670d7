{"name": "ac-odh-batch-distribution-webfocus", "version": "1.0.0", "description": "This repo will get data from Trax and generate a CSV file for the web focus", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/AC-IT-Development/ac-odh-batch-distribution-webfocus.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/AC-IT-Development/ac-odh-batch-distribution-webfocus/issues"}, "homepage": "https://github.com/AC-IT-Development/ac-odh-batch-distribution-webfocus#readme", "dependencies": {"@aws-sdk/client-s3": "^3.515.0", "dbaas-logger": "git+ssh://**************/AC-IT-Development/dbaas-logger.git#vstable_4.0.7", "ac-utils": "git+ssh://**************/AC-IT-Development/ac-utils.git#2.0.0", "@aws-sdk/client-secrets-manager": "^3.342.0", "@aws-sdk/client-sns": "^3.848.0", "oracledb": "^6.8.0", "knex": "3.1.0", "fast-csv": "^5.0.2"}, "devDependencies": {"serverless-glue": "^2.9.0", "serverless-step-functions": "^3.18.0"}}