service: ac-odh-batch-distribution-webfocus

custom:
  tags:
    service: ac-odh-batch-distribution-webfocus
    Criticality: Major
    Sensitivity: High
    BusinessImpact: High
    Observability: Yes
    SharedResource: No
    RecoveryTimeObjective: 60 mins
    RecoveryPointObjective: 15 mins
    Type: Operations
    # TODO: Need to add more tags
  prune:
    automatic: true
    number: 1

  params:
    intca1:
      DISTRIBUTION_BUCKET_NAME: "ac-odh-batch-distribution-intca1"
      FILE_PREFIX_WO: "TRAX_WO_INT"
      FILE_PREFIX_VENDOR: "TRAX_VENDOR_INT"
      FILE_PREFIX_PO: "TRAX_PO_INT"
      FILE_PREFIX_TASK_CARD: "TRAX_TCC_INT"
      FILE_PREFIX_ENGINE_DATA: "TRAX_Engines_INT"
      START_DATE: "20170101"
      END_DATE: "20250721"
      GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: 3
      GLUE_JOB_CONFIG_WORKERTYPE: G.1X
      FLOWN_GLUE_JOB_CONFIG_NUMBEROFWORKERS: 10
      GLUE_JOB_CONFIG_NUMBEROFWORKERS: 2
      GLUE_JOB_CONFIG_TIMEOUT: 2880
      GLUE_JOB_CONFIG_MAXRETRIES: 2
      ENABLE_GLUE_CLOUDWATCH_LOGS: true
      GLUE_SPARK_UI_LOGS_PATH: "s3://ac-odh-glue-jobs-scripts-common-intca1/spark-ui/"
      GLUE_UTILS_PATH: "s3://ac-odh-common-glue-utils-intca1/aws-glue-utils/v1.1.0/glue_utils.zip"
      TRAX_DB_SECRET: intca1/trax-external-db/db-credentials
      RDS_CONNECTION_SUBNET_ID_A: subnet-07612cb342a5be91d
      RDS_CONNECTION_SUBNET_ID_B: subnet-01644fc5d5a72b235
      RDS_CONNECTION_SUBNET_ID_D: subnet-0656557afe0f2bc5e
      LAMBDA_TIMEOUT: 900
      LAMBDA_MEMORY: 2048
      LAMBDA_RESERVED_CONCURRENCY: 1
      WEBFOCUS_S3_BUCKET: "ac-maintenance-ops-webfocus-int-files"
      VENDOR_GENERATING_FILE_PATH: "WEB_FOCUS/VENDOR/"
      VENDOR_UPLOADING_FILE_PATH: "WEB_FOCUS/VENDOR/"
      WO_GENERATING_FILE_PATH: "WEB_FOCUS/WO/"
      WO_UPLOADING_FILE_PATH: "WEB_FOCUS/WO/"
      PO_GENERATING_FILE_PATH: "WEB_FOCUS/PO/"
      PO_UPLOADING_FILE_PATH: "WEB_FOCUS/PO/"
      TASK_CARD_GENERATING_FILE_PATH: "WEB_FOCUS/TCC/"
      TASK_CARD_UPLOADING_FILE_PATH: "WEB_FOCUS/TCC/"
      ENGINE_DATA_GENERATING_FILE_PATH: "WEB_FOCUS/Engines/"
      ENGINE_DATA_UPLOADING_FILE_PATH: "WEB_FOCUS/Engines/"
      PENDING_FILE_GENERATING_PATH: "PENDING/"
      PROCESSED_FILE_GENERATING_PATH: "PROCESSED/"
      STEP_FUNCTION_RATE: rate(3 hours)
      ERROR_NOTIFY_EMAIL: <EMAIL>
      KNEX_DEBUG: true
      #dbaas logs
      DEBUG_MODE: false
      DEFAULT_LANG: en
      LOG_STREAM_NAME: SLSInfo
      LAMBDA_LOG_MODE: debug
      LAYER_DEBUG_MODE: false
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-intca1-default
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: intca1
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      CW_PERMISSION: Allow
      LOG_RETENTION_IN_DAYS: 7

      DB_CONNECTION_RETRY_ATTEMPTS: 3
      DB_CONNECTION_MIN_POOL_SIZE: 1
      DB_CONNECTION_MAX_POOL_SIZE: 3

    batca1:
      DISTRIBUTION_BUCKET_NAME: "ac-odh-batch-distribution-batca1"
      FILE_PREFIX_VENDOR: "TRAX_VENDOR_BAT"
      FILE_PREFIX_WO: "TRAX_WO_BAT"
      FILE_PREFIX_PO: "TRAX_PO_BAT"
      FILE_PREFIX_TASK_CARD: "TRAX_TCC_BAT"
      FILE_PREFIX_ENGINE_DATA: "TRAX_Engines_BAT"
      START_DATE: "20170101"
      END_DATE: "20250721"
      GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: 3
      GLUE_JOB_CONFIG_WORKERTYPE: G.1X
      FLOWN_GLUE_JOB_CONFIG_NUMBEROFWORKERS: 10
      GLUE_JOB_CONFIG_NUMBEROFWORKERS: 2
      GLUE_JOB_CONFIG_TIMEOUT: 2880
      GLUE_JOB_CONFIG_MAXRETRIES: 2
      ENABLE_GLUE_CLOUDWATCH_LOGS: true
      GLUE_SPARK_UI_LOGS_PATH: "s3://ac-odh-glue-jobs-scripts-common-batca1/spark-ui/"
      GLUE_UTILS_PATH: "s3://ac-odh-common-glue-utils-batca1/aws-glue-utils/v1.1.0/glue_utils.zip"
      TRAX_DB_SECRET: batca1/trax-external-db/db-credentials
      RDS_CONNECTION_SUBNET_ID_A: subnet-093ec232ccaaf9236
      RDS_CONNECTION_SUBNET_ID_B: subnet-0e040f56cf4be65bc
      RDS_CONNECTION_SUBNET_ID_D: subnet-0fd7784cf1d74d946
      LAMBDA_TIMEOUT: 900
      LAMBDA_MEMORY: 2048
      LAMBDA_RESERVED_CONCURRENCY: 1
      WEBFOCUS_S3_BUCKET: "ac-maintenance-ops-webfocus-uat-files"
      VENDOR_GENERATING_FILE_PATH: "WEB_FOCUS/VENDOR/"
      VENDOR_UPLOADING_FILE_PATH: "WEB_FOCUS/VENDOR/"
      WO_GENERATING_FILE_PATH: "WEB_FOCUS/WO/"
      WO_UPLOADING_FILE_PATH: "WEB_FOCUS/WO/"
      PO_GENERATING_FILE_PATH: "WEB_FOCUS/PO/"
      PO_UPLOADING_FILE_PATH: "WEB_FOCUS/PO/"
      TASK_CARD_GENERATING_FILE_PATH: "WEB_FOCUS/TCC/"
      TASK_CARD_UPLOADING_FILE_PATH: "WEB_FOCUS/TCC/"
      ENGINE_DATA_GENERATING_FILE_PATH: "WEB_FOCUS/Engines/"
      ENGINE_DATA_UPLOADING_FILE_PATH: "WEB_FOCUS/Engines/"
      PENDING_FILE_GENERATING_PATH: "PENDING/"
      PROCESSED_FILE_GENERATING_PATH: "PROCESSED/"
      STEP_FUNCTION_RATE: rate(3 hours)
      ERROR_NOTIFY_EMAIL: <EMAIL>
      KNEX_DEBUG: true
      #dbaas logs
      DEBUG_MODE: false
      DEFAULT_LANG: en
      LOG_STREAM_NAME: SLSInfo
      LAMBDA_LOG_MODE: debug
      LAYER_DEBUG_MODE: false
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-batca1-default
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: batca1
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      CW_PERMISSION: Allow
      LOG_RETENTION_IN_DAYS: 7

      DB_CONNECTION_RETRY_ATTEMPTS: 3
      DB_CONNECTION_MIN_POOL_SIZE: 1
      DB_CONNECTION_MAX_POOL_SIZE: 3
    
    crtca1:
      DISTRIBUTION_BUCKET_NAME: "ac-odh-batch-distribution-crtca1"
      FILE_PREFIX_VENDOR: "TRAX_VENDOR_CRT"
      FILE_PREFIX_WO: "TRAX_WO_CRT"
      FILE_PREFIX_PO: "TRAX_PO_CRT"
      FILE_PREFIX_TASK_CARD: "TRAX_TCC_CRT"
      FILE_PREFIX_ENGINE_DATA: "TRAX_Engines_CRT"
      START_DATE: "20170101"
      END_DATE: "20250721"
      GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: 3
      GLUE_JOB_CONFIG_WORKERTYPE: G.1X
      FLOWN_GLUE_JOB_CONFIG_NUMBEROFWORKERS: 10
      GLUE_JOB_CONFIG_NUMBEROFWORKERS: 2
      GLUE_JOB_CONFIG_TIMEOUT: 2880
      GLUE_JOB_CONFIG_MAXRETRIES: 2
      ENABLE_GLUE_CLOUDWATCH_LOGS: true
      GLUE_SPARK_UI_LOGS_PATH: "s3://ac-odh-glue-jobs-scripts-common-crtca1/spark-ui/"
      GLUE_UTILS_PATH: "s3://ac-odh-common-glue-utils-crtca1/aws-glue-utils/v1.1.0/glue_utils.zip"
      TRAX_DB_SECRET: crtca1/trax-external-db/db-credentials
      RDS_CONNECTION_SUBNET_ID_A: subnet-0e030f7de3818424f
      RDS_CONNECTION_SUBNET_ID_B: subnet-0f064c984d71076c7
      RDS_CONNECTION_SUBNET_ID_D: subnet-0cfe753e530b47cc8
      LAMBDA_TIMEOUT: 900
      LAMBDA_MEMORY: 2048
      LAMBDA_RESERVED_CONCURRENCY: 1
      WEBFOCUS_S3_BUCKET: "ac-maintenance-ops-webfocus-crt-files"
      VENDOR_GENERATING_FILE_PATH: "WEB_FOCUS/VENDOR/"
      VENDOR_UPLOADING_FILE_PATH: "WEB_FOCUS/VENDOR/"
      WO_GENERATING_FILE_PATH: "WEB_FOCUS/WO/"
      WO_UPLOADING_FILE_PATH: "WEB_FOCUS/WO/"
      PO_GENERATING_FILE_PATH: "WEB_FOCUS/PO/"
      PO_UPLOADING_FILE_PATH: "WEB_FOCUS/PO/"
      TASK_CARD_GENERATING_FILE_PATH: "WEB_FOCUS/TCC/"
      TASK_CARD_UPLOADING_FILE_PATH: "WEB_FOCUS/TCC/"
      ENGINE_DATA_GENERATING_FILE_PATH: "WEB_FOCUS/Engines/"
      ENGINE_DATA_UPLOADING_FILE_PATH: "WEB_FOCUS/Engines/"
      PENDING_FILE_GENERATING_PATH: "PENDING/"
      PROCESSED_FILE_GENERATING_PATH: "PROCESSED/"
      STEP_FUNCTION_RATE: rate(3 hours)
      ERROR_NOTIFY_EMAIL: <EMAIL>
      KNEX_DEBUG: true
      #dbaas logs
      DEBUG_MODE: false
      DEFAULT_LANG: en
      LOG_STREAM_NAME: SLSInfo
      LAMBDA_LOG_MODE: debug
      LAYER_DEBUG_MODE: false
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-crtca1-default
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: crtca1
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      CW_PERMISSION: Allow
      LOG_RETENTION_IN_DAYS: 7

      DB_CONNECTION_RETRY_ATTEMPTS: 3
      DB_CONNECTION_MIN_POOL_SIZE: 1
      DB_CONNECTION_MAX_POOL_SIZE: 3

    preprodca1:
      DISTRIBUTION_BUCKET_NAME: "ac-odh-batch-distribution-preprodca1"
      FILE_PREFIX_VENDOR: "TRAX_VENDOR_PREPROD"
      FILE_PREFIX_WO: "TRAX_WO_PREPROD"
      FILE_PREFIX_PO: "TRAX_PO_PREPROD"
      FILE_PREFIX_TASK_CARD: "TRAX_TCC_PREPROD"
      FILE_PREFIX_ENGINE_DATA: "TRAX_Engines_PREPROD"
      START_DATE: "20170101"
      END_DATE: "20250721"
      GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: 3
      GLUE_JOB_CONFIG_WORKERTYPE: G.1X
      FLOWN_GLUE_JOB_CONFIG_NUMBEROFWORKERS: 10
      GLUE_JOB_CONFIG_NUMBEROFWORKERS: 2
      GLUE_JOB_CONFIG_TIMEOUT: 2880
      GLUE_JOB_CONFIG_MAXRETRIES: 2
      ENABLE_GLUE_CLOUDWATCH_LOGS: true
      GLUE_SPARK_UI_LOGS_PATH: "s3://ac-odh-glue-jobs-scripts-common-preprodca1/spark-ui/"
      GLUE_UTILS_PATH: "s3://ac-odh-common-glue-utils-preprodca1/aws-glue-utils/v1.1.0/glue_utils.zip"
      TRAX_DB_SECRET: preprodca1/trax-external-db/db-credentials
      RDS_CONNECTION_SUBNET_ID_A: subnet-0ce0a9dbac4cfc597
      RDS_CONNECTION_SUBNET_ID_B: subnet-06dd18a1cfb259360
      RDS_CONNECTION_SUBNET_ID_D: subnet-0e01bb22ef83c9ca3
      LAMBDA_TIMEOUT: 900
      LAMBDA_MEMORY: 2048
      LAMBDA_RESERVED_CONCURRENCY: 1
      WEBFOCUS_S3_BUCKET: "ac-maintenance-ops-webfocus-preprod-files"
      VENDOR_GENERATING_FILE_PATH: "WEB_FOCUS/VENDOR/"
      VENDOR_UPLOADING_FILE_PATH: "WEB_FOCUS/VENDOR/"
      WO_GENERATING_FILE_PATH: "WEB_FOCUS/WO/"
      WO_UPLOADING_FILE_PATH: "WEB_FOCUS/WO/"
      PO_GENERATING_FILE_PATH: "WEB_FOCUS/PO/"
      PO_UPLOADING_FILE_PATH: "WEB_FOCUS/PO/"
      TASK_CARD_GENERATING_FILE_PATH: "WEB_FOCUS/TCC/"
      TASK_CARD_UPLOADING_FILE_PATH: "WEB_FOCUS/TCC/"
      ENGINE_DATA_GENERATING_FILE_PATH: "WEB_FOCUS/Engines/"
      ENGINE_DATA_UPLOADING_FILE_PATH: "WEB_FOCUS/Engines/"
      PENDING_FILE_GENERATING_PATH: "PENDING/"
      PROCESSED_FILE_GENERATING_PATH: "PROCESSED/"
      STEP_FUNCTION_RATE: rate(3 hours)
      ERROR_NOTIFY_EMAIL: <EMAIL>
      KNEX_DEBUG: true
      #dbaas logs
      DEBUG_MODE: false
      DEFAULT_LANG: en
      LOG_STREAM_NAME: SLSInfo
      LAMBDA_LOG_MODE: debug
      LAYER_DEBUG_MODE: false
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-preprodca1-default
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: preprodca1
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      CW_PERMISSION: Allow
      LOG_RETENTION_IN_DAYS: 7

      DB_CONNECTION_RETRY_ATTEMPTS: 3
      DB_CONNECTION_MIN_POOL_SIZE: 1
      DB_CONNECTION_MAX_POOL_SIZE: 3

    prodca1:
      DISTRIBUTION_BUCKET_NAME: "ac-odh-batch-distribution-prodca1"
      FILE_PREFIX_VENDOR: "TRAX_VENDOR_PROD"
      FILE_PREFIX_WO: "TRAX_WO_PROD"
      FILE_PREFIX_PO: "TRAX_PO_PROD"
      FILE_PREFIX_TASK_CARD: "TRAX_TCC_PROD"
      FILE_PREFIX_ENGINE_DATA: "TRAX_Engines_PROD"
      START_DATE: "20170101"
      END_DATE: "20250721"
      GLUE_JOB_CONFIG_MAXCONCURRENTRUNS: 3
      GLUE_JOB_CONFIG_WORKERTYPE: G.1X
      FLOWN_GLUE_JOB_CONFIG_NUMBEROFWORKERS: 10
      GLUE_JOB_CONFIG_NUMBEROFWORKERS: 2
      GLUE_JOB_CONFIG_TIMEOUT: 2880
      GLUE_JOB_CONFIG_MAXRETRIES: 2
      ENABLE_GLUE_CLOUDWATCH_LOGS: true
      GLUE_SPARK_UI_LOGS_PATH: "s3://ac-odh-glue-jobs-scripts-common-prodca1/spark-ui/"
      GLUE_UTILS_PATH: "s3://ac-odh-common-glue-utils-prodca1/aws-glue-utils/v1.1.0/glue_utils.zip"
      TRAX_DB_SECRET: prodca1/trax-external-db/db-credentials
      RDS_CONNECTION_SUBNET_ID_A: subnet-0895b4b087142e29f
      RDS_CONNECTION_SUBNET_ID_B: subnet-0e21904f481f055d1
      RDS_CONNECTION_SUBNET_ID_D: subnet-075c6590cab8b8957
      LAMBDA_TIMEOUT: 900
      LAMBDA_MEMORY: 2048
      LAMBDA_RESERVED_CONCURRENCY: 2
      WEBFOCUS_S3_BUCKET: "ac-maintenance-ops-webfocus-prod-files"
      VENDOR_GENERATING_FILE_PATH: "WEB_FOCUS/VENDOR/"
      VENDOR_UPLOADING_FILE_PATH: "WEB_FOCUS/VENDOR/"
      WO_GENERATING_FILE_PATH: "WEB_FOCUS/WO/"
      WO_UPLOADING_FILE_PATH: "WEB_FOCUS/WO/"
      PO_GENERATING_FILE_PATH: "WEB_FOCUS/PO/"
      PO_UPLOADING_FILE_PATH: "WEB_FOCUS/PO/"
      TASK_CARD_GENERATING_FILE_PATH: "WEB_FOCUS/TCC/"
      TASK_CARD_UPLOADING_FILE_PATH: "WEB_FOCUS/TCC/"
      ENGINE_DATA_GENERATING_FILE_PATH: "WEB_FOCUS/Engines/"
      ENGINE_DATA_UPLOADING_FILE_PATH: "WEB_FOCUS/Engines/"
      PENDING_FILE_GENERATING_PATH: "PENDING/"
      PROCESSED_FILE_GENERATING_PATH: "PROCESSED/"
      STEP_FUNCTION_RATE: rate(3 hours)
      ERROR_NOTIFY_EMAIL: <EMAIL>
      KNEX_DEBUG: true
      #dbaas logs
      DEBUG_MODE: false
      DEFAULT_LANG: en
      LOG_STREAM_NAME: SLSInfo
      LAMBDA_LOG_MODE: debug
      LAYER_DEBUG_MODE: false
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-prodca1-default
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: prodca1
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      CW_PERMISSION: Allow
      LOG_RETENTION_IN_DAYS: 7

      DB_CONNECTION_RETRY_ATTEMPTS: 3
      DB_CONNECTION_MIN_POOL_SIZE: 1
      DB_CONNECTION_MAX_POOL_SIZE: 3
