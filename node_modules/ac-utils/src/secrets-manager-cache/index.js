'use strict'

const {SecretsManagerClient,GetSecretValueCommand} = require("@aws-sdk/client-secrets-manager")
const cacheSecretsValue = new Map();

class SecretsCached {
  constructor(secretsValue) {
    this.secretsValue = secretsValue
  }
}
class CacheSecretsManager {
  constructor({region = process.env.AWS_REGION, apiVersion = '2017-10-17'} ={}) {

    this.client = new SecretsManagerClient({
      region,
      apiVersion
    });

  }

  async getSecret(secretName) {
    return this.getSecretsManagerCache(secretName)
  }

  async getSecretsManagerCache(secretName) {
    console.log('[secrets-cache] checking secrets cache');

    const hasCache = cacheSecretsValue.has(secretName);


    if (hasCache) {
      console.log('[secrets-cache] has cache');
      const secret = cacheSecretsValue.get(secretName).secretsValue;
      console.log('[secrets-cache] secretsValue');
      return secret.toString();
    }
    console.log('[secrets-cache] cache not found');

    const command = new GetSecretValueCommand({SecretId: secretName});
    const secretsValue = await this.client.send(command);
    console.log('[secrets-cache] got values from secrets manager');
    //decrypt secrets
    const value = secretsValue.SecretString ?
      secretsValue.SecretString : readBinary(secretsValue.SecretBinary);
    cacheSecretsValue.set(secretName, new SecretsCached(value))
    console.log('[secrets-cache] cache was created');
    return value;
    function readBinary(data) {
      const buff = new Buffer.from(data.SecretBinary, 'base64');
      decodedBinarySecret = buff.toString('ascii');
      return decodedBinarySecret;
    }
  }
}

module.exports = CacheSecretsManager;