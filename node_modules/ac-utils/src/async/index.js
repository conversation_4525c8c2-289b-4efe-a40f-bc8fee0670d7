'use strict'

// Run callback in parallel
const asyncMapRunInParallel = async (array, callback) => {
  const responses = [];
  return await Promise.all(
    array.map(async (item) => responses.push({
      item: item,
      response: await callback(item)
    }))
  )

  return responses;
}

const asyncMapRunInParallelInChunks = async (array, chunk, callback) => {
  const responses = [];
  for (let i = 0; i < array.length; i += chunk) {
    let slice = array.slice(i, i + chunk)
    await Promise.all(
      slice.map(async (item, index) => responses.push({
      item: item,
      response: await callback(item, i + index)
    }))
    )
  }

  return responses;
}

const asyncForEach = async (array, callback) => {
  const responses = [];
  for (let index = 0; index < array.length; index++) {
    responses.push({
      item: array[index],
      response: await callback(array[index], index, array)
    })
  }

  return responses;
}

module.exports = {
  asyncMapRunInParallel,
  asyncMapRunInParallelInChunks,
  asyncForEach
}