'use strict'

const AC_RETRIER_DEBUG_MODE = process.env.AC_RETRIER_DEBUG_MODE == 'true' || false;

const retrier = async ({ callback, attempts = 3, exponentialBackoff = exponentialBackoffFunction, debug = AC_RETRIER_DEBUG_MODE }) => {
  let retry = 0;

  try {
    while (attempts > retry) {
      try {
        const callbackResponse = await callback();

        return callbackResponse;
      } catch (err) {
        if (debug)
          logMessage({
            message: 'Retrier - Retrying',
            params: {
              currentAttempt: retry,
              totalAttempt: attempts,
              error: err,
              errorMessage: err.message
            },
            debug
          })

        await sleep(exponentialBackoff(retry));

        retry++;
        if (attempts == retry) {
          throw err;
        }
      }
    }
  } catch (err) {
    logMessage({
      message: 'Retrier - Error after tries',
      params: {
        currentAttempt: retry,
        totalAttempt: attempts,
        error: err,
        errorMessage: err.message
      },
      debug
    })

    throw err;
  }
}

const logMessage = ({ message, params = {}, debug = false }) => {
  if (!debug) {
    return
  }

  const logMsg = Object.assign({}, params);
  logMsg.message = message;

  console.log(JSON.stringify(logMsg));
}

const exponentialBackoffFunction = (retry) => 1000 * (retry / 2);

const sleep = (n) => {
  return new Promise(function (resolve) {
    setTimeout(resolve, n);
  });
}

module.exports = retrier