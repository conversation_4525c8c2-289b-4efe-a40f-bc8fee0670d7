# Retrier
Utility that provides a retry functionality as a wrapper.

Main Features:
- Custom attempts.
- exponential backoff.

## Installation
Add this module to your project's package.json dependencies section:
```json
"dependencies": {
    "ac-utils": "git+ssh://*****************/aircanada-m3/ac-utils.git#RELEASE_TAG"
  }
```
> Change `RELEASE_TAG` placeholder to the recommended version tag.

## How to use

```js
const retrier = require('ac-utils/src/retrier');

module.exports.handler = async (event, context) => {
  const response = await retrier({ callback: () => myFunction(1) });

  console.log(response);
}

const myFunction = async (item) => {
  const responsePromise = await new Promise(async (resolve, reject) => {
    if (item === 3 || item === 1)
    reject('test');

    setTimeout(() => resolve(`my item ${item}`), 1000)
  })

  // if (item === 3 || item === 1)
  //   throw Error('test-2');

  return responsePromise;
}
```

## Retrier Parameters

- `callback` - The function that will be retried.
- `attempts` - The number of times to retry the function | `Default: 3`
- `exponentialBackoff` - A function that returns the amount of milliseconds to wait before the next attempt | `Default: (retry) => 1000 * (retry / 2)`
- `debug` - If enabled, attempts counter is logged  | `Default: false`