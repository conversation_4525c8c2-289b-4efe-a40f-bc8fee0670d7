const knex = require('knex');
const { asyncForEach, asyncMapRunInParallelInChunks } = require('../async')
const retrier = require('../retrier')
const path = require('path');

class QueryManager {
  constructor(params) {
    if (params)
      this.init(params)
  }

  initOnce = (params) => {
    if (!this.knex)
      this.init(params);
  }

  init = ({
    host,
    user,
    password,
    database,
    port = 3306,
    debug = false,
    connectionDebug = false,
    migrationsDebug = false,
    asyncStackTraces = false,
    pool = {},
    postProcessResponse = null,
    acquireConnectionTimeout = 60000, // Milliseconds
    client = 'mysql2',
    hooks = {}, // query, query-success, query-error, error,
    migrations = null, // {directory: "./migrations", tableName:"knex_migrations", ...}
    instanceName = 'Default'
  }) => {
    this.host = host;
    this.user = user;
    this.password = password;
    this.database = database;
    this.port = port;
    this.debug = debug;
    this.connectionDebug = connectionDebug;
    this.migrationsDebug = migrationsDebug;
    this.asyncStackTraces = asyncStackTraces;
    this.pool = {
      min: 0,
      max: 100,  // if you want to run parallel DB operations with transaction then increase it as needed.
      idleTimeoutMillis: 60000, // free resouces are destroyed after this many milliseconds (1 minute by default)
      afterCreate: function (conn, done) {
        logMessage({
          message: `${instanceName}  QueryManagerInstance - CONNECTION CREATED`,
          debug: connectionDebug
        })
        done(null, conn);
      }, ...pool
    };
    this.postProcessResponse = postProcessResponse;
    this.client = client;
    this.acquireConnectionTimeout = acquireConnectionTimeout;
    this.migrations = migrations;
    this.instanceName = instanceName;

    this.statements = [];
    this.statementsInGroups = {};

    this.getNewKnexInstance();

    const hooksKeys = Object.keys(hooks);

    // query, query-success, query-error, error
    if (hooksKeys) {
      hooksKeys.forEach(key => this.knex.on(key, hooks[key]));
    }
  }

  getNewKnexInstance = _ => this.knex = knex({
    client: this.client,
    debug: this.debug,
    asyncStackTraces: this.asyncStackTraces,
    postProcessResponse: this.postProcessResponse,
    connection: {
      host: this.host,
      port: this.port,
      user: this.user,
      password: this.password,
      database: this.database
    },
    acquireConnectionTimeout: this.acquireConnectionTimeout,
    pool: this.pool,
    migrations: this.migrations
  })

  isConnected = async _ => {
    try {
      return !! await this.getKnex().raw('SELECT 1');
    } catch (error) {
      //console.log(error)
      return false;
    }
  }
  reConnect = async _ => this.getNewKnexInstance()
  destroy = async _ => this.getBuilder().destroy();

  addRawStatements = (rawStatements) => {
    const statementsToAdd = Array.isArray(rawStatements) ? rawStatements : [rawStatements];

    statementsToAdd.forEach(statement => {
      if (typeof statement !== 'string') {
        throw new Error(`${statement} is not a raw statement`);
      }

      this.statements.push(statement);

      // logMessage({
      //   message: 'QueryManager - Statement added',
      //   params: {
      //     statement: statement,
      //     method: 'addRawStatements'
      //   },
      //   debug: this.debug
      // })
    })
  }

  addStatements = (callbackOrStatements) => {
    let statements = callbackOrStatements;

    if (typeof callbackOrStatements === 'function') {
      statements = callbackOrStatements(this.getKnex());
    }

    const statementsToAdd = Array.isArray(statements) ? statements : [statements];

    statementsToAdd.forEach(statement => {
      this.addRawStatements(statement.toString());

      // logMessage({
      //   message: 'QueryManager - Statement added',
      //   params: {
      //     statement: statement.toString(),
      //     method: 'addStatements'
      //   },
      //   debug: this.debug
      // })
    })
  }


  addStatementsInGroups = (callbackOrStatements) => {
    let statementsToAdd = callbackOrStatements;

    if (typeof callbackOrStatements === 'function') {
      statementsToAdd = callbackOrStatements(this.getKnex());
    }


    // const statementsToAdd = Array.isArray(statements) ? statements : [statements];

    statementsToAdd.forEach(({ group, statements }) => {
      if (!this.statementsInGroups.hasOwnProperty(group)) {
        this.statementsInGroups[group] = [];
      }
      this.statementsInGroups[group].push(...statements.map(statement => statement.toString()));

      // logMessage({
      //   message: 'QueryManager - Statement added',
      //   params: {
      //     statement: statement.toString(),
      //     method: 'addStatements'
      //   },
      //   debug: this.debug
      // })
    })
  }

  clearStatements = _ => this.statements = []
  clearStatementsInGroups = _ => this.statementsInGroups = {}
  getStatements = _ => this.statements
  getStatementsInGroups = _ => this.statementsInGroups
  getKnex = _ => this.knex
  getBuilder = _ => this.getKnex()
  getMigrate = _ => this.knex.migrate

  runStatements = async ({ retryAttempts = 3, retriesDelay = retriesDelayFunction } = {}) => {
    const response = await retrier({
      attempts: retryAttempts,
      retriesDelay,
      callback: () => this.transaction(async (trx) => {
        return await asyncForEach(this.statements, async statement => {
          return await trx.raw(statement);
        })
      }),
      instanceName: this.instanceName
    })
    this.clearStatements();

    return response;
  }

  runStatementsInParallel = async ({ retryAttempts = 3, batchSize = 1000, retriesDelay = retriesDelayFunction } = {}) => {
    const response = await retrier({
      attempts: retryAttempts,
      retriesDelay,
      callback: () => this.transaction(async (trx) => {
        return await asyncMapRunInParallelInChunks(this.statements, batchSize, async statement => {
          return await trx.raw(statement);
        })
      }),
      instanceName: this.instanceName
    })
    this.clearStatements();

    return response;
  }

  runStatementsWithoutTransaction = async ({ retryAttempts = 3, retriesDelay = retriesDelayFunction } = {}) => {
    const response = await retrier({
      attempts: retryAttempts,
      retriesDelay,
      callback: () => asyncForEach(this.statements, async statement => {
        return await this.getKnex().raw(statement);
      }),
      instanceName: this.instanceName
    })
    this.clearStatements();

    return response;
  }

  runStatementsWithoutTransactionInParallel = async ({ retryAttempts = 3, batchSize = 1000, retriesDelay = retriesDelayFunction } = {}) => {
    const response = await retrier({
      attempts: retryAttempts,
      retriesDelay,
      callback: () => asyncMapRunInParallelInChunks(this.statements, batchSize, async statement => {
        return await this.getKnex().raw(statement);
      }),
      instanceName: this.instanceName
    })
    this.clearStatements();

    return response;
  }

  runStatementsInGroupInParallel = async ({ retryAttempts = 3, batchSize = 1000, retriesDelay = retriesDelayFunction } = {}) => {
    const response = await retrier({
      attempts: retryAttempts,
      retriesDelay,
      callback: () => this.transaction(async (trx) => {
        return await asyncForEach(Object.keys(this.statementsInGroups), async statementGroup => {
          return await asyncMapRunInParallelInChunks(this.statementsInGroups[statementGroup], batchSize, async statement => {
            return await trx.raw(statement);
          })
        })
      }),
      instanceName: this.instanceName
    })
    this.clearStatementsInGroups();

    return response;
  }

  execute = async callback => await callback(this.getKnex())

  transaction = async (callback) => {
    const trx = await this.getKnex().transaction();

    if (!callback)
      return trx;

    try {
      const response = await callback(trx);
      await trx.commit();
      return response;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  upsert = ({ table, data, builder = this.getBuilder(), updateKeysToExclude = [] }) => {
    const keys = Object.keys(data[0]);
    const listFieldUpdate = keys.filter((key) => !updateKeysToExclude.includes(key));
    const statement = builder.table(table).insert(data).onConflict().merge(listFieldUpdate);

    return statement;
  }

  listPendingMigrations = async () => {
    const builder = this.getBuilder();

    // Index 0 -> already migrated
    // Index 1 -> To be migrated
    const list = await builder.migrate.list();
    const toBeMigrated = [];

    list[1].forEach(({ file, directory }) => {
      const absoluteDir = path.resolve(process.cwd(), `${directory}`);
      const { up, down } = require(`${absoluteDir}/${file}`);

      toBeMigrated.push({
        file,
        up: up(builder).toString(),
        down: down(builder).toString()
      })

    });

    return toBeMigrated;
  }

  runMigrations = async () => {
    const builder = this.getBuilder();
    const migrationCurrentVersion = await builder.migrate.currentVersion();

    try {
      const migrationResponse = await builder.migrate.latest();
      const migrationStatusResponse = await builder.migrate.status();

      logMessage({
        message: `${this.instanceName}  QueryManagerInstance - run migrations`,
        params: {
          response: migrationResponse,
          status: migrationStatusResponse,
          method: 'runMigrations'
        },
        debug: this.migrationsDebug
      })

      if (migrationStatusResponse !== 0) {
        throw new Error(`Migration status returned ${migrationStatusResponse}`);
      }

      logMessage({
        message: `${this.instanceName}  QueryManagerInstance - Migrations completed successfully`,
        params: {
          method: 'runMigrations'
        },
        debug: this.migrationsDebug
      })

      return { status: migrationStatusResponse, success: true, message: "Migrations completed successfully", migrationResponse }
    } catch (error) {

      const migrationNewVersion = await builder.migrate.currentVersion();

      if (migrationCurrentVersion !== migrationNewVersion) {
        logMessage({
          message: `${this.instanceName}  QueryManagerInstance - Performing a rollback due to error: ` + error,
          params: {
            method: 'runMigrations'
          },
          debug: this.migrationsDebug
        })

        await builder.migrate.rollback();
      }

      return { status: 1, success: false, message: "Migrations failed", errorMessage: error.message }
    }
  }
}

const logMessage = ({ message, params = {}, debug = false }) => {
  if (!debug) {
    return
  }

  const logMsg = Object.assign({}, params);
  logMsg.message = message;

  console.log(JSON.stringify(logMsg));
}

const retriesDelayFunction = (retry) => 1000 * (retry / 2);

module.exports = QueryManager;