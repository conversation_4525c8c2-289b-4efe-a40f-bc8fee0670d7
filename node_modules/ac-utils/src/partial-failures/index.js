'use strict'

const { asyncMapRunInParallelInChunks, asyncForEach } = require('../async');
const retrier = require('../retrier');

const AC_UTILS_PARTIAL_FAILURES_DEBUG_MODE = process.env.AC_UTILS_PARTIAL_FAILURES_DEBUG_MODE == 'true' || false;

const sleep = (n) => {
  return new Promise(function (resolve) {
    setTimeout(resolve, n);
  });
}

const processInSequence = async ({ list, callback, retries = 3, retriesDelay = retriesDelayFunction }) => {

  const failedItems = [];
  const responses = [];

  await asyncForEach(list, async (item) => {
    responses.push(await internalFunction({ item, retries, retriesDelay, callback, failedItems }))
  });

  return {
    responses,
    failures: failedItems
  };
}

const processInBatches = async ({ list, batchSize = 10, callback, retries = 3, retriesDelay = retriesDelayFunction }) => {
  const failedItems = [];
  const responses = [];

  await asyncMapRunInParallelInChunks(list, batchSize, async (item, index) => {
    responses.push(await internalFunction({ item, retries, retriesDelay, callback, failedItems, index }))
  });

  return {
    responses,
    failures: failedItems
  };
}

const internalFunction = async ({ item, retries, retriesDelay, callback, failedItems, index }) => {
  try {
    return await retrier({
      callback: async () => await callback(item, index),
      attempts: retries,
      exponentialBackoff: retriesDelay,
      debug: AC_UTILS_PARTIAL_FAILURES_DEBUG_MODE
    });
  } catch (err) {
    failedItems.push({ item, error: err, errorMessage: err.message });
  }
}

const retriesDelayFunction = (retry) => 1000 * (retry / 2);

module.exports = {
  processInSequence,
  processInBatches
}