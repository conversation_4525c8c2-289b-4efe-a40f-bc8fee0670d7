import { FlagInfo } from './private/flag-modeling';
export declare const ENABLE_STACK_NAME_DUPLICATES_CONTEXT = "@aws-cdk/core:enableStackNameDuplicates";
export declare const ENABLE_DIFF_NO_FAIL_CONTEXT = "aws-cdk:enableDiffNoFail";
/** @deprecated use `ENABLE_DIFF_NO_FAIL_CONTEXT` */
export declare const ENABLE_DIFF_NO_FAIL = "aws-cdk:enableDiffNoFail";
export declare const NEW_STYLE_STACK_SYNTHESIS_CONTEXT = "@aws-cdk/core:newStyleStackSynthesis";
export declare const STACK_RELATIVE_EXPORTS_CONTEXT = "@aws-cdk/core:stackRelativeExports";
export declare const DOCKER_IGNORE_SUPPORT = "@aws-cdk/aws-ecr-assets:dockerIgnoreSupport";
export declare const SECRETS_MANAGER_PARSE_OWNED_SECRET_NAME = "@aws-cdk/aws-secretsmanager:parseOwnedSecretName";
export declare const KMS_DEFAULT_KEY_POLICIES = "@aws-cdk/aws-kms:defaultKeyPolicies";
export declare const S3_GRANT_WRITE_WITHOUT_ACL = "@aws-cdk/aws-s3:grantWriteWithoutAcl";
export declare const ECS_REMOVE_DEFAULT_DESIRED_COUNT = "@aws-cdk/aws-ecs-patterns:removeDefaultDesiredCount";
export declare const RDS_LOWERCASE_DB_IDENTIFIER = "@aws-cdk/aws-rds:lowercaseDbIdentifier";
export declare const APIGATEWAY_USAGEPLANKEY_ORDERINSENSITIVE_ID = "@aws-cdk/aws-apigateway:usagePlanKeyOrderInsensitiveId";
export declare const EFS_DEFAULT_ENCRYPTION_AT_REST = "@aws-cdk/aws-efs:defaultEncryptionAtRest";
export declare const LAMBDA_RECOGNIZE_VERSION_PROPS = "@aws-cdk/aws-lambda:recognizeVersionProps";
export declare const LAMBDA_RECOGNIZE_LAYER_VERSION = "@aws-cdk/aws-lambda:recognizeLayerVersion";
export declare const CLOUDFRONT_DEFAULT_SECURITY_POLICY_TLS_V1_2_2021 = "@aws-cdk/aws-cloudfront:defaultSecurityPolicyTLSv1.2_2021";
export declare const CHECK_SECRET_USAGE = "@aws-cdk/core:checkSecretUsage";
export declare const TARGET_PARTITIONS = "@aws-cdk/core:target-partitions";
export declare const ECS_SERVICE_EXTENSIONS_ENABLE_DEFAULT_LOG_DRIVER = "@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver";
export declare const EC2_UNIQUE_IMDSV2_LAUNCH_TEMPLATE_NAME = "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName";
export declare const ECS_ARN_FORMAT_INCLUDES_CLUSTER_NAME = "@aws-cdk/aws-ecs:arnFormatIncludesClusterName";
export declare const IAM_MINIMIZE_POLICIES = "@aws-cdk/aws-iam:minimizePolicies";
export declare const IAM_IMPORTED_ROLE_STACK_SAFE_DEFAULT_POLICY_NAME = "@aws-cdk/aws-iam:importedRoleStackSafeDefaultPolicyName";
export declare const VALIDATE_SNAPSHOT_REMOVAL_POLICY = "@aws-cdk/core:validateSnapshotRemovalPolicy";
export declare const CODEPIPELINE_CROSS_ACCOUNT_KEY_ALIAS_STACK_SAFE_RESOURCE_NAME = "@aws-cdk/aws-codepipeline:crossAccountKeyAliasStackSafeResourceName";
export declare const S3_CREATE_DEFAULT_LOGGING_POLICY = "@aws-cdk/aws-s3:createDefaultLoggingPolicy";
export declare const SNS_SUBSCRIPTIONS_SQS_DECRYPTION_POLICY = "@aws-cdk/aws-sns-subscriptions:restrictSqsDescryption";
export declare const APIGATEWAY_DISABLE_CLOUDWATCH_ROLE = "@aws-cdk/aws-apigateway:disableCloudWatchRole";
export declare const ENABLE_PARTITION_LITERALS = "@aws-cdk/core:enablePartitionLiterals";
export declare const EVENTS_TARGET_QUEUE_SAME_ACCOUNT = "@aws-cdk/aws-events:eventsTargetQueueSameAccount";
export declare const ECS_DISABLE_EXPLICIT_DEPLOYMENT_CONTROLLER_FOR_CIRCUIT_BREAKER = "@aws-cdk/aws-ecs:disableExplicitDeploymentControllerForCircuitBreaker";
export declare const S3_SERVER_ACCESS_LOGS_USE_BUCKET_POLICY = "@aws-cdk/aws-s3:serverAccessLogsUseBucketPolicy";
export declare const ROUTE53_PATTERNS_USE_CERTIFICATE = "@aws-cdk/aws-route53-patters:useCertificate";
export declare const AWS_CUSTOM_RESOURCE_LATEST_SDK_DEFAULT = "@aws-cdk/customresources:installLatestAwsSdkDefault";
export declare const DATABASE_PROXY_UNIQUE_RESOURCE_NAME = "@aws-cdk/aws-rds:databaseProxyUniqueResourceName";
export declare const CODEDEPLOY_REMOVE_ALARMS_FROM_DEPLOYMENT_GROUP = "@aws-cdk/aws-codedeploy:removeAlarmsFromDeploymentGroup";
export declare const APIGATEWAY_AUTHORIZER_CHANGE_DEPLOYMENT_LOGICAL_ID = "@aws-cdk/aws-apigateway:authorizerChangeDeploymentLogicalId";
export declare const EC2_LAUNCH_TEMPLATE_DEFAULT_USER_DATA = "@aws-cdk/aws-ec2:launchTemplateDefaultUserData";
export declare const SECRETS_MANAGER_TARGET_ATTACHMENT_RESOURCE_POLICY = "@aws-cdk/aws-secretsmanager:useAttachedSecretResourcePolicyForSecretTargetAttachments";
export declare const REDSHIFT_COLUMN_ID = "@aws-cdk/aws-redshift:columnId";
export declare const ENABLE_EMR_SERVICE_POLICY_V2 = "@aws-cdk/aws-stepfunctions-tasks:enableEmrServicePolicyV2";
export declare const EC2_RESTRICT_DEFAULT_SECURITY_GROUP = "@aws-cdk/aws-ec2:restrictDefaultSecurityGroup";
export declare const APIGATEWAY_REQUEST_VALIDATOR_UNIQUE_ID = "@aws-cdk/aws-apigateway:requestValidatorUniqueId";
export declare const INCLUDE_PREFIX_IN_UNIQUE_NAME_GENERATION = "@aws-cdk/core:includePrefixInUniqueNameGeneration";
export declare const KMS_ALIAS_NAME_REF = "@aws-cdk/aws-kms:aliasNameRef";
export declare const KMS_APPLY_IMPORTED_ALIAS_PERMISSIONS_TO_PRINCIPAL = "@aws-cdk/aws-kms:applyImportedAliasPermissionsToPrincipal";
export declare const EFS_DENY_ANONYMOUS_ACCESS = "@aws-cdk/aws-efs:denyAnonymousAccess";
export declare const EFS_MOUNTTARGET_ORDERINSENSITIVE_LOGICAL_ID = "@aws-cdk/aws-efs:mountTargetOrderInsensitiveLogicalId";
export declare const AUTOSCALING_GENERATE_LAUNCH_TEMPLATE = "@aws-cdk/aws-autoscaling:generateLaunchTemplateInsteadOfLaunchConfig";
export declare const ENABLE_OPENSEARCH_MULTIAZ_WITH_STANDBY = "@aws-cdk/aws-opensearchservice:enableOpensearchMultiAzWithStandby";
export declare const LAMBDA_NODEJS_USE_LATEST_RUNTIME = "@aws-cdk/aws-lambda-nodejs:useLatestRuntimeVersion";
export declare const RDS_PREVENT_RENDERING_DEPRECATED_CREDENTIALS = "@aws-cdk/aws-rds:preventRenderingDeprecatedCredentials";
export declare const AURORA_CLUSTER_CHANGE_SCOPE_OF_INSTANCE_PARAMETER_GROUP_WITH_EACH_PARAMETERS = "@aws-cdk/aws-rds:auroraClusterChangeScopeOfInstanceParameterGroupWithEachParameters";
export declare const APPSYNC_ENABLE_USE_ARN_IDENTIFIER_SOURCE_API_ASSOCIATION = "@aws-cdk/aws-appsync:useArnForSourceApiAssociationIdentifier";
export declare const CODECOMMIT_SOURCE_ACTION_DEFAULT_BRANCH_NAME = "@aws-cdk/aws-codepipeline-actions:useNewDefaultBranchForCodeCommitSource";
export declare const LAMBDA_PERMISSION_LOGICAL_ID_FOR_LAMBDA_ACTION = "@aws-cdk/aws-cloudwatch-actions:changeLambdaPermissionLogicalIdForLambdaAction";
export declare const CODEPIPELINE_CROSS_ACCOUNT_KEYS_DEFAULT_VALUE_TO_FALSE = "@aws-cdk/aws-codepipeline:crossAccountKeysDefaultValueToFalse";
export declare const CODEPIPELINE_DEFAULT_PIPELINE_TYPE_TO_V2 = "@aws-cdk/aws-codepipeline:defaultPipelineTypeToV2";
export declare const KMS_REDUCE_CROSS_ACCOUNT_REGION_POLICY_SCOPE = "@aws-cdk/aws-kms:reduceCrossAccountRegionPolicyScope";
export declare const PIPELINE_REDUCE_ASSET_ROLE_TRUST_SCOPE = "@aws-cdk/pipelines:reduceAssetRoleTrustScope";
export declare const EKS_NODEGROUP_NAME = "@aws-cdk/aws-eks:nodegroupNameAttribute";
export declare const EBS_DEFAULT_GP3 = "@aws-cdk/aws-ec2:ebsDefaultGp3Volume";
export declare const ECS_REMOVE_DEFAULT_DEPLOYMENT_ALARM = "@aws-cdk/aws-ecs:removeDefaultDeploymentAlarm";
export declare const LOG_API_RESPONSE_DATA_PROPERTY_TRUE_DEFAULT = "@aws-cdk/custom-resources:logApiResponseDataPropertyTrueDefault";
export declare const S3_KEEP_NOTIFICATION_IN_IMPORTED_BUCKET = "@aws-cdk/aws-s3:keepNotificationInImportedBucket";
export declare const USE_NEW_S3URI_PARAMETERS_FOR_BEDROCK_INVOKE_MODEL_TASK = "@aws-cdk/aws-stepfunctions-tasks:useNewS3UriParametersForBedrockInvokeModelTask";
export declare const EXPLICIT_STACK_TAGS = "@aws-cdk/core:explicitStackTags";
export declare const REDUCE_EC2_FARGATE_CLOUDWATCH_PERMISSIONS = "@aws-cdk/aws-ecs:reduceEc2FargateCloudWatchPermissions";
export declare const DYNAMODB_TABLEV2_RESOURCE_POLICY_PER_REPLICA = "@aws-cdk/aws-dynamodb:resourcePolicyPerReplica";
export declare const EC2_SUM_TIMEOUT_ENABLED = "@aws-cdk/aws-ec2:ec2SumTImeoutEnabled";
export declare const APPSYNC_GRAPHQLAPI_SCOPE_LAMBDA_FUNCTION_PERMISSION = "@aws-cdk/aws-appsync:appSyncGraphQLAPIScopeLambdaPermission";
export declare const USE_CORRECT_VALUE_FOR_INSTANCE_RESOURCE_ID_PROPERTY = "@aws-cdk/aws-rds:setCorrectValueForDatabaseInstanceReadReplicaInstanceResourceId";
export declare const CFN_INCLUDE_REJECT_COMPLEX_RESOURCE_UPDATE_CREATE_POLICY_INTRINSICS = "@aws-cdk/core:cfnIncludeRejectComplexResourceUpdateCreatePolicyIntrinsics";
export declare const LAMBDA_NODEJS_SDK_V3_EXCLUDE_SMITHY_PACKAGES = "@aws-cdk/aws-lambda-nodejs:sdkV3ExcludeSmithyPackages";
export declare const STEPFUNCTIONS_TASKS_FIX_RUN_ECS_TASK_POLICY = "@aws-cdk/aws-stepfunctions-tasks:fixRunEcsTaskPolicy";
export declare const STEPFUNCTIONS_USE_DISTRIBUTED_MAP_RESULT_WRITER_V2 = "@aws-cdk/aws-stepfunctions:useDistributedMapResultWriterV2";
export declare const BASTION_HOST_USE_AMAZON_LINUX_2023_BY_DEFAULT = "@aws-cdk/aws-ec2:bastionHostUseAmazonLinux2023ByDefault";
export declare const ASPECT_STABILIZATION = "@aws-cdk/core:aspectStabilization";
export declare const USER_POOL_DOMAIN_NAME_METHOD_WITHOUT_CUSTOM_RESOURCE = "@aws-cdk/aws-route53-targets:userPoolDomainNameMethodWithoutCustomResource";
export declare const Enable_IMDS_Blocking_Deprecated_Feature = "@aws-cdk/aws-ecs:enableImdsBlockingDeprecatedFeature";
export declare const Disable_ECS_IMDS_Blocking = "@aws-cdk/aws-ecs:disableEcsImdsBlocking";
export declare const ALB_DUALSTACK_WITHOUT_PUBLIC_IPV4_SECURITY_GROUP_RULES_DEFAULT = "@aws-cdk/aws-elasticloadbalancingV2:albDualstackWithoutPublicIpv4SecurityGroupRulesDefault";
export declare const IAM_OIDC_REJECT_UNAUTHORIZED_CONNECTIONS = "@aws-cdk/aws-iam:oidcRejectUnauthorizedConnections";
export declare const ENABLE_ADDITIONAL_METADATA_COLLECTION = "@aws-cdk/core:enableAdditionalMetadataCollection";
export declare const LAMBDA_CREATE_NEW_POLICIES_WITH_ADDTOROLEPOLICY = "@aws-cdk/aws-lambda:createNewPoliciesWithAddToRolePolicy";
export declare const SET_UNIQUE_REPLICATION_ROLE_NAME = "@aws-cdk/aws-s3:setUniqueReplicationRoleName";
export declare const PIPELINE_REDUCE_STAGE_ROLE_TRUST_SCOPE = "@aws-cdk/pipelines:reduceStageRoleTrustScope";
export declare const EVENTBUS_POLICY_SID_REQUIRED = "@aws-cdk/aws-events:requireEventBusPolicySid";
export declare const ASPECT_PRIORITIES_MUTATING = "@aws-cdk/core:aspectPrioritiesMutating";
export declare const DYNAMODB_TABLE_RETAIN_TABLE_REPLICA = "@aws-cdk/aws-dynamodb:retainTableReplica";
export declare const LOG_USER_POOL_CLIENT_SECRET_VALUE = "@aws-cdk/cognito:logUserPoolClientSecretValue";
export declare const PIPELINE_REDUCE_CROSS_ACCOUNT_ACTION_ROLE_TRUST_SCOPE = "@aws-cdk/pipelines:reduceCrossAccountActionRoleTrustScope";
export declare const S3_TRUST_KEY_POLICY_FOR_SNS_SUBSCRIPTIONS = "@aws-cdk/s3-notifications:addS3TrustKeyPolicyForSnsSubscriptions";
export declare const EC2_REQUIRE_PRIVATE_SUBNETS_FOR_EGRESSONLYINTERNETGATEWAY = "@aws-cdk/aws-ec2:requirePrivateSubnetsForEgressOnlyInternetGateway";
export declare const USE_RESOURCEID_FOR_VPCV2_MIGRATION = "@aws-cdk/aws-ec2-alpha:useResourceIdForVpcV2Migration";
export declare const S3_PUBLIC_ACCESS_BLOCKED_BY_DEFAULT = "@aws-cdk/aws-s3:publicAccessBlockedByDefault";
export declare const USE_CDK_MANAGED_LAMBDA_LOGGROUP = "@aws-cdk/aws-lambda:useCdkManagedLogGroup";
export declare const FLAGS: Record<string, FlagInfo>;
export declare const CURRENT_MV = "v2";
/**
 * The list of future flags that are now expired. This is going to be used to identify
 * and block usages of old feature flags in the new major version of CDK.
 */
export declare const CURRENT_VERSION_EXPIRED_FLAGS: string[];
/**
 * Flag values that should apply for new projects
 *
 * Add a flag in here (typically with the value `true`), to enable
 * backwards-breaking behavior changes only for new projects.  New projects
 * generated through `cdk init` will include these flags in their generated
 * project config.
 *
 * Tests must cover the default (disabled) case and the future (enabled) case.
 *
 * Going forward, this should *NOT* be consumed directly anymore.
 */
export declare const CURRENTLY_RECOMMENDED_FLAGS: {
    [k: string]: any;
};
/**
 * The default values of each of these flags in the current major version.
 *
 * This is the effective value of the flag, unless it's overridden via
 * context.
 *
 * Adding new flags here is only allowed during the pre-release period of a new
 * major version!
 */
export declare const CURRENT_VERSION_FLAG_DEFAULTS: {
    [k: string]: any;
};
export declare function futureFlagDefault(flag: string): boolean;
/** @deprecated use CURRENT_VERSION_EXPIRED_FLAGS instead */
export declare const FUTURE_FLAGS_EXPIRED: string[];
/** @deprecated do not use at all! */
export declare const FUTURE_FLAGS: {
    [k: string]: any;
};
/** @deprecated do not use at all! */
export declare const NEW_PROJECT_DEFAULT_CONTEXT: {
    [k: string]: any;
};
