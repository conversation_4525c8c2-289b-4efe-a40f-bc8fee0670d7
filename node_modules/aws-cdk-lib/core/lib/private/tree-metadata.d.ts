import { Construct } from 'constructs';
import { ConstructInfo } from './runtime-info';
import { ISynthesisSession } from '../stack-synthesizers';
/**
 * Construct that is automatically attached to the top-level `App`.
 * This generates, as part of synthesis, a file containing the construct tree and the metadata for each node in the tree.
 * The output is in a tree format so as to preserve the construct hierarchy.
 */
export declare class TreeMetadata extends Construct {
    constructor(scope: Construct);
    /**
     * Create tree.json
     * @internal
     */
    _synthesizeTree(session: ISynthesisSession): void;
    private synthAttributes;
}
/**
 * Serializable representation of a construct
 */
interface Node {
    /**
     * The construct's ID
     *
     * Even though this ID is already in the `children` map of the containing node,
     * we repeat it here.
     */
    readonly id: string;
    /**
     * The construct's path
     *
     * Even though this path can be constructed from the construct IDs of constructs
     * on the root path to this construct, we still repeat it here.
     *
     * FIXME: In a sizeable file (tested on 136MB) this takes about 20% of the
     * total size without adding any value. We should probably remove this at some
     * point.
     */
    readonly path: string;
    children?: {
        [key: string]: TreeNode;
    };
    attributes?: {
        [key: string]: unknown;
    };
    /**
     * Information on the construct class that led to this node, if available
     */
    constructInfo?: ConstructInfo;
}
export interface TreeFile {
    version: 'tree-0.1';
    tree: TreeNode;
}
export interface ForestFile {
    version: 'forest-0.1';
    forest: Record<string, TreeNode>;
}
type TreeNode = Node | SubTreeReference;
/**
 * A reference to a node that is stored in an entirely different tree.json file
 */
interface SubTreeReference {
    readonly id: string;
    readonly path: string;
    fileName: string;
    /**
     * If set, indicates the subtree in the forest file
     *
     * If this is set then `fileName` must point to a ForestFile, and this indicates
     * the tree inside the forest.
     */
    treeId?: string;
}
export declare function isSubtreeReference(x: TreeFile['tree']): x is Extract<TreeFile['tree'], {
    fileName: string;
}>;
export {};
