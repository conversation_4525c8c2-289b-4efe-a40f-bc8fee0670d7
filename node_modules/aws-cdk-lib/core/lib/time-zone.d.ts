/**
 * Canonical names of the IANA time zones, derived from the IANA Time Zone Database.
 *
 * For more information, see https://en.wikipedia.org/wiki/List_of_tz_database_time_zones.
 */
export declare class TimeZone {
    readonly timezoneName: string;
    /** IANA Time Zone database entry for Africa/Abidjan.  UTC offset +00:00/+00:00. */
    static readonly AFRICA_ABIDJAN: TimeZone;
    /** IANA Time Zone database entry for Africa/Algiers.  UTC offset +01:00/+01:00. */
    static readonly AFRICA_ALGIERS: TimeZone;
    /** IANA Time Zone database entry for Africa/Bissau.  UTC offset +00:00/+00:00. */
    static readonly AFRICA_BISSAU: TimeZone;
    /** IANA Time Zone database entry for Africa/Cairo.  UTC offset +02:00/+02:00. */
    static readonly AFRICA_CAIRO: TimeZone;
    /** IANA Time Zone database entry for Africa/Casablanca.  UTC offset +01:00/+00:00. */
    static readonly AFRICA_CASABLANCA: TimeZone;
    /** IANA Time Zone database entry for Africa/Ceuta.  UTC offset +01:00/+02:00. */
    static readonly AFRICA_CEUTA: TimeZone;
    /** IANA Time Zone database entry for Africa/El_Aaiun.  UTC offset +01:00/+00:00. */
    static readonly AFRICA_EL_AAIUN: TimeZone;
    /** IANA Time Zone database entry for Africa/Johannesburg.  UTC offset +02:00/+02:00. */
    static readonly AFRICA_JOHANNESBURG: TimeZone;
    /** IANA Time Zone database entry for Africa/Juba.  UTC offset +02:00/+02:00. */
    static readonly AFRICA_JUBA: TimeZone;
    /** IANA Time Zone database entry for Africa/Khartoum.  UTC offset +02:00/+02:00. */
    static readonly AFRICA_KHARTOUM: TimeZone;
    /** IANA Time Zone database entry for Africa/Lagos.  UTC offset +01:00/+01:00. */
    static readonly AFRICA_LAGOS: TimeZone;
    /** IANA Time Zone database entry for Africa/Maputo.  UTC offset +02:00/+02:00. */
    static readonly AFRICA_MAPUTO: TimeZone;
    /** IANA Time Zone database entry for Africa/Monrovia.  UTC offset +00:00/+00:00. */
    static readonly AFRICA_MONROVIA: TimeZone;
    /** IANA Time Zone database entry for Africa/Nairobi.  UTC offset +03:00/+03:00. */
    static readonly AFRICA_NAIROBI: TimeZone;
    /** IANA Time Zone database entry for Africa/Ndjamena.  UTC offset +01:00/+01:00. */
    static readonly AFRICA_NDJAMENA: TimeZone;
    /** IANA Time Zone database entry for Africa/Sao_Tome.  UTC offset +00:00/+00:00. */
    static readonly AFRICA_SAO_TOME: TimeZone;
    /** IANA Time Zone database entry for Africa/Tripoli.  UTC offset +02:00/+02:00. */
    static readonly AFRICA_TRIPOLI: TimeZone;
    /** IANA Time Zone database entry for Africa/Tunis.  UTC offset +01:00/+01:00. */
    static readonly AFRICA_TUNIS: TimeZone;
    /** IANA Time Zone database entry for Africa/Windhoek.  UTC offset +02:00/+02:00. */
    static readonly AFRICA_WINDHOEK: TimeZone;
    /** IANA Time Zone database entry for America/Adak.  UTC offset −10:00/−09:00. */
    static readonly AMERICA_ADAK: TimeZone;
    /** IANA Time Zone database entry for America/Anchorage.  UTC offset −09:00/−08:00. */
    static readonly AMERICA_ANCHORAGE: TimeZone;
    /** IANA Time Zone database entry for America/Araguaina.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARAGUAINA: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/Buenos_Aires.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_BUENOS_AIRES: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/Catamarca.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_CATAMARCA: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/Cordoba.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_CORDOBA: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/Jujuy.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_JUJUY: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/La_Rioja.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_LA_RIOJA: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/Mendoza.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_MENDOZA: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/Rio_Gallegos.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_RIO_GALLEGOS: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/Salta.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_SALTA: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/San_Juan.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_SAN_JUAN: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/San_Luis.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_SAN_LUIS: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/Tucuman.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_TUCUMAN: TimeZone;
    /** IANA Time Zone database entry for America/Argentina/Ushuaia.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_ARGENTINA_USHUAIA: TimeZone;
    /** IANA Time Zone database entry for America/Asuncion.  UTC offset −04:00/−03:00. */
    static readonly AMERICA_ASUNCION: TimeZone;
    /** IANA Time Zone database entry for America/Bahia.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_BAHIA: TimeZone;
    /** IANA Time Zone database entry for America/Bahia_Banderas.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_BAHIA_BANDERAS: TimeZone;
    /** IANA Time Zone database entry for America/Barbados.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_BARBADOS: TimeZone;
    /** IANA Time Zone database entry for America/Belem.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_BELEM: TimeZone;
    /** IANA Time Zone database entry for America/Belize.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_BELIZE: TimeZone;
    /** IANA Time Zone database entry for America/Boa_Vista.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_BOA_VISTA: TimeZone;
    /** IANA Time Zone database entry for America/Bogota.  UTC offset −05:00/−05:00. */
    static readonly AMERICA_BOGOTA: TimeZone;
    /** IANA Time Zone database entry for America/Boise.  UTC offset −07:00/−06:00. */
    static readonly AMERICA_BOISE: TimeZone;
    /** IANA Time Zone database entry for America/Cambridge_Bay.  UTC offset −07:00/−06:00. */
    static readonly AMERICA_CAMBRIDGE_BAY: TimeZone;
    /** IANA Time Zone database entry for America/Campo_Grande.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_CAMPO_GRANDE: TimeZone;
    /** IANA Time Zone database entry for America/Cancun.  UTC offset −05:00/−05:00. */
    static readonly AMERICA_CANCUN: TimeZone;
    /** IANA Time Zone database entry for America/Caracas.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_CARACAS: TimeZone;
    /** IANA Time Zone database entry for America/Cayenne.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_CAYENNE: TimeZone;
    /** IANA Time Zone database entry for America/Chicago.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_CHICAGO: TimeZone;
    /** IANA Time Zone database entry for America/Chihuahua.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_CHIHUAHUA: TimeZone;
    /** IANA Time Zone database entry for America/Ciudad_Juarez.  UTC offset −07:00/−06:00. */
    static readonly AMERICA_CIUDAD_JUAREZ: TimeZone;
    /** IANA Time Zone database entry for America/Costa_Rica.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_COSTA_RICA: TimeZone;
    /** IANA Time Zone database entry for America/Cuiaba.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_CUIABA: TimeZone;
    /** IANA Time Zone database entry for America/Danmarkshavn.  UTC offset +00:00/+00:00. */
    static readonly AMERICA_DANMARKSHAVN: TimeZone;
    /** IANA Time Zone database entry for America/Dawson.  UTC offset −07:00/−07:00. */
    static readonly AMERICA_DAWSON: TimeZone;
    /** IANA Time Zone database entry for America/Dawson_Creek.  UTC offset −07:00/−07:00. */
    static readonly AMERICA_DAWSON_CREEK: TimeZone;
    /** IANA Time Zone database entry for America/Denver.  UTC offset −07:00/−06:00. */
    static readonly AMERICA_DENVER: TimeZone;
    /** IANA Time Zone database entry for America/Detroit.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_DETROIT: TimeZone;
    /** IANA Time Zone database entry for America/Edmonton.  UTC offset −07:00/−06:00. */
    static readonly AMERICA_EDMONTON: TimeZone;
    /** IANA Time Zone database entry for America/Eirunepe.  UTC offset −05:00/−05:00. */
    static readonly AMERICA_EIRUNEPE: TimeZone;
    /** IANA Time Zone database entry for America/El_Salvador.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_EL_SALVADOR: TimeZone;
    /** IANA Time Zone database entry for America/Fort_Nelson.  UTC offset −07:00/−07:00. */
    static readonly AMERICA_FORT_NELSON: TimeZone;
    /** IANA Time Zone database entry for America/Fortaleza.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_FORTALEZA: TimeZone;
    /** IANA Time Zone database entry for America/Glace_Bay.  UTC offset −04:00/−03:00. */
    static readonly AMERICA_GLACE_BAY: TimeZone;
    /** IANA Time Zone database entry for America/Goose_Bay.  UTC offset −04:00/−03:00. */
    static readonly AMERICA_GOOSE_BAY: TimeZone;
    /** IANA Time Zone database entry for America/Grand_Turk.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_GRAND_TURK: TimeZone;
    /** IANA Time Zone database entry for America/Guatemala.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_GUATEMALA: TimeZone;
    /** IANA Time Zone database entry for America/Guayaquil.  UTC offset −05:00/−05:00. */
    static readonly AMERICA_GUAYAQUIL: TimeZone;
    /** IANA Time Zone database entry for America/Guyana.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_GUYANA: TimeZone;
    /** IANA Time Zone database entry for America/Halifax.  UTC offset −04:00/−03:00. */
    static readonly AMERICA_HALIFAX: TimeZone;
    /** IANA Time Zone database entry for America/Havana.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_HAVANA: TimeZone;
    /** IANA Time Zone database entry for America/Hermosillo.  UTC offset −07:00/−07:00. */
    static readonly AMERICA_HERMOSILLO: TimeZone;
    /** IANA Time Zone database entry for America/Indiana/Indianapolis.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_INDIANA_INDIANAPOLIS: TimeZone;
    /** IANA Time Zone database entry for America/Indiana/Knox.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_INDIANA_KNOX: TimeZone;
    /** IANA Time Zone database entry for America/Indiana/Marengo.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_INDIANA_MARENGO: TimeZone;
    /** IANA Time Zone database entry for America/Indiana/Petersburg.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_INDIANA_PETERSBURG: TimeZone;
    /** IANA Time Zone database entry for America/Indiana/Tell_City.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_INDIANA_TELL_CITY: TimeZone;
    /** IANA Time Zone database entry for America/Indiana/Vevay.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_INDIANA_VEVAY: TimeZone;
    /** IANA Time Zone database entry for America/Indiana/Vincennes.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_INDIANA_VINCENNES: TimeZone;
    /** IANA Time Zone database entry for America/Indiana/Winamac.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_INDIANA_WINAMAC: TimeZone;
    /** IANA Time Zone database entry for America/Inuvik.  UTC offset −07:00/−06:00. */
    static readonly AMERICA_INUVIK: TimeZone;
    /** IANA Time Zone database entry for America/Iqaluit.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_IQALUIT: TimeZone;
    /** IANA Time Zone database entry for America/Jamaica.  UTC offset −05:00/−05:00. */
    static readonly AMERICA_JAMAICA: TimeZone;
    /** IANA Time Zone database entry for America/Juneau.  UTC offset −09:00/−08:00. */
    static readonly AMERICA_JUNEAU: TimeZone;
    /** IANA Time Zone database entry for America/Kentucky/Louisville.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_KENTUCKY_LOUISVILLE: TimeZone;
    /** IANA Time Zone database entry for America/Kentucky/Monticello.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_KENTUCKY_MONTICELLO: TimeZone;
    /** IANA Time Zone database entry for America/La_Paz.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_LA_PAZ: TimeZone;
    /** IANA Time Zone database entry for America/Lima.  UTC offset −05:00/−05:00. */
    static readonly AMERICA_LIMA: TimeZone;
    /** IANA Time Zone database entry for America/Los_Angeles.  UTC offset −08:00/−07:00. */
    static readonly AMERICA_LOS_ANGELES: TimeZone;
    /** IANA Time Zone database entry for America/Maceio.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_MACEIO: TimeZone;
    /** IANA Time Zone database entry for America/Managua.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_MANAGUA: TimeZone;
    /** IANA Time Zone database entry for America/Manaus.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_MANAUS: TimeZone;
    /** IANA Time Zone database entry for America/Martinique.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_MARTINIQUE: TimeZone;
    /** IANA Time Zone database entry for America/Matamoros.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_MATAMOROS: TimeZone;
    /** IANA Time Zone database entry for America/Mazatlan.  UTC offset −07:00/−07:00. */
    static readonly AMERICA_MAZATLAN: TimeZone;
    /** IANA Time Zone database entry for America/Menominee.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_MENOMINEE: TimeZone;
    /** IANA Time Zone database entry for America/Merida.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_MERIDA: TimeZone;
    /** IANA Time Zone database entry for America/Metlakatla.  UTC offset −09:00/−08:00. */
    static readonly AMERICA_METLAKATLA: TimeZone;
    /** IANA Time Zone database entry for America/Mexico_City.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_MEXICO_CITY: TimeZone;
    /** IANA Time Zone database entry for America/Miquelon.  UTC offset −03:00/−02:00. */
    static readonly AMERICA_MIQUELON: TimeZone;
    /** IANA Time Zone database entry for America/Moncton.  UTC offset −04:00/−03:00. */
    static readonly AMERICA_MONCTON: TimeZone;
    /** IANA Time Zone database entry for America/Monterrey.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_MONTERREY: TimeZone;
    /** IANA Time Zone database entry for America/Montevideo.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_MONTEVIDEO: TimeZone;
    /** IANA Time Zone database entry for America/New_York.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_NEW_YORK: TimeZone;
    /** IANA Time Zone database entry for America/Nome.  UTC offset −09:00/−08:00. */
    static readonly AMERICA_NOME: TimeZone;
    /** IANA Time Zone database entry for America/Noronha.  UTC offset −02:00/−02:00. */
    static readonly AMERICA_NORONHA: TimeZone;
    /** IANA Time Zone database entry for America/North_Dakota/Beulah.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_NORTH_DAKOTA_BEULAH: TimeZone;
    /** IANA Time Zone database entry for America/North_Dakota/Center.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_NORTH_DAKOTA_CENTER: TimeZone;
    /** IANA Time Zone database entry for America/North_Dakota/New_Salem.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_NORTH_DAKOTA_NEW_SALEM: TimeZone;
    /** IANA Time Zone database entry for America/Nuuk.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_NUUK: TimeZone;
    /** IANA Time Zone database entry for America/Ojinaga.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_OJINAGA: TimeZone;
    /** IANA Time Zone database entry for America/Panama.  UTC offset −05:00/−05:00. */
    static readonly AMERICA_PANAMA: TimeZone;
    /** IANA Time Zone database entry for America/Paramaribo.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_PARAMARIBO: TimeZone;
    /** IANA Time Zone database entry for America/Phoenix.  UTC offset −07:00/−07:00. */
    static readonly AMERICA_PHOENIX: TimeZone;
    /** IANA Time Zone database entry for America/Port-au-Prince.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_PORT_MINUS_AU_MINUS_PRINCE: TimeZone;
    /** IANA Time Zone database entry for America/Porto_Velho.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_PORTO_VELHO: TimeZone;
    /** IANA Time Zone database entry for America/Puerto_Rico.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_PUERTO_RICO: TimeZone;
    /** IANA Time Zone database entry for America/Punta_Arenas.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_PUNTA_ARENAS: TimeZone;
    /** IANA Time Zone database entry for America/Rankin_Inlet.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_RANKIN_INLET: TimeZone;
    /** IANA Time Zone database entry for America/Recife.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_RECIFE: TimeZone;
    /** IANA Time Zone database entry for America/Regina.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_REGINA: TimeZone;
    /** IANA Time Zone database entry for America/Resolute.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_RESOLUTE: TimeZone;
    /** IANA Time Zone database entry for America/Rio_Branco.  UTC offset −05:00/−05:00. */
    static readonly AMERICA_RIO_BRANCO: TimeZone;
    /** IANA Time Zone database entry for America/Santarem.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_SANTAREM: TimeZone;
    /** IANA Time Zone database entry for America/Santiago.  UTC offset −04:00/−03:00. */
    static readonly AMERICA_SANTIAGO: TimeZone;
    /** IANA Time Zone database entry for America/Santo_Domingo.  UTC offset −04:00/−04:00. */
    static readonly AMERICA_SANTO_DOMINGO: TimeZone;
    /** IANA Time Zone database entry for America/Sao_Paulo.  UTC offset −03:00/−03:00. */
    static readonly AMERICA_SAO_PAULO: TimeZone;
    /** IANA Time Zone database entry for America/Scoresbysund.  UTC offset −01:00/+00:00. */
    static readonly AMERICA_SCORESBYSUND: TimeZone;
    /** IANA Time Zone database entry for America/Sitka.  UTC offset −09:00/−08:00. */
    static readonly AMERICA_SITKA: TimeZone;
    /** IANA Time Zone database entry for America/St_Johns.  UTC offset −03:30/−02:30. */
    static readonly AMERICA_ST_JOHNS: TimeZone;
    /** IANA Time Zone database entry for America/Swift_Current.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_SWIFT_CURRENT: TimeZone;
    /** IANA Time Zone database entry for America/Tegucigalpa.  UTC offset −06:00/−06:00. */
    static readonly AMERICA_TEGUCIGALPA: TimeZone;
    /** IANA Time Zone database entry for America/Thule.  UTC offset −04:00/−03:00. */
    static readonly AMERICA_THULE: TimeZone;
    /** IANA Time Zone database entry for America/Tijuana.  UTC offset −08:00/−07:00. */
    static readonly AMERICA_TIJUANA: TimeZone;
    /** IANA Time Zone database entry for America/Toronto.  UTC offset −05:00/−04:00. */
    static readonly AMERICA_TORONTO: TimeZone;
    /** IANA Time Zone database entry for America/Vancouver.  UTC offset −08:00/−07:00. */
    static readonly AMERICA_VANCOUVER: TimeZone;
    /** IANA Time Zone database entry for America/Whitehorse.  UTC offset −07:00/−07:00. */
    static readonly AMERICA_WHITEHORSE: TimeZone;
    /** IANA Time Zone database entry for America/Winnipeg.  UTC offset −06:00/−05:00. */
    static readonly AMERICA_WINNIPEG: TimeZone;
    /** IANA Time Zone database entry for America/Yakutat.  UTC offset −09:00/−08:00. */
    static readonly AMERICA_YAKUTAT: TimeZone;
    /** IANA Time Zone database entry for America/Yellowknife.  UTC offset −07:00/−06:00. */
    static readonly AMERICA_YELLOWKNIFE: TimeZone;
    /** IANA Time Zone database entry for Antarctica/Casey.  UTC offset +11:00/+11:00. */
    static readonly ANTARCTICA_CASEY: TimeZone;
    /** IANA Time Zone database entry for Antarctica/Davis.  UTC offset +07:00/+07:00. */
    static readonly ANTARCTICA_DAVIS: TimeZone;
    /** IANA Time Zone database entry for Antarctica/Macquarie.  UTC offset +10:00/+11:00. */
    static readonly ANTARCTICA_MACQUARIE: TimeZone;
    /** IANA Time Zone database entry for Antarctica/Mawson.  UTC offset +05:00/+05:00. */
    static readonly ANTARCTICA_MAWSON: TimeZone;
    /** IANA Time Zone database entry for Antarctica/Palmer.  UTC offset −03:00/−03:00. */
    static readonly ANTARCTICA_PALMER: TimeZone;
    /** IANA Time Zone database entry for Antarctica/Rothera.  UTC offset −03:00/−03:00. */
    static readonly ANTARCTICA_ROTHERA: TimeZone;
    /** IANA Time Zone database entry for Antarctica/Troll.  UTC offset +00:00/+02:00. */
    static readonly ANTARCTICA_TROLL: TimeZone;
    /** IANA Time Zone database entry for Asia/Almaty.  UTC offset +06:00/+06:00. */
    static readonly ASIA_ALMATY: TimeZone;
    /** IANA Time Zone database entry for Asia/Amman.  UTC offset +03:00/+03:00. */
    static readonly ASIA_AMMAN: TimeZone;
    /** IANA Time Zone database entry for Asia/Anadyr.  UTC offset +12:00/+12:00. */
    static readonly ASIA_ANADYR: TimeZone;
    /** IANA Time Zone database entry for Asia/Aqtau.  UTC offset +05:00/+05:00. */
    static readonly ASIA_AQTAU: TimeZone;
    /** IANA Time Zone database entry for Asia/Aqtobe.  UTC offset +05:00/+05:00. */
    static readonly ASIA_AQTOBE: TimeZone;
    /** IANA Time Zone database entry for Asia/Ashgabat.  UTC offset +05:00/+05:00. */
    static readonly ASIA_ASHGABAT: TimeZone;
    /** IANA Time Zone database entry for Asia/Atyrau.  UTC offset +05:00/+05:00. */
    static readonly ASIA_ATYRAU: TimeZone;
    /** IANA Time Zone database entry for Asia/Baghdad.  UTC offset +03:00/+03:00. */
    static readonly ASIA_BAGHDAD: TimeZone;
    /** IANA Time Zone database entry for Asia/Baku.  UTC offset +04:00/+04:00. */
    static readonly ASIA_BAKU: TimeZone;
    /** IANA Time Zone database entry for Asia/Bangkok.  UTC offset +07:00/+07:00. */
    static readonly ASIA_BANGKOK: TimeZone;
    /** IANA Time Zone database entry for Asia/Barnaul.  UTC offset +07:00/+07:00. */
    static readonly ASIA_BARNAUL: TimeZone;
    /** IANA Time Zone database entry for Asia/Beirut.  UTC offset +02:00/+03:00. */
    static readonly ASIA_BEIRUT: TimeZone;
    /** IANA Time Zone database entry for Asia/Bishkek.  UTC offset +06:00/+06:00. */
    static readonly ASIA_BISHKEK: TimeZone;
    /** IANA Time Zone database entry for Asia/Chita.  UTC offset +09:00/+09:00. */
    static readonly ASIA_CHITA: TimeZone;
    /** IANA Time Zone database entry for Asia/Choibalsan.  UTC offset +08:00/+08:00. */
    static readonly ASIA_CHOIBALSAN: TimeZone;
    /** IANA Time Zone database entry for Asia/Colombo.  UTC offset +05:30/+05:30. */
    static readonly ASIA_COLOMBO: TimeZone;
    /** IANA Time Zone database entry for Asia/Damascus.  UTC offset +03:00/+03:00. */
    static readonly ASIA_DAMASCUS: TimeZone;
    /** IANA Time Zone database entry for Asia/Dhaka.  UTC offset +06:00/+06:00. */
    static readonly ASIA_DHAKA: TimeZone;
    /** IANA Time Zone database entry for Asia/Dili.  UTC offset +09:00/+09:00. */
    static readonly ASIA_DILI: TimeZone;
    /** IANA Time Zone database entry for Asia/Dubai.  UTC offset +04:00/+04:00. */
    static readonly ASIA_DUBAI: TimeZone;
    /** IANA Time Zone database entry for Asia/Dushanbe.  UTC offset +05:00/+05:00. */
    static readonly ASIA_DUSHANBE: TimeZone;
    /** IANA Time Zone database entry for Asia/Famagusta.  UTC offset +02:00/+03:00. */
    static readonly ASIA_FAMAGUSTA: TimeZone;
    /** IANA Time Zone database entry for Asia/Gaza.  UTC offset +02:00/+03:00. */
    static readonly ASIA_GAZA: TimeZone;
    /** IANA Time Zone database entry for Asia/Hebron.  UTC offset +02:00/+03:00. */
    static readonly ASIA_HEBRON: TimeZone;
    /** IANA Time Zone database entry for Asia/Ho_Chi_Minh.  UTC offset +07:00/+07:00. */
    static readonly ASIA_HO_CHI_MINH: TimeZone;
    /** IANA Time Zone database entry for Asia/Hong_Kong.  UTC offset +08:00/+08:00. */
    static readonly ASIA_HONG_KONG: TimeZone;
    /** IANA Time Zone database entry for Asia/Hovd.  UTC offset +07:00/+07:00. */
    static readonly ASIA_HOVD: TimeZone;
    /** IANA Time Zone database entry for Asia/Irkutsk.  UTC offset +08:00/+08:00. */
    static readonly ASIA_IRKUTSK: TimeZone;
    /** IANA Time Zone database entry for Asia/Jakarta.  UTC offset +07:00/+07:00. */
    static readonly ASIA_JAKARTA: TimeZone;
    /** IANA Time Zone database entry for Asia/Jayapura.  UTC offset +09:00/+09:00. */
    static readonly ASIA_JAYAPURA: TimeZone;
    /** IANA Time Zone database entry for Asia/Jerusalem.  UTC offset +02:00/+03:00. */
    static readonly ASIA_JERUSALEM: TimeZone;
    /** IANA Time Zone database entry for Asia/Kabul.  UTC offset +04:30/+04:30. */
    static readonly ASIA_KABUL: TimeZone;
    /** IANA Time Zone database entry for Asia/Kamchatka.  UTC offset +12:00/+12:00. */
    static readonly ASIA_KAMCHATKA: TimeZone;
    /** IANA Time Zone database entry for Asia/Karachi.  UTC offset +05:00/+05:00. */
    static readonly ASIA_KARACHI: TimeZone;
    /** IANA Time Zone database entry for Asia/Kathmandu.  UTC offset +05:45/+05:45. */
    static readonly ASIA_KATHMANDU: TimeZone;
    /** IANA Time Zone database entry for Asia/Khandyga.  UTC offset +09:00/+09:00. */
    static readonly ASIA_KHANDYGA: TimeZone;
    /** IANA Time Zone database entry for Asia/Kolkata.  UTC offset +05:30/+05:30. */
    static readonly ASIA_KOLKATA: TimeZone;
    /** IANA Time Zone database entry for Asia/Krasnoyarsk.  UTC offset +07:00/+07:00. */
    static readonly ASIA_KRASNOYARSK: TimeZone;
    /** IANA Time Zone database entry for Asia/Kuching.  UTC offset +08:00/+08:00. */
    static readonly ASIA_KUCHING: TimeZone;
    /** IANA Time Zone database entry for Asia/Macau.  UTC offset +08:00/+08:00. */
    static readonly ASIA_MACAU: TimeZone;
    /** IANA Time Zone database entry for Asia/Magadan.  UTC offset +11:00/+11:00. */
    static readonly ASIA_MAGADAN: TimeZone;
    /** IANA Time Zone database entry for Asia/Makassar.  UTC offset +08:00/+08:00. */
    static readonly ASIA_MAKASSAR: TimeZone;
    /** IANA Time Zone database entry for Asia/Manila.  UTC offset +08:00/+08:00. */
    static readonly ASIA_MANILA: TimeZone;
    /** IANA Time Zone database entry for Asia/Nicosia.  UTC offset +02:00/+03:00. */
    static readonly ASIA_NICOSIA: TimeZone;
    /** IANA Time Zone database entry for Asia/Novokuznetsk.  UTC offset +07:00/+07:00. */
    static readonly ASIA_NOVOKUZNETSK: TimeZone;
    /** IANA Time Zone database entry for Asia/Novosibirsk.  UTC offset +07:00/+07:00. */
    static readonly ASIA_NOVOSIBIRSK: TimeZone;
    /** IANA Time Zone database entry for Asia/Omsk.  UTC offset +06:00/+06:00. */
    static readonly ASIA_OMSK: TimeZone;
    /** IANA Time Zone database entry for Asia/Oral.  UTC offset +05:00/+05:00. */
    static readonly ASIA_ORAL: TimeZone;
    /** IANA Time Zone database entry for Asia/Pontianak.  UTC offset +07:00/+07:00. */
    static readonly ASIA_PONTIANAK: TimeZone;
    /** IANA Time Zone database entry for Asia/Pyongyang.  UTC offset +09:00/+09:00. */
    static readonly ASIA_PYONGYANG: TimeZone;
    /** IANA Time Zone database entry for Asia/Qatar.  UTC offset +03:00/+03:00. */
    static readonly ASIA_QATAR: TimeZone;
    /** IANA Time Zone database entry for Asia/Qostanay.  UTC offset +06:00/+06:00. */
    static readonly ASIA_QOSTANAY: TimeZone;
    /** IANA Time Zone database entry for Asia/Qyzylorda.  UTC offset +05:00/+05:00. */
    static readonly ASIA_QYZYLORDA: TimeZone;
    /** IANA Time Zone database entry for Asia/Riyadh.  UTC offset +03:00/+03:00. */
    static readonly ASIA_RIYADH: TimeZone;
    /** IANA Time Zone database entry for Asia/Sakhalin.  UTC offset +11:00/+11:00. */
    static readonly ASIA_SAKHALIN: TimeZone;
    /** IANA Time Zone database entry for Asia/Samarkand.  UTC offset +05:00/+05:00. */
    static readonly ASIA_SAMARKAND: TimeZone;
    /** IANA Time Zone database entry for Asia/Seoul.  UTC offset +09:00/+09:00. */
    static readonly ASIA_SEOUL: TimeZone;
    /** IANA Time Zone database entry for Asia/Shanghai.  UTC offset +08:00/+08:00. */
    static readonly ASIA_SHANGHAI: TimeZone;
    /** IANA Time Zone database entry for Asia/Singapore.  UTC offset +08:00/+08:00. */
    static readonly ASIA_SINGAPORE: TimeZone;
    /** IANA Time Zone database entry for Asia/Srednekolymsk.  UTC offset +11:00/+11:00. */
    static readonly ASIA_SREDNEKOLYMSK: TimeZone;
    /** IANA Time Zone database entry for Asia/Taipei.  UTC offset +08:00/+08:00. */
    static readonly ASIA_TAIPEI: TimeZone;
    /** IANA Time Zone database entry for Asia/Tashkent.  UTC offset +05:00/+05:00. */
    static readonly ASIA_TASHKENT: TimeZone;
    /** IANA Time Zone database entry for Asia/Tbilisi.  UTC offset +04:00/+04:00. */
    static readonly ASIA_TBILISI: TimeZone;
    /** IANA Time Zone database entry for Asia/Tehran.  UTC offset +03:30/+03:30. */
    static readonly ASIA_TEHRAN: TimeZone;
    /** IANA Time Zone database entry for Asia/Thimphu.  UTC offset +06:00/+06:00. */
    static readonly ASIA_THIMPHU: TimeZone;
    /** IANA Time Zone database entry for Asia/Tokyo.  UTC offset +09:00/+09:00. */
    static readonly ASIA_TOKYO: TimeZone;
    /** IANA Time Zone database entry for Asia/Tomsk.  UTC offset +07:00/+07:00. */
    static readonly ASIA_TOMSK: TimeZone;
    /** IANA Time Zone database entry for Asia/Ulaanbaatar.  UTC offset +08:00/+08:00. */
    static readonly ASIA_ULAANBAATAR: TimeZone;
    /** IANA Time Zone database entry for Asia/Urumqi.  UTC offset +06:00/+06:00. */
    static readonly ASIA_URUMQI: TimeZone;
    /** IANA Time Zone database entry for Asia/Ust-Nera.  UTC offset +10:00/+10:00. */
    static readonly ASIA_UST_MINUS_NERA: TimeZone;
    /** IANA Time Zone database entry for Asia/Vladivostok.  UTC offset +10:00/+10:00. */
    static readonly ASIA_VLADIVOSTOK: TimeZone;
    /** IANA Time Zone database entry for Asia/Yakutsk.  UTC offset +09:00/+09:00. */
    static readonly ASIA_YAKUTSK: TimeZone;
    /** IANA Time Zone database entry for Asia/Yangon.  UTC offset +06:30/+06:30. */
    static readonly ASIA_YANGON: TimeZone;
    /** IANA Time Zone database entry for Asia/Yekaterinburg.  UTC offset +05:00/+05:00. */
    static readonly ASIA_YEKATERINBURG: TimeZone;
    /** IANA Time Zone database entry for Asia/Yerevan.  UTC offset +04:00/+04:00. */
    static readonly ASIA_YEREVAN: TimeZone;
    /** IANA Time Zone database entry for Atlantic/Azores.  UTC offset −01:00/+00:00. */
    static readonly ATLANTIC_AZORES: TimeZone;
    /** IANA Time Zone database entry for Atlantic/Bermuda.  UTC offset −04:00/−03:00. */
    static readonly ATLANTIC_BERMUDA: TimeZone;
    /** IANA Time Zone database entry for Atlantic/Canary.  UTC offset +00:00/+01:00. */
    static readonly ATLANTIC_CANARY: TimeZone;
    /** IANA Time Zone database entry for Atlantic/Cape_Verde.  UTC offset −01:00/−01:00. */
    static readonly ATLANTIC_CAPE_VERDE: TimeZone;
    /** IANA Time Zone database entry for Atlantic/Faroe.  UTC offset +00:00/+01:00. */
    static readonly ATLANTIC_FAROE: TimeZone;
    /** IANA Time Zone database entry for Atlantic/Madeira.  UTC offset +00:00/+01:00. */
    static readonly ATLANTIC_MADEIRA: TimeZone;
    /** IANA Time Zone database entry for Atlantic/South_Georgia.  UTC offset −02:00/−02:00. */
    static readonly ATLANTIC_SOUTH_GEORGIA: TimeZone;
    /** IANA Time Zone database entry for Atlantic/Stanley.  UTC offset −03:00/−03:00. */
    static readonly ATLANTIC_STANLEY: TimeZone;
    /** IANA Time Zone database entry for Australia/Adelaide.  UTC offset +09:30/+10:30. */
    static readonly AUSTRALIA_ADELAIDE: TimeZone;
    /** IANA Time Zone database entry for Australia/Brisbane.  UTC offset +10:00/+10:00. */
    static readonly AUSTRALIA_BRISBANE: TimeZone;
    /** IANA Time Zone database entry for Australia/Broken_Hill.  UTC offset +09:30/+10:30. */
    static readonly AUSTRALIA_BROKEN_HILL: TimeZone;
    /** IANA Time Zone database entry for Australia/Darwin.  UTC offset +09:30/+09:30. */
    static readonly AUSTRALIA_DARWIN: TimeZone;
    /** IANA Time Zone database entry for Australia/Eucla.  UTC offset +08:45/+08:45. */
    static readonly AUSTRALIA_EUCLA: TimeZone;
    /** IANA Time Zone database entry for Australia/Hobart.  UTC offset +10:00/+11:00. */
    static readonly AUSTRALIA_HOBART: TimeZone;
    /** IANA Time Zone database entry for Australia/Lindeman.  UTC offset +10:00/+10:00. */
    static readonly AUSTRALIA_LINDEMAN: TimeZone;
    /** IANA Time Zone database entry for Australia/Lord_Howe.  UTC offset +10:30/+11:00. */
    static readonly AUSTRALIA_LORD_HOWE: TimeZone;
    /** IANA Time Zone database entry for Australia/Melbourne.  UTC offset +10:00/+11:00. */
    static readonly AUSTRALIA_MELBOURNE: TimeZone;
    /** IANA Time Zone database entry for Australia/Perth.  UTC offset +08:00/+08:00. */
    static readonly AUSTRALIA_PERTH: TimeZone;
    /** IANA Time Zone database entry for Australia/Sydney.  UTC offset +10:00/+11:00. */
    static readonly AUSTRALIA_SYDNEY: TimeZone;
    /** IANA Time Zone database entry for CET.  UTC offset +01:00/+02:00. */
    static readonly CET: TimeZone;
    /** IANA Time Zone database entry for CST6CDT.  UTC offset −06:00/−05:00. */
    static readonly CST6CDT: TimeZone;
    /** IANA Time Zone database entry for EET.  UTC offset +02:00/+03:00. */
    static readonly EET: TimeZone;
    /** IANA Time Zone database entry for EST.  UTC offset −05:00/−05:00. */
    static readonly EST: TimeZone;
    /** IANA Time Zone database entry for EST5EDT.  UTC offset −05:00/−04:00. */
    static readonly EST5EDT: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT.  UTC offset +00:00/+00:00. */
    static readonly ETC_GMT: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+1.  UTC offset −01:00/−01:00. */
    static readonly ETC_GMT_PLUS_1: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+10.  UTC offset −10:00/−10:00. */
    static readonly ETC_GMT_PLUS_10: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+11.  UTC offset −11:00/−11:00. */
    static readonly ETC_GMT_PLUS_11: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+12.  UTC offset −12:00/−12:00. */
    static readonly ETC_GMT_PLUS_12: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+2.  UTC offset −02:00/−02:00. */
    static readonly ETC_GMT_PLUS_2: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+3.  UTC offset −03:00/−03:00. */
    static readonly ETC_GMT_PLUS_3: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+4.  UTC offset −04:00/−04:00. */
    static readonly ETC_GMT_PLUS_4: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+5.  UTC offset −05:00/−05:00. */
    static readonly ETC_GMT_PLUS_5: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+6.  UTC offset −06:00/−06:00. */
    static readonly ETC_GMT_PLUS_6: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+7.  UTC offset −07:00/−07:00. */
    static readonly ETC_GMT_PLUS_7: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+8.  UTC offset −08:00/−08:00. */
    static readonly ETC_GMT_PLUS_8: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT+9.  UTC offset −09:00/−09:00. */
    static readonly ETC_GMT_PLUS_9: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-1.  UTC offset +01:00/+01:00. */
    static readonly ETC_GMT_MINUS_1: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-10.  UTC offset +10:00/+10:00. */
    static readonly ETC_GMT_MINUS_10: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-11.  UTC offset +11:00/+11:00. */
    static readonly ETC_GMT_MINUS_11: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-12.  UTC offset +12:00/+12:00. */
    static readonly ETC_GMT_MINUS_12: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-13.  UTC offset +13:00/+13:00. */
    static readonly ETC_GMT_MINUS_13: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-14.  UTC offset +14:00/+14:00. */
    static readonly ETC_GMT_MINUS_14: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-2.  UTC offset +02:00/+02:00. */
    static readonly ETC_GMT_MINUS_2: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-3.  UTC offset +03:00/+03:00. */
    static readonly ETC_GMT_MINUS_3: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-4.  UTC offset +04:00/+04:00. */
    static readonly ETC_GMT_MINUS_4: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-5.  UTC offset +05:00/+05:00. */
    static readonly ETC_GMT_MINUS_5: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-6.  UTC offset +06:00/+06:00. */
    static readonly ETC_GMT_MINUS_6: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-7.  UTC offset +07:00/+07:00. */
    static readonly ETC_GMT_MINUS_7: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-8.  UTC offset +08:00/+08:00. */
    static readonly ETC_GMT_MINUS_8: TimeZone;
    /** IANA Time Zone database entry for Etc/GMT-9.  UTC offset +09:00/+09:00. */
    static readonly ETC_GMT_MINUS_9: TimeZone;
    /** IANA Time Zone database entry for Etc/UTC.  UTC offset +00:00/+00:00. */
    static readonly ETC_UTC: TimeZone;
    /** IANA Time Zone database entry for Europe/Andorra.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_ANDORRA: TimeZone;
    /** IANA Time Zone database entry for Europe/Astrakhan.  UTC offset +04:00/+04:00. */
    static readonly EUROPE_ASTRAKHAN: TimeZone;
    /** IANA Time Zone database entry for Europe/Athens.  UTC offset +02:00/+03:00. */
    static readonly EUROPE_ATHENS: TimeZone;
    /** IANA Time Zone database entry for Europe/Belgrade.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_BELGRADE: TimeZone;
    /** IANA Time Zone database entry for Europe/Berlin.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_BERLIN: TimeZone;
    /** IANA Time Zone database entry for Europe/Brussels.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_BRUSSELS: TimeZone;
    /** IANA Time Zone database entry for Europe/Bucharest.  UTC offset +02:00/+03:00. */
    static readonly EUROPE_BUCHAREST: TimeZone;
    /** IANA Time Zone database entry for Europe/Budapest.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_BUDAPEST: TimeZone;
    /** IANA Time Zone database entry for Europe/Chisinau.  UTC offset +02:00/+03:00. */
    static readonly EUROPE_CHISINAU: TimeZone;
    /** IANA Time Zone database entry for Europe/Dublin.  UTC offset +01:00/+00:00. */
    static readonly EUROPE_DUBLIN: TimeZone;
    /** IANA Time Zone database entry for Europe/Gibraltar.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_GIBRALTAR: TimeZone;
    /** IANA Time Zone database entry for Europe/Helsinki.  UTC offset +02:00/+03:00. */
    static readonly EUROPE_HELSINKI: TimeZone;
    /** IANA Time Zone database entry for Europe/Istanbul.  UTC offset +03:00/+03:00. */
    static readonly EUROPE_ISTANBUL: TimeZone;
    /** IANA Time Zone database entry for Europe/Kaliningrad.  UTC offset +02:00/+02:00. */
    static readonly EUROPE_KALININGRAD: TimeZone;
    /** IANA Time Zone database entry for Europe/Kirov.  UTC offset +03:00/+03:00. */
    static readonly EUROPE_KIROV: TimeZone;
    /** IANA Time Zone database entry for Europe/Kyiv.  UTC offset +02:00/+03:00. */
    static readonly EUROPE_KYIV: TimeZone;
    /** IANA Time Zone database entry for Europe/Lisbon.  UTC offset +00:00/+01:00. */
    static readonly EUROPE_LISBON: TimeZone;
    /** IANA Time Zone database entry for Europe/London.  UTC offset +00:00/+01:00. */
    static readonly EUROPE_LONDON: TimeZone;
    /** IANA Time Zone database entry for Europe/Madrid.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_MADRID: TimeZone;
    /** IANA Time Zone database entry for Europe/Malta.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_MALTA: TimeZone;
    /** IANA Time Zone database entry for Europe/Minsk.  UTC offset +03:00/+03:00. */
    static readonly EUROPE_MINSK: TimeZone;
    /** IANA Time Zone database entry for Europe/Moscow.  UTC offset +03:00/+03:00. */
    static readonly EUROPE_MOSCOW: TimeZone;
    /** IANA Time Zone database entry for Europe/Paris.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_PARIS: TimeZone;
    /** IANA Time Zone database entry for Europe/Prague.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_PRAGUE: TimeZone;
    /** IANA Time Zone database entry for Europe/Riga.  UTC offset +02:00/+03:00. */
    static readonly EUROPE_RIGA: TimeZone;
    /** IANA Time Zone database entry for Europe/Rome.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_ROME: TimeZone;
    /** IANA Time Zone database entry for Europe/Samara.  UTC offset +04:00/+04:00. */
    static readonly EUROPE_SAMARA: TimeZone;
    /** IANA Time Zone database entry for Europe/Saratov.  UTC offset +04:00/+04:00. */
    static readonly EUROPE_SARATOV: TimeZone;
    /** IANA Time Zone database entry for Europe/Simferopol.  UTC offset +03:00/+03:00. */
    static readonly EUROPE_SIMFEROPOL: TimeZone;
    /** IANA Time Zone database entry for Europe/Sofia.  UTC offset +02:00/+03:00. */
    static readonly EUROPE_SOFIA: TimeZone;
    /** IANA Time Zone database entry for Europe/Tallinn.  UTC offset +02:00/+03:00. */
    static readonly EUROPE_TALLINN: TimeZone;
    /** IANA Time Zone database entry for Europe/Tirane.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_TIRANE: TimeZone;
    /** IANA Time Zone database entry for Europe/Ulyanovsk.  UTC offset +04:00/+04:00. */
    static readonly EUROPE_ULYANOVSK: TimeZone;
    /** IANA Time Zone database entry for Europe/Vienna.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_VIENNA: TimeZone;
    /** IANA Time Zone database entry for Europe/Vilnius.  UTC offset +02:00/+03:00. */
    static readonly EUROPE_VILNIUS: TimeZone;
    /** IANA Time Zone database entry for Europe/Volgograd.  UTC offset +03:00/+03:00. */
    static readonly EUROPE_VOLGOGRAD: TimeZone;
    /** IANA Time Zone database entry for Europe/Warsaw.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_WARSAW: TimeZone;
    /** IANA Time Zone database entry for Europe/Zurich.  UTC offset +01:00/+02:00. */
    static readonly EUROPE_ZURICH: TimeZone;
    /** IANA Time Zone database entry for Factory.  UTC offset +00:00/+00:00. */
    static readonly FACTORY: TimeZone;
    /** IANA Time Zone database entry for HST.  UTC offset −10:00/−10:00. */
    static readonly HST: TimeZone;
    /** IANA Time Zone database entry for Indian/Chagos.  UTC offset +06:00/+06:00. */
    static readonly INDIAN_CHAGOS: TimeZone;
    /** IANA Time Zone database entry for Indian/Maldives.  UTC offset +05:00/+05:00. */
    static readonly INDIAN_MALDIVES: TimeZone;
    /** IANA Time Zone database entry for Indian/Mauritius.  UTC offset +04:00/+04:00. */
    static readonly INDIAN_MAURITIUS: TimeZone;
    /** IANA Time Zone database entry for MET.  UTC offset +01:00/+02:00. */
    static readonly MET: TimeZone;
    /** IANA Time Zone database entry for MST.  UTC offset −07:00/−07:00. */
    static readonly MST: TimeZone;
    /** IANA Time Zone database entry for MST7MDT.  UTC offset −07:00/−06:00. */
    static readonly MST7MDT: TimeZone;
    /** IANA Time Zone database entry for Pacific/Apia.  UTC offset +13:00/+13:00. */
    static readonly PACIFIC_APIA: TimeZone;
    /** IANA Time Zone database entry for Pacific/Auckland.  UTC offset +12:00/+13:00. */
    static readonly PACIFIC_AUCKLAND: TimeZone;
    /** IANA Time Zone database entry for Pacific/Bougainville.  UTC offset +11:00/+11:00. */
    static readonly PACIFIC_BOUGAINVILLE: TimeZone;
    /** IANA Time Zone database entry for Pacific/Chatham.  UTC offset +12:45/+13:45. */
    static readonly PACIFIC_CHATHAM: TimeZone;
    /** IANA Time Zone database entry for Pacific/Easter.  UTC offset −06:00/−05:00. */
    static readonly PACIFIC_EASTER: TimeZone;
    /** IANA Time Zone database entry for Pacific/Efate.  UTC offset +11:00/+11:00. */
    static readonly PACIFIC_EFATE: TimeZone;
    /** IANA Time Zone database entry for Pacific/Fakaofo.  UTC offset +13:00/+13:00. */
    static readonly PACIFIC_FAKAOFO: TimeZone;
    /** IANA Time Zone database entry for Pacific/Fiji.  UTC offset +12:00/+12:00. */
    static readonly PACIFIC_FIJI: TimeZone;
    /** IANA Time Zone database entry for Pacific/Galapagos.  UTC offset −06:00/−06:00. */
    static readonly PACIFIC_GALAPAGOS: TimeZone;
    /** IANA Time Zone database entry for Pacific/Gambier.  UTC offset −09:00/−09:00. */
    static readonly PACIFIC_GAMBIER: TimeZone;
    /** IANA Time Zone database entry for Pacific/Guadalcanal.  UTC offset +11:00/+11:00. */
    static readonly PACIFIC_GUADALCANAL: TimeZone;
    /** IANA Time Zone database entry for Pacific/Guam.  UTC offset +10:00/+10:00. */
    static readonly PACIFIC_GUAM: TimeZone;
    /** IANA Time Zone database entry for Pacific/Honolulu.  UTC offset −10:00/−10:00. */
    static readonly PACIFIC_HONOLULU: TimeZone;
    /** IANA Time Zone database entry for Pacific/Kanton.  UTC offset +13:00/+13:00. */
    static readonly PACIFIC_KANTON: TimeZone;
    /** IANA Time Zone database entry for Pacific/Kiritimati.  UTC offset +14:00/+14:00. */
    static readonly PACIFIC_KIRITIMATI: TimeZone;
    /** IANA Time Zone database entry for Pacific/Kosrae.  UTC offset +11:00/+11:00. */
    static readonly PACIFIC_KOSRAE: TimeZone;
    /** IANA Time Zone database entry for Pacific/Kwajalein.  UTC offset +12:00/+12:00. */
    static readonly PACIFIC_KWAJALEIN: TimeZone;
    /** IANA Time Zone database entry for Pacific/Marquesas.  UTC offset −09:30/−09:30. */
    static readonly PACIFIC_MARQUESAS: TimeZone;
    /** IANA Time Zone database entry for Pacific/Nauru.  UTC offset +12:00/+12:00. */
    static readonly PACIFIC_NAURU: TimeZone;
    /** IANA Time Zone database entry for Pacific/Niue.  UTC offset −11:00/−11:00. */
    static readonly PACIFIC_NIUE: TimeZone;
    /** IANA Time Zone database entry for Pacific/Norfolk.  UTC offset +11:00/+12:00. */
    static readonly PACIFIC_NORFOLK: TimeZone;
    /** IANA Time Zone database entry for Pacific/Noumea.  UTC offset +11:00/+11:00. */
    static readonly PACIFIC_NOUMEA: TimeZone;
    /** IANA Time Zone database entry for Pacific/Pago_Pago.  UTC offset −11:00/−11:00. */
    static readonly PACIFIC_PAGO_PAGO: TimeZone;
    /** IANA Time Zone database entry for Pacific/Palau.  UTC offset +09:00/+09:00. */
    static readonly PACIFIC_PALAU: TimeZone;
    /** IANA Time Zone database entry for Pacific/Pitcairn.  UTC offset −08:00/−08:00. */
    static readonly PACIFIC_PITCAIRN: TimeZone;
    /** IANA Time Zone database entry for Pacific/Port_Moresby.  UTC offset +10:00/+10:00. */
    static readonly PACIFIC_PORT_MORESBY: TimeZone;
    /** IANA Time Zone database entry for Pacific/Rarotonga.  UTC offset −10:00/−10:00. */
    static readonly PACIFIC_RAROTONGA: TimeZone;
    /** IANA Time Zone database entry for Pacific/Tahiti.  UTC offset −10:00/−10:00. */
    static readonly PACIFIC_TAHITI: TimeZone;
    /** IANA Time Zone database entry for Pacific/Tarawa.  UTC offset +12:00/+12:00. */
    static readonly PACIFIC_TARAWA: TimeZone;
    /** IANA Time Zone database entry for Pacific/Tongatapu.  UTC offset +13:00/+13:00. */
    static readonly PACIFIC_TONGATAPU: TimeZone;
    /** IANA Time Zone database entry for PST8PDT.  UTC offset −08:00/−07:00. */
    static readonly PST8PDT: TimeZone;
    /** IANA Time Zone database entry for WET.  UTC offset +00:00/+01:00. */
    static readonly WET: TimeZone;
    /**
     * Use this to add a timezone not in this class.
     *
     * @param timezoneName the name of the timezone
     * @returns a new Timezone
     */
    static of(timezoneName: string): TimeZone;
    /**
     *
     * @param timezoneName The name of the timezone
     */
    private constructor();
}
