"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TelemetrySession = void 0;
const crypto_1 = require("crypto");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const installation_id_1 = require("./installation-id");
const library_version_1 = require("./library-version");
const sanitation_1 = require("./sanitation");
const schema_1 = require("./schema");
const ci_systems_1 = require("../ci-systems");
const messages_1 = require("../telemetry/messages");
const ci_1 = require("../util/ci");
const version_1 = require("../version");
const ABORTED_ERROR_MESSAGE = '__CDK-Toolkit__Aborted';
class TelemetrySession {
    constructor(props) {
        this.props = props;
        this.count = 0;
        this.ioHost = props.ioHost;
        this.client = props.client;
    }
    async begin() {
        // sanitize the raw cli input
        const { path, parameters } = (0, sanitation_1.sanitizeCommandLineArguments)(this.props.arguments);
        this._sessionInfo = {
            identifiers: {
                installationId: await (0, installation_id_1.getOrCreateInstallationId)(this.ioHost.asIoHelper()),
                sessionId: (0, crypto_1.randomUUID)(),
                telemetryVersion: '1.0',
                cdkCliVersion: (0, version_1.versionNumber)(),
                cdkLibraryVersion: await (0, library_version_1.getLibraryVersion)(this.ioHost.asIoHelper()),
            },
            event: {
                command: {
                    path,
                    parameters,
                    config: {
                        context: (0, sanitation_1.sanitizeContext)(this.props.context),
                    },
                },
            },
            environment: {
                ci: (0, ci_1.isCI)() || Boolean((0, ci_systems_1.detectCiSystem)()),
                os: {
                    platform: process.platform,
                    release: process.release.name,
                },
                nodeVersion: process.version,
            },
            project: {},
        };
        // If SIGINT has a listener installed, its default behavior will be removed (Node.js will no longer exit).
        // This ensures that on SIGINT we process safely close the telemetry session before exiting.
        process.on('SIGINT', async () => {
            try {
                await this.end({
                    name: schema_1.ErrorName.TOOLKIT_ERROR,
                    message: ABORTED_ERROR_MESSAGE,
                });
            }
            catch (e) {
                await this.ioHost.defaults.trace(`Ending Telemetry failed: ${e.message}`);
            }
            process.exit(1);
        });
        // Begin the session span
        this.span = await this.ioHost.asIoHelper().span(messages_1.CLI_PRIVATE_SPAN.COMMAND).begin({});
    }
    async attachRegion(region) {
        this.sessionInfo.identifiers = {
            ...this.sessionInfo.identifiers,
            region,
        };
    }
    /**
     * When the command is complete, so is the CliIoHost. Ends the span of the entire CliIoHost
     * and notifies with an optional error message in the data.
     */
    async end(error) {
        await this.span?.end({ error });
        // Ideally span.end() should no-op if called twice, but that is not the case right now
        this.span = undefined;
        await this.client.flush();
    }
    async emit(event) {
        this.count += 1;
        return this.client.emit({
            event: {
                command: this.sessionInfo.event.command,
                state: getState(event.error),
                eventType: event.eventType,
            },
            identifiers: {
                ...this.sessionInfo.identifiers,
                eventId: `${this.sessionInfo.identifiers.sessionId}:${this.count}`,
                timestamp: new Date().toISOString(),
            },
            environment: this.sessionInfo.environment,
            project: this.sessionInfo.project,
            duration: {
                total: event.duration,
            },
            ...(event.error ? {
                error: {
                    name: event.error.name,
                },
            } : {}),
        });
    }
    get sessionInfo() {
        if (!this._sessionInfo) {
            throw new toolkit_lib_1.ToolkitError('Session Info not initialized. Call begin() first.');
        }
        return this._sessionInfo;
    }
}
exports.TelemetrySession = TelemetrySession;
function getState(error) {
    if (error) {
        return isAbortedError(error) ? 'ABORTED' : 'FAILED';
    }
    return 'SUCCEEDED';
}
function isAbortedError(error) {
    if (error?.name === 'ToolkitError' && error?.message?.includes(ABORTED_ERROR_MESSAGE)) {
        return true;
    }
    return false;
}
//# sourceMappingURL=data:application/json;base64,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