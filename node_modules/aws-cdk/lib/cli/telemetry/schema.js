"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorName = void 0;
var ErrorName;
(function (ErrorName) {
    ErrorName["TOOLKIT_ERROR"] = "ToolkitError";
    ErrorName["AUTHENTICATION_ERROR"] = "AuthenticationError";
    ErrorName["ASSEMBLY_ERROR"] = "AssemblyError";
    ErrorName["CONTEXT_PROVIDER_ERROR"] = "ContextProviderError";
    ErrorName["UNKNOWN_ERROR"] = "UnknownError";
})(ErrorName || (exports.ErrorName = ErrorName = {}));
//# sourceMappingURL=data:application/json;base64,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