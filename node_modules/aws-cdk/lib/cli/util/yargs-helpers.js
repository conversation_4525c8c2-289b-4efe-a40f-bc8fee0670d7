"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isCI = void 0;
exports.yargsNegativeAlias = yargsNegativeAlias;
exports.cliVersion = cliVersion;
exports.browserForPlatform = browserForPlatform;
exports.shouldDisplayNotices = shouldDisplayNotices;
const ci_systems_1 = require("../ci-systems");
const ci_1 = require("../util/ci");
const version_1 = require("../version");
var ci_2 = require("../util/ci");
Object.defineProperty(exports, "isCI", { enumerable: true, get: function () { return ci_2.isCI; } });
/**
 * yargs middleware to negate an option if a negative alias is provided
 * E.g. `-R` will imply `--rollback=false`
 *
 * @param optionToNegate - The name of the option to negate, e.g. `rollback`
 * @param negativeAlias - The alias that should negate the option, e.g. `R`
 * @returns a middleware function that can be passed to yargs
 */
function yargsNegativeAlias(negativeAlias, optionToNegate) {
    return (argv) => {
        // if R in argv && argv[R]
        // then argv[rollback] = false
        if (negativeAlias in argv && argv[negativeAlias]) {
            argv[optionToNegate] = false;
        }
        return argv;
    };
}
/**
 * Returns the current version of the CLI
 * @returns the current version of the CLI
 */
function cliVersion() {
    return (0, version_1.versionWithBuild)();
}
/**
 * Returns the default browser command for the current platform
 * @returns the default browser command for the current platform
 */
function browserForPlatform() {
    switch (process.platform) {
        case 'darwin':
            return 'open %u';
        case 'win32':
            return 'start %u';
        default:
            return 'xdg-open %u';
    }
}
/**
 * The default value for displaying (and refreshing) notices on all commands.
 *
 * If the user didn't supply either `--notices` or `--no-notices`, we do
 * autodetection. The autodetection currently is: do write notices if we are
 * not on CI, or are on a CI system where we know that writing to stderr is
 * safe. We fail "closed"; that is, we decide to NOT print for unknown CI
 * systems, even though technically we maybe could.
 */
function shouldDisplayNotices() {
    return !(0, ci_1.isCI)() || Boolean((0, ci_systems_1.ciSystemIsStdErrSafe)());
}
//# sourceMappingURL=data:application/json;base64,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