"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;base64,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