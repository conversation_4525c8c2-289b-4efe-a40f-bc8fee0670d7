"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CloudExecutable = void 0;
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const cloud_assembly_1 = require("./cloud-assembly");
const api_private_1 = require("../../lib/api-private");
const singleton_plugin_host_1 = require("../cli/singleton-plugin-host");
const error_1 = require("../cli/telemetry/error");
const messages_1 = require("../cli/telemetry/messages");
const contextproviders = require("../context-providers");
/**
 * Represent the Cloud Executable and the synthesis we can do on it
 */
class CloudExecutable {
    constructor(props) {
        this.props = props;
    }
    async produce() {
        const synthesisResult = await this.synthesize(true);
        // We must return an `IReadableCloudAssembly` here, but this Cloud Assembly is only used in the context
        // of the CLI and `cli.ts` currently manages its own locking in the "synthesizer" callback function.
        //
        // All the lock-related functions are therefore no-ops.
        return new api_private_1.BorrowedAssembly(synthesisResult.assembly);
    }
    /**
     * Return whether there is an app command from the configuration
     */
    get hasApp() {
        return !!this.props.configuration.settings.get(['app']);
    }
    /**
     * Synthesize a set of stacks.
     *
     * @param cacheCloudAssembly - whether to cache the Cloud Assembly after it has been first synthesized.
     *   This is 'true' by default, and only set to 'false' for 'cdk watch',
     *   which needs to re-synthesize the Assembly each time it detects a change to the project files
     */
    async synthesize(cacheCloudAssembly = true) {
        if (!this._cloudAssembly || !cacheCloudAssembly) {
            this._cloudAssembly = await this.doSynthesize();
        }
        return this._cloudAssembly;
    }
    async doSynthesize() {
        // We may need to run the cloud executable multiple times in order to satisfy all missing context
        // (When the executable runs, it will tell us about context it wants to use
        // but it missing. We'll then look up the context and run the executable again, and
        // again, until it doesn't complain anymore or we've stopped making progress).
        let previouslyMissingKeys;
        const synthSpan = await this.props.ioHelper.span(messages_1.CLI_PRIVATE_SPAN.SYNTH_ASSEMBLY).begin({});
        let error;
        try {
            while (true) {
                const assembly = await this.props.synthesizer(this.props.sdkProvider, this.props.configuration);
                if (assembly.manifest.missing && assembly.manifest.missing.length > 0) {
                    const missingKeys = missingContextKeys(assembly.manifest.missing);
                    if (!this.canLookup) {
                        throw new toolkit_lib_1.ToolkitError('Context lookups have been disabled. '
                            + 'Make sure all necessary context is already in \'cdk.context.json\' by running \'cdk synth\' on a machine with sufficient AWS credentials and committing the result. '
                            + `Missing context keys: '${Array.from(missingKeys).join(', ')}'`);
                    }
                    let tryLookup = true;
                    if (previouslyMissingKeys && setsEqual(missingKeys, previouslyMissingKeys)) {
                        await this.props.ioHelper.defaults.debug('Not making progress trying to resolve environmental context. Giving up.');
                        tryLookup = false;
                    }
                    previouslyMissingKeys = missingKeys;
                    if (tryLookup) {
                        await this.props.ioHelper.defaults.debug('Some context information is missing. Fetching...');
                        const updates = await contextproviders.provideContextValues(assembly.manifest.missing, this.props.sdkProvider, singleton_plugin_host_1.GLOBAL_PLUGIN_HOST, this.props.ioHelper);
                        for (const [key, value] of Object.entries(updates)) {
                            this.props.configuration.context.set(key, value);
                        }
                        // Cache the new context to disk
                        await this.props.configuration.saveContext();
                        // Execute again
                        continue;
                    }
                }
                return new cloud_assembly_1.CloudAssembly(assembly, this.props.ioHelper);
            }
        }
        catch (e) {
            error = {
                name: (0, error_1.cdkCliErrorName)(e.name),
            };
            throw e;
        }
        finally {
            await synthSpan.end({ error });
        }
    }
    get canLookup() {
        return !!(this.props.configuration.settings.get(['lookups']) ?? true);
    }
}
exports.CloudExecutable = CloudExecutable;
/**
 * Return all keys of missing context items
 */
function missingContextKeys(missing) {
    return new Set((missing || []).map(m => m.key));
}
function setsEqual(a, b) {
    if (a.size !== b.size) {
        return false;
    }
    for (const x of a) {
        if (!b.has(x)) {
            return false;
        }
    }
    return true;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY2xvdWQtZXhlY3V0YWJsZS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbImNsb3VkLWV4ZWN1dGFibGUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQ0Esc0RBQW9EO0FBQ3BELHFEQUFpRDtBQUdqRCx1REFBeUQ7QUFFekQsd0VBQWtFO0FBQ2xFLGtEQUF5RDtBQUN6RCx3REFBNkQ7QUFHN0QseURBQXlEO0FBNkJ6RDs7R0FFRztBQUNILE1BQWEsZUFBZTtJQUcxQixZQUE2QixLQUEyQjtRQUEzQixVQUFLLEdBQUwsS0FBSyxDQUFzQjtJQUN4RCxDQUFDO0lBRU0sS0FBSyxDQUFDLE9BQU87UUFDbEIsTUFBTSxlQUFlLEdBQUcsTUFBTSxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBRXBELHVHQUF1RztRQUN2RyxvR0FBb0c7UUFDcEcsRUFBRTtRQUNGLHVEQUF1RDtRQUN2RCxPQUFPLElBQUksOEJBQWdCLENBQUMsZUFBZSxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ3hELENBQUM7SUFFRDs7T0FFRztJQUNILElBQVcsTUFBTTtRQUNmLE9BQU8sQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO0lBQzFELENBQUM7SUFFRDs7Ozs7O09BTUc7SUFDSSxLQUFLLENBQUMsVUFBVSxDQUFDLHFCQUE4QixJQUFJO1FBQ3hELElBQUksQ0FBQyxJQUFJLENBQUMsY0FBYyxJQUFJLENBQUMsa0JBQWtCLEVBQUUsQ0FBQztZQUNoRCxJQUFJLENBQUMsY0FBYyxHQUFHLE1BQU0sSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDO1FBQ2xELENBQUM7UUFDRCxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUM7SUFDN0IsQ0FBQztJQUVPLEtBQUssQ0FBQyxZQUFZO1FBQ3hCLGlHQUFpRztRQUNqRywyRUFBMkU7UUFDM0UsbUZBQW1GO1FBQ25GLDhFQUE4RTtRQUM5RSxJQUFJLHFCQUE4QyxDQUFDO1FBQ25ELE1BQU0sU0FBUyxHQUFHLE1BQU0sSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLDJCQUFnQixDQUFDLGNBQWMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUM1RixJQUFJLEtBQStCLENBQUM7UUFDcEMsSUFBSSxDQUFDO1lBQ0gsT0FBTyxJQUFJLEVBQUUsQ0FBQztnQkFDWixNQUFNLFFBQVEsR0FBRyxNQUFNLElBQUksQ0FBQyxLQUFLLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsV0FBVyxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLENBQUM7Z0JBRWhHLElBQUksUUFBUSxDQUFDLFFBQVEsQ0FBQyxPQUFPLElBQUksUUFBUSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO29CQUN0RSxNQUFNLFdBQVcsR0FBRyxrQkFBa0IsQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxDQUFDO29CQUVsRSxJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFDO3dCQUNwQixNQUFNLElBQUksMEJBQVksQ0FDcEIsc0NBQXNDOzhCQUNwQyxzS0FBc0s7OEJBQ3RLLDBCQUEwQixLQUFLLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUM7b0JBQ3ZFLENBQUM7b0JBRUQsSUFBSSxTQUFTLEdBQUcsSUFBSSxDQUFDO29CQUNyQixJQUFJLHFCQUFxQixJQUFJLFNBQVMsQ0FBQyxXQUFXLEVBQUUscUJBQXFCLENBQUMsRUFBRSxDQUFDO3dCQUMzRSxNQUFNLElBQUksQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMseUVBQXlFLENBQUMsQ0FBQzt3QkFDcEgsU0FBUyxHQUFHLEtBQUssQ0FBQztvQkFDcEIsQ0FBQztvQkFFRCxxQkFBcUIsR0FBRyxXQUFXLENBQUM7b0JBRXBDLElBQUksU0FBUyxFQUFFLENBQUM7d0JBQ2QsTUFBTSxJQUFJLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLGtEQUFrRCxDQUFDLENBQUM7d0JBRTdGLE1BQU0sT0FBTyxHQUFHLE1BQU0sZ0JBQWdCLENBQUMsb0JBQW9CLENBQ3pELFFBQVEsQ0FBQyxRQUFRLENBQUMsT0FBTyxFQUN6QixJQUFJLENBQUMsS0FBSyxDQUFDLFdBQVcsRUFDdEIsMENBQWtCLEVBQ2xCLElBQUksQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUNwQixDQUFDO3dCQUVGLEtBQUssTUFBTSxDQUFDLEdBQUcsRUFBRSxLQUFLLENBQUMsSUFBSSxNQUFNLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7NEJBQ25ELElBQUksQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsR0FBRyxFQUFFLEtBQUssQ0FBQyxDQUFDO3dCQUNuRCxDQUFDO3dCQUVELGdDQUFnQzt3QkFDaEMsTUFBTSxJQUFJLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxXQUFXLEVBQUUsQ0FBQzt3QkFFN0MsZ0JBQWdCO3dCQUNoQixTQUFTO29CQUNYLENBQUM7Z0JBQ0gsQ0FBQztnQkFDRCxPQUFPLElBQUksOEJBQWEsQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUMxRCxDQUFDO1FBQ0gsQ0FBQztRQUFDLE9BQU8sQ0FBTSxFQUFFLENBQUM7WUFDaEIsS0FBSyxHQUFHO2dCQUNOLElBQUksRUFBRSxJQUFBLHVCQUFlLEVBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQzthQUM5QixDQUFDO1lBQ0YsTUFBTSxDQUFDLENBQUM7UUFDVixDQUFDO2dCQUFTLENBQUM7WUFDVCxNQUFNLFNBQVMsQ0FBQyxHQUFHLENBQUMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDO1FBQ2pDLENBQUM7SUFDSCxDQUFDO0lBRUQsSUFBWSxTQUFTO1FBQ25CLE9BQU8sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxhQUFhLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLElBQUksSUFBSSxDQUFDLENBQUM7SUFDeEUsQ0FBQztDQUNGO0FBdkdELDBDQXVHQztBQUVEOztHQUVHO0FBQ0gsU0FBUyxrQkFBa0IsQ0FBQyxPQUFnQztJQUMxRCxPQUFPLElBQUksR0FBRyxDQUFDLENBQUMsT0FBTyxJQUFJLEVBQUUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO0FBQ2xELENBQUM7QUFFRCxTQUFTLFNBQVMsQ0FBSSxDQUFTLEVBQUUsQ0FBUztJQUN4QyxJQUFJLENBQUMsQ0FBQyxJQUFJLEtBQUssQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDO1FBQ3RCLE9BQU8sS0FBSyxDQUFDO0lBQ2YsQ0FBQztJQUNELEtBQUssTUFBTSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUM7UUFDbEIsSUFBSSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztZQUNkLE9BQU8sS0FBSyxDQUFDO1FBQ2YsQ0FBQztJQUNILENBQUM7SUFDRCxPQUFPLElBQUksQ0FBQztBQUNkLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSAqIGFzIGN4YXBpIGZyb20gJ0Bhd3MtY2RrL2N4LWFwaSc7XG5pbXBvcnQgeyBUb29sa2l0RXJyb3IgfSBmcm9tICdAYXdzLWNkay90b29sa2l0LWxpYic7XG5pbXBvcnQgeyBDbG91ZEFzc2VtYmx5IH0gZnJvbSAnLi9jbG91ZC1hc3NlbWJseSc7XG5pbXBvcnQgdHlwZSB7IElDbG91ZEFzc2VtYmx5U291cmNlLCBJUmVhZGFibGVDbG91ZEFzc2VtYmx5IH0gZnJvbSAnLi4vLi4vbGliL2FwaSc7XG5pbXBvcnQgdHlwZSB7IElvSGVscGVyIH0gZnJvbSAnLi4vLi4vbGliL2FwaS1wcml2YXRlJztcbmltcG9ydCB7IEJvcnJvd2VkQXNzZW1ibHkgfSBmcm9tICcuLi8uLi9saWIvYXBpLXByaXZhdGUnO1xuaW1wb3J0IHR5cGUgeyBTZGtQcm92aWRlciB9IGZyb20gJy4uL2FwaS9hd3MtYXV0aCc7XG5pbXBvcnQgeyBHTE9CQUxfUExVR0lOX0hPU1QgfSBmcm9tICcuLi9jbGkvc2luZ2xldG9uLXBsdWdpbi1ob3N0JztcbmltcG9ydCB7IGNka0NsaUVycm9yTmFtZSB9IGZyb20gJy4uL2NsaS90ZWxlbWV0cnkvZXJyb3InO1xuaW1wb3J0IHsgQ0xJX1BSSVZBVEVfU1BBTiB9IGZyb20gJy4uL2NsaS90ZWxlbWV0cnkvbWVzc2FnZXMnO1xuaW1wb3J0IHR5cGUgeyBFcnJvckRldGFpbHMgfSBmcm9tICcuLi9jbGkvdGVsZW1ldHJ5L3NjaGVtYSc7XG5pbXBvcnQgdHlwZSB7IENvbmZpZ3VyYXRpb24gfSBmcm9tICcuLi9jbGkvdXNlci1jb25maWd1cmF0aW9uJztcbmltcG9ydCAqIGFzIGNvbnRleHRwcm92aWRlcnMgZnJvbSAnLi4vY29udGV4dC1wcm92aWRlcnMnO1xuXG4vKipcbiAqIEByZXR1cm5zIG91dHB1dCBkaXJlY3RvcnlcbiAqL1xuZXhwb3J0IHR5cGUgU3ludGhlc2l6ZXIgPSAoYXdzOiBTZGtQcm92aWRlciwgY29uZmlnOiBDb25maWd1cmF0aW9uKSA9PiBQcm9taXNlPGN4YXBpLkNsb3VkQXNzZW1ibHk+O1xuXG5leHBvcnQgaW50ZXJmYWNlIENsb3VkRXhlY3V0YWJsZVByb3BzIHtcbiAgLyoqXG4gICAqIEFwcGxpY2F0aW9uIGNvbmZpZ3VyYXRpb24gKHNldHRpbmdzIGFuZCBjb250ZXh0KVxuICAgKi9cbiAgY29uZmlndXJhdGlvbjogQ29uZmlndXJhdGlvbjtcblxuICAvKipcbiAgICogQVdTIG9iamVjdCAodXNlZCBieSBzeW50aGVzaXplciBhbmQgY29udGV4dHByb3ZpZGVyKVxuICAgKi9cbiAgc2RrUHJvdmlkZXI6IFNka1Byb3ZpZGVyO1xuXG4gIC8qKlxuICAgKiBNZXNzYWdpbmcgaGVscGVyXG4gICAqL1xuICBpb0hlbHBlcjogSW9IZWxwZXI7XG5cbiAgLyoqXG4gICAqIENhbGxiYWNrIGludm9rZWQgdG8gc3ludGhlc2l6ZSB0aGUgYWN0dWFsIHN0YWNrc1xuICAgKi9cbiAgc3ludGhlc2l6ZXI6IFN5bnRoZXNpemVyO1xufVxuXG4vKipcbiAqIFJlcHJlc2VudCB0aGUgQ2xvdWQgRXhlY3V0YWJsZSBhbmQgdGhlIHN5bnRoZXNpcyB3ZSBjYW4gZG8gb24gaXRcbiAqL1xuZXhwb3J0IGNsYXNzIENsb3VkRXhlY3V0YWJsZSBpbXBsZW1lbnRzIElDbG91ZEFzc2VtYmx5U291cmNlIHtcbiAgcHJpdmF0ZSBfY2xvdWRBc3NlbWJseT86IENsb3VkQXNzZW1ibHk7XG5cbiAgY29uc3RydWN0b3IocHJpdmF0ZSByZWFkb25seSBwcm9wczogQ2xvdWRFeGVjdXRhYmxlUHJvcHMpIHtcbiAgfVxuXG4gIHB1YmxpYyBhc3luYyBwcm9kdWNlKCk6IFByb21pc2U8SVJlYWRhYmxlQ2xvdWRBc3NlbWJseT4ge1xuICAgIGNvbnN0IHN5bnRoZXNpc1Jlc3VsdCA9IGF3YWl0IHRoaXMuc3ludGhlc2l6ZSh0cnVlKTtcblxuICAgIC8vIFdlIG11c3QgcmV0dXJuIGFuIGBJUmVhZGFibGVDbG91ZEFzc2VtYmx5YCBoZXJlLCBidXQgdGhpcyBDbG91ZCBBc3NlbWJseSBpcyBvbmx5IHVzZWQgaW4gdGhlIGNvbnRleHRcbiAgICAvLyBvZiB0aGUgQ0xJIGFuZCBgY2xpLnRzYCBjdXJyZW50bHkgbWFuYWdlcyBpdHMgb3duIGxvY2tpbmcgaW4gdGhlIFwic3ludGhlc2l6ZXJcIiBjYWxsYmFjayBmdW5jdGlvbi5cbiAgICAvL1xuICAgIC8vIEFsbCB0aGUgbG9jay1yZWxhdGVkIGZ1bmN0aW9ucyBhcmUgdGhlcmVmb3JlIG5vLW9wcy5cbiAgICByZXR1cm4gbmV3IEJvcnJvd2VkQXNzZW1ibHkoc3ludGhlc2lzUmVzdWx0LmFzc2VtYmx5KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBSZXR1cm4gd2hldGhlciB0aGVyZSBpcyBhbiBhcHAgY29tbWFuZCBmcm9tIHRoZSBjb25maWd1cmF0aW9uXG4gICAqL1xuICBwdWJsaWMgZ2V0IGhhc0FwcCgpIHtcbiAgICByZXR1cm4gISF0aGlzLnByb3BzLmNvbmZpZ3VyYXRpb24uc2V0dGluZ3MuZ2V0KFsnYXBwJ10pO1xuICB9XG5cbiAgLyoqXG4gICAqIFN5bnRoZXNpemUgYSBzZXQgb2Ygc3RhY2tzLlxuICAgKlxuICAgKiBAcGFyYW0gY2FjaGVDbG91ZEFzc2VtYmx5IC0gd2hldGhlciB0byBjYWNoZSB0aGUgQ2xvdWQgQXNzZW1ibHkgYWZ0ZXIgaXQgaGFzIGJlZW4gZmlyc3Qgc3ludGhlc2l6ZWQuXG4gICAqICAgVGhpcyBpcyAndHJ1ZScgYnkgZGVmYXVsdCwgYW5kIG9ubHkgc2V0IHRvICdmYWxzZScgZm9yICdjZGsgd2F0Y2gnLFxuICAgKiAgIHdoaWNoIG5lZWRzIHRvIHJlLXN5bnRoZXNpemUgdGhlIEFzc2VtYmx5IGVhY2ggdGltZSBpdCBkZXRlY3RzIGEgY2hhbmdlIHRvIHRoZSBwcm9qZWN0IGZpbGVzXG4gICAqL1xuICBwdWJsaWMgYXN5bmMgc3ludGhlc2l6ZShjYWNoZUNsb3VkQXNzZW1ibHk6IGJvb2xlYW4gPSB0cnVlKTogUHJvbWlzZTxDbG91ZEFzc2VtYmx5PiB7XG4gICAgaWYgKCF0aGlzLl9jbG91ZEFzc2VtYmx5IHx8ICFjYWNoZUNsb3VkQXNzZW1ibHkpIHtcbiAgICAgIHRoaXMuX2Nsb3VkQXNzZW1ibHkgPSBhd2FpdCB0aGlzLmRvU3ludGhlc2l6ZSgpO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5fY2xvdWRBc3NlbWJseTtcbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgZG9TeW50aGVzaXplKCk6IFByb21pc2U8Q2xvdWRBc3NlbWJseT4ge1xuICAgIC8vIFdlIG1heSBuZWVkIHRvIHJ1biB0aGUgY2xvdWQgZXhlY3V0YWJsZSBtdWx0aXBsZSB0aW1lcyBpbiBvcmRlciB0byBzYXRpc2Z5IGFsbCBtaXNzaW5nIGNvbnRleHRcbiAgICAvLyAoV2hlbiB0aGUgZXhlY3V0YWJsZSBydW5zLCBpdCB3aWxsIHRlbGwgdXMgYWJvdXQgY29udGV4dCBpdCB3YW50cyB0byB1c2VcbiAgICAvLyBidXQgaXQgbWlzc2luZy4gV2UnbGwgdGhlbiBsb29rIHVwIHRoZSBjb250ZXh0IGFuZCBydW4gdGhlIGV4ZWN1dGFibGUgYWdhaW4sIGFuZFxuICAgIC8vIGFnYWluLCB1bnRpbCBpdCBkb2Vzbid0IGNvbXBsYWluIGFueW1vcmUgb3Igd2UndmUgc3RvcHBlZCBtYWtpbmcgcHJvZ3Jlc3MpLlxuICAgIGxldCBwcmV2aW91c2x5TWlzc2luZ0tleXM6IFNldDxzdHJpbmc+IHwgdW5kZWZpbmVkO1xuICAgIGNvbnN0IHN5bnRoU3BhbiA9IGF3YWl0IHRoaXMucHJvcHMuaW9IZWxwZXIuc3BhbihDTElfUFJJVkFURV9TUEFOLlNZTlRIX0FTU0VNQkxZKS5iZWdpbih7fSk7XG4gICAgbGV0IGVycm9yOiBFcnJvckRldGFpbHMgfCB1bmRlZmluZWQ7XG4gICAgdHJ5IHtcbiAgICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICAgIGNvbnN0IGFzc2VtYmx5ID0gYXdhaXQgdGhpcy5wcm9wcy5zeW50aGVzaXplcih0aGlzLnByb3BzLnNka1Byb3ZpZGVyLCB0aGlzLnByb3BzLmNvbmZpZ3VyYXRpb24pO1xuXG4gICAgICAgIGlmIChhc3NlbWJseS5tYW5pZmVzdC5taXNzaW5nICYmIGFzc2VtYmx5Lm1hbmlmZXN0Lm1pc3NpbmcubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGNvbnN0IG1pc3NpbmdLZXlzID0gbWlzc2luZ0NvbnRleHRLZXlzKGFzc2VtYmx5Lm1hbmlmZXN0Lm1pc3NpbmcpO1xuXG4gICAgICAgICAgaWYgKCF0aGlzLmNhbkxvb2t1cCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IFRvb2xraXRFcnJvcihcbiAgICAgICAgICAgICAgJ0NvbnRleHQgbG9va3VwcyBoYXZlIGJlZW4gZGlzYWJsZWQuICdcbiAgICAgICAgICAgICAgKyAnTWFrZSBzdXJlIGFsbCBuZWNlc3NhcnkgY29udGV4dCBpcyBhbHJlYWR5IGluIFxcJ2Nkay5jb250ZXh0Lmpzb25cXCcgYnkgcnVubmluZyBcXCdjZGsgc3ludGhcXCcgb24gYSBtYWNoaW5lIHdpdGggc3VmZmljaWVudCBBV1MgY3JlZGVudGlhbHMgYW5kIGNvbW1pdHRpbmcgdGhlIHJlc3VsdC4gJ1xuICAgICAgICAgICAgICArIGBNaXNzaW5nIGNvbnRleHQga2V5czogJyR7QXJyYXkuZnJvbShtaXNzaW5nS2V5cykuam9pbignLCAnKX0nYCk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgbGV0IHRyeUxvb2t1cCA9IHRydWU7XG4gICAgICAgICAgaWYgKHByZXZpb3VzbHlNaXNzaW5nS2V5cyAmJiBzZXRzRXF1YWwobWlzc2luZ0tleXMsIHByZXZpb3VzbHlNaXNzaW5nS2V5cykpIHtcbiAgICAgICAgICAgIGF3YWl0IHRoaXMucHJvcHMuaW9IZWxwZXIuZGVmYXVsdHMuZGVidWcoJ05vdCBtYWtpbmcgcHJvZ3Jlc3MgdHJ5aW5nIHRvIHJlc29sdmUgZW52aXJvbm1lbnRhbCBjb250ZXh0LiBHaXZpbmcgdXAuJyk7XG4gICAgICAgICAgICB0cnlMb29rdXAgPSBmYWxzZTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBwcmV2aW91c2x5TWlzc2luZ0tleXMgPSBtaXNzaW5nS2V5cztcblxuICAgICAgICAgIGlmICh0cnlMb29rdXApIHtcbiAgICAgICAgICAgIGF3YWl0IHRoaXMucHJvcHMuaW9IZWxwZXIuZGVmYXVsdHMuZGVidWcoJ1NvbWUgY29udGV4dCBpbmZvcm1hdGlvbiBpcyBtaXNzaW5nLiBGZXRjaGluZy4uLicpO1xuXG4gICAgICAgICAgICBjb25zdCB1cGRhdGVzID0gYXdhaXQgY29udGV4dHByb3ZpZGVycy5wcm92aWRlQ29udGV4dFZhbHVlcyhcbiAgICAgICAgICAgICAgYXNzZW1ibHkubWFuaWZlc3QubWlzc2luZyxcbiAgICAgICAgICAgICAgdGhpcy5wcm9wcy5zZGtQcm92aWRlcixcbiAgICAgICAgICAgICAgR0xPQkFMX1BMVUdJTl9IT1NULFxuICAgICAgICAgICAgICB0aGlzLnByb3BzLmlvSGVscGVyLFxuICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXModXBkYXRlcykpIHtcbiAgICAgICAgICAgICAgdGhpcy5wcm9wcy5jb25maWd1cmF0aW9uLmNvbnRleHQuc2V0KGtleSwgdmFsdWUpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBDYWNoZSB0aGUgbmV3IGNvbnRleHQgdG8gZGlza1xuICAgICAgICAgICAgYXdhaXQgdGhpcy5wcm9wcy5jb25maWd1cmF0aW9uLnNhdmVDb250ZXh0KCk7XG5cbiAgICAgICAgICAgIC8vIEV4ZWN1dGUgYWdhaW5cbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3IENsb3VkQXNzZW1ibHkoYXNzZW1ibHksIHRoaXMucHJvcHMuaW9IZWxwZXIpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGU6IGFueSkge1xuICAgICAgZXJyb3IgPSB7XG4gICAgICAgIG5hbWU6IGNka0NsaUVycm9yTmFtZShlLm5hbWUpLFxuICAgICAgfTtcbiAgICAgIHRocm93IGU7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIGF3YWl0IHN5bnRoU3Bhbi5lbmQoeyBlcnJvciB9KTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGdldCBjYW5Mb29rdXAoKSB7XG4gICAgcmV0dXJuICEhKHRoaXMucHJvcHMuY29uZmlndXJhdGlvbi5zZXR0aW5ncy5nZXQoWydsb29rdXBzJ10pID8/IHRydWUpO1xuICB9XG59XG5cbi8qKlxuICogUmV0dXJuIGFsbCBrZXlzIG9mIG1pc3NpbmcgY29udGV4dCBpdGVtc1xuICovXG5mdW5jdGlvbiBtaXNzaW5nQ29udGV4dEtleXMobWlzc2luZz86IGN4YXBpLk1pc3NpbmdDb250ZXh0W10pOiBTZXQ8c3RyaW5nPiB7XG4gIHJldHVybiBuZXcgU2V0KChtaXNzaW5nIHx8IFtdKS5tYXAobSA9PiBtLmtleSkpO1xufVxuXG5mdW5jdGlvbiBzZXRzRXF1YWw8QT4oYTogU2V0PEE+LCBiOiBTZXQ8QT4pIHtcbiAgaWYgKGEuc2l6ZSAhPT0gYi5zaXplKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGZvciAoY29uc3QgeCBvZiBhKSB7XG4gICAgaWYgKCFiLmhhcyh4KSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cbiJdfQ==