{"version": 3, "file": "pdfkit.js", "sources": ["../lib/abstract_reference.js", "../lib/tree.js", "../lib/object.js", "../lib/reference.js", "../lib/page.js", "../lib/name_tree.js", "../lib/saslprep/lib/util.js", "../lib/saslprep/lib/code-points.js", "../lib/saslprep/index.js", "../lib/security.js", "../lib/gradient.js", "../lib/mixins/color.js", "../lib/path.js", "../lib/mixins/vector.js", "../lib/font/afm.js", "../lib/font.js", "../lib/font/standard.js", "../lib/font/embedded.js", "../lib/font_factory.js", "../lib/mixins/fonts.js", "../lib/line_wrapper.js", "../lib/mixins/text.js", "../lib/image/jpeg.js", "../lib/image/png.js", "../lib/image.js", "../lib/mixins/images.js", "../lib/mixins/annotations.js", "../lib/outline.js", "../lib/mixins/outline.js", "../lib/structure_content.js", "../lib/structure_element.js", "../lib/number_tree.js", "../lib/mixins/markings.js", "../lib/mixins/acroform.js", "../lib/mixins/attachments.js", "../lib/document.js"], "sourcesContent": ["/*\r\nPDFAbstractReference - abstract class for PDF reference\r\n*/\r\n\r\nclass PDFAbstractReference {\r\n  toString() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n}\r\n\r\nexport default PDFAbstractReference;\r\n", "/*\r\nPDFTree - abstract base class for name and number tree objects\r\n*/\r\n\r\nimport PDFObject from './object';\r\n\r\nclass PDFTree {\r\n  constructor(options = {}) {\r\n    this._items = {};\r\n    // disable /Limits output for this tree\r\n    this.limits =\r\n      typeof options.limits === 'boolean' ? options.limits : true;\r\n  }\r\n\r\n  add(key, val) {\r\n    return (this._items[key] = val);\r\n  }\r\n\r\n  get(key) {\r\n    return this._items[key];\r\n  }\r\n\r\n  toString() {\r\n    // Needs to be sorted by key\r\n    const sortedKeys = Object.keys(this._items).sort((a, b) =>\r\n      this._compareKeys(a, b)\r\n    );\r\n\r\n    const out = ['<<'];\r\n    if (this.limits && sortedKeys.length > 1) {\r\n      const first = sortedKeys[0],\r\n        last = sortedKeys[sortedKeys.length - 1];\r\n      out.push(\r\n        `  /Limits ${PDFObject.convert([this._dataForKey(first), this._dataForKey(last)])}`\r\n      );\r\n    }\r\n    out.push(`  /${this._keysName()} [`);\r\n    for (let key of sortedKeys) {\r\n      out.push(\r\n        `    ${PDFObject.convert(this._dataFor<PERSON>ey(key))} ${PDFObject.convert(\r\n          this._items[key]\r\n        )}`\r\n      );\r\n    }\r\n    out.push(']');\r\n    out.push('>>');\r\n    return out.join('\\n');\r\n  }\r\n\r\n  _compareKeys(/*a, b*/) {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  _keysName() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  _dataForKey(/*k*/) {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n}\r\n\r\nexport default PDFTree;\r\n", "/*\r\nPDFObject - converts JavaScript types into their corresponding PDF types.\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFAbstractReference from './abstract_reference';\r\nimport PDFTree from './tree';\r\n\r\nconst pad = (str, length) => (Array(length + 1).join('0') + str).slice(-length);\r\n\r\nconst escapableRe = /[\\n\\r\\t\\b\\f()\\\\]/g;\r\nconst escapable = {\r\n  '\\n': '\\\\n',\r\n  '\\r': '\\\\r',\r\n  '\\t': '\\\\t',\r\n  '\\b': '\\\\b',\r\n  '\\f': '\\\\f',\r\n  '\\\\': '\\\\\\\\',\r\n  '(': '\\\\(',\r\n  ')': '\\\\)'\r\n};\r\n\r\n// Convert little endian UTF-16 to big endian\r\nconst swapBytes = function(buff) {\r\n  const l = buff.length;\r\n  if (l & 0x01) {\r\n    throw new Error('Buffer length must be even');\r\n  } else {\r\n    for (let i = 0, end = l - 1; i < end; i += 2) {\r\n      const a = buff[i];\r\n      buff[i] = buff[i + 1];\r\n      buff[i + 1] = a;\r\n    }\r\n  }\r\n\r\n  return buff;\r\n};\r\n\r\nclass PDFObject {\r\n  static convert(object, encryptFn = null) {\r\n    // String literals are converted to the PDF name type\r\n    if (typeof object === 'string') {\r\n      return `/${object}`;\r\n\r\n      // String objects are converted to PDF strings (UTF-16)\r\n    } else if (object instanceof String) {\r\n      let string = object;\r\n      // Detect if this is a unicode string\r\n      let isUnicode = false;\r\n      for (let i = 0, end = string.length; i < end; i++) {\r\n        if (string.charCodeAt(i) > 0x7f) {\r\n          isUnicode = true;\r\n          break;\r\n        }\r\n      }\r\n\r\n      // If so, encode it as big endian UTF-16\r\n      let stringBuffer;\r\n      if (isUnicode) {\r\n        stringBuffer = swapBytes(Buffer.from(`\\ufeff${string}`, 'utf16le'));\r\n      } else {\r\n        stringBuffer = Buffer.from(string.valueOf(), 'ascii');\r\n      }\r\n\r\n      // Encrypt the string when necessary\r\n      if (encryptFn) {\r\n        string = encryptFn(stringBuffer).toString('binary');\r\n      } else {\r\n        string = stringBuffer.toString('binary');\r\n      }\r\n\r\n      // Escape characters as required by the spec\r\n      string = string.replace(escapableRe, c => escapable[c]);\r\n\r\n      return `(${string})`;\r\n\r\n      // Buffers are converted to PDF hex strings\r\n    } else if (Buffer.isBuffer(object)) {\r\n      return `<${object.toString('hex')}>`;\r\n    } else if (\r\n      object instanceof PDFAbstractReference ||\r\n      object instanceof PDFTree\r\n    ) {\r\n      return object.toString();\r\n    } else if (object instanceof Date) {\r\n      let string =\r\n        `D:${pad(object.getUTCFullYear(), 4)}` +\r\n        pad(object.getUTCMonth() + 1, 2) +\r\n        pad(object.getUTCDate(), 2) +\r\n        pad(object.getUTCHours(), 2) +\r\n        pad(object.getUTCMinutes(), 2) +\r\n        pad(object.getUTCSeconds(), 2) +\r\n        'Z';\r\n\r\n      // Encrypt the string when necessary\r\n      if (encryptFn) {\r\n        string = encryptFn(Buffer.from(string, 'ascii')).toString('binary');\r\n\r\n        // Escape characters as required by the spec\r\n        string = string.replace(escapableRe, c => escapable[c]);\r\n      }\r\n\r\n      return `(${string})`;\r\n    } else if (Array.isArray(object)) {\r\n      const items = object.map(e => PDFObject.convert(e, encryptFn)).join(' ');\r\n      return `[${items}]`;\r\n    } else if ({}.toString.call(object) === '[object Object]') {\r\n      const out = ['<<'];\r\n      for (let key in object) {\r\n        const val = object[key];\r\n        out.push(`/${key} ${PDFObject.convert(val, encryptFn)}`);\r\n      }\r\n\r\n      out.push('>>');\r\n      return out.join('\\n');\r\n    } else if (typeof object === 'number') {\r\n      return PDFObject.number(object);\r\n    } else {\r\n      return `${object}`;\r\n    }\r\n  }\r\n\r\n  static number(n) {\r\n    if (n > -1e21 && n < 1e21) {\r\n      return Math.round(n * 1e6) / 1e6;\r\n    }\r\n\r\n    throw new Error(`unsupported number: ${n}`);\r\n  }\r\n}\r\n\r\nexport default PDFObject;\r\n", "/*\r\nPDFReference - represents a reference to another object in the PDF object heirarchy\r\nBy <PERSON>\r\n*/\r\n\r\nimport zlib from 'zlib';\r\nimport PDFAbstractReference from './abstract_reference';\r\nimport PDFObject from './object';\r\n\r\nclass PDFReference extends PDFAbstractReference {\r\n  constructor(document, id, data = {}) {\r\n    super();\r\n    this.document = document;\r\n    this.id = id;\r\n    this.data = data;\r\n    this.gen = 0;\r\n    this.compress = this.document.compress && !this.data.Filter;\r\n    this.uncompressedLength = 0;\r\n    this.buffer = [];\r\n  }\r\n\r\n  write(chunk) {\r\n    if (!Buffer.isBuffer(chunk)) {\r\n      chunk = Buffer.from(chunk + '\\n', 'binary');\r\n    }\r\n\r\n    this.uncompressedLength += chunk.length;\r\n    if (this.data.Length == null) {\r\n      this.data.Length = 0;\r\n    }\r\n    this.buffer.push(chunk);\r\n    this.data.Length += chunk.length;\r\n    if (this.compress) {\r\n      return (this.data.Filter = 'FlateDecode');\r\n    }\r\n  }\r\n\r\n  end(chunk) {\r\n    if (chunk) {\r\n      this.write(chunk);\r\n    }\r\n    return this.finalize();\r\n  }\r\n\r\n  finalize() {\r\n    this.offset = this.document._offset;\r\n\r\n    const encryptFn = this.document._security\r\n      ? this.document._security.getEncryptFn(this.id, this.gen)\r\n      : null;\r\n\r\n    if (this.buffer.length) {\r\n      this.buffer = Buffer.concat(this.buffer);\r\n      if (this.compress) {\r\n        this.buffer = zlib.deflateSync(this.buffer);\r\n      }\r\n\r\n      if (encryptFn) {\r\n        this.buffer = encryptFn(this.buffer);\r\n      }\r\n\r\n      this.data.Length = this.buffer.length;\r\n    }\r\n\r\n    this.document._write(`${this.id} ${this.gen} obj`);\r\n    this.document._write(PDFObject.convert(this.data, encryptFn));\r\n\r\n    if (this.buffer.length) {\r\n      this.document._write('stream');\r\n      this.document._write(this.buffer);\r\n\r\n      this.buffer = []; // free up memory\r\n      this.document._write('\\nendstream');\r\n    }\r\n\r\n    this.document._write('endobj');\r\n    this.document._refEnd(this);\r\n  }\r\n  toString() {\r\n    return `${this.id} ${this.gen} R`;\r\n  }\r\n}\r\n\r\nexport default PDFReference;\r\n", "/*\r\nPDFPage - represents a single page in the PDF document\r\nBy <PERSON>\r\n*/\r\n\r\nconst DEFAULT_MARGINS = {\r\n  top: 72,\r\n  left: 72,\r\n  bottom: 72,\r\n  right: 72\r\n};\r\n\r\nconst SIZES = {\r\n  '4A0': [4767.87, 6740.79],\r\n  '2A0': [3370.39, 4767.87],\r\n  A0: [2383.94, 3370.39],\r\n  A1: [1683.78, 2383.94],\r\n  A2: [1190.55, 1683.78],\r\n  A3: [841.89, 1190.55],\r\n  A4: [595.28, 841.89],\r\n  A5: [419.53, 595.28],\r\n  A6: [297.64, 419.53],\r\n  A7: [209.76, 297.64],\r\n  A8: [147.4, 209.76],\r\n  A9: [104.88, 147.4],\r\n  A10: [73.7, 104.88],\r\n  B0: [2834.65, 4008.19],\r\n  B1: [2004.09, 2834.65],\r\n  B2: [1417.32, 2004.09],\r\n  B3: [1000.63, 1417.32],\r\n  B4: [708.66, 1000.63],\r\n  B5: [498.9, 708.66],\r\n  B6: [354.33, 498.9],\r\n  B7: [249.45, 354.33],\r\n  B8: [175.75, 249.45],\r\n  B9: [124.72, 175.75],\r\n  B10: [87.87, 124.72],\r\n  C0: [2599.37, 3676.54],\r\n  C1: [1836.85, 2599.37],\r\n  C2: [1298.27, 1836.85],\r\n  C3: [918.43, 1298.27],\r\n  C4: [649.13, 918.43],\r\n  C5: [459.21, 649.13],\r\n  C6: [323.15, 459.21],\r\n  C7: [229.61, 323.15],\r\n  C8: [161.57, 229.61],\r\n  C9: [113.39, 161.57],\r\n  C10: [79.37, 113.39],\r\n  RA0: [2437.8, 3458.27],\r\n  RA1: [1729.13, 2437.8],\r\n  RA2: [1218.9, 1729.13],\r\n  RA3: [864.57, 1218.9],\r\n  RA4: [609.45, 864.57],\r\n  SRA0: [2551.18, 3628.35],\r\n  SRA1: [1814.17, 2551.18],\r\n  SRA2: [1275.59, 1814.17],\r\n  SRA3: [907.09, 1275.59],\r\n  SRA4: [637.8, 907.09],\r\n  EXECUTIVE: [521.86, 756.0],\r\n  FOLIO: [612.0, 936.0],\r\n  LEGAL: [612.0, 1008.0],\r\n  LETTER: [612.0, 792.0],\r\n  TABLOID: [792.0, 1224.0]\r\n};\r\n\r\nclass PDFPage {\r\n  constructor(document, options = {}) {\r\n    this.document = document;\r\n    this.size = options.size || 'letter';\r\n    this.layout = options.layout || 'portrait';\r\n\r\n    // process margins\r\n    if (typeof options.margin === 'number') {\r\n      this.margins = {\r\n        top: options.margin,\r\n        left: options.margin,\r\n        bottom: options.margin,\r\n        right: options.margin\r\n      };\r\n\r\n      // default to 1 inch margins\r\n    } else {\r\n      this.margins = options.margins || DEFAULT_MARGINS;\r\n    }\r\n\r\n    // calculate page dimensions\r\n    const dimensions = Array.isArray(this.size)\r\n      ? this.size\r\n      : SIZES[this.size.toUpperCase()];\r\n    this.width = dimensions[this.layout === 'portrait' ? 0 : 1];\r\n    this.height = dimensions[this.layout === 'portrait' ? 1 : 0];\r\n\r\n    this.content = this.document.ref();\r\n\r\n    // Initialize the Font, XObject, and ExtGState dictionaries\r\n    this.resources = this.document.ref({\r\n      ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI']\r\n    });\r\n\r\n    // The page dictionary\r\n    this.dictionary = this.document.ref({\r\n      Type: 'Page',\r\n      Parent: this.document._root.data.Pages,\r\n      MediaBox: [0, 0, this.width, this.height],\r\n      Contents: this.content,\r\n      Resources: this.resources\r\n    });\r\n\r\n    this.markings = [];\r\n  }\r\n\r\n  // Lazily create these objects\r\n  get fonts() {\r\n    const data = this.resources.data;\r\n    return data.Font != null ? data.Font : (data.Font = {});\r\n  }\r\n\r\n  get xobjects() {\r\n    const data = this.resources.data;\r\n    return data.XObject != null ? data.XObject : (data.XObject = {});\r\n  }\r\n\r\n  get ext_gstates() {\r\n    const data = this.resources.data;\r\n    return data.ExtGState != null ? data.ExtGState : (data.ExtGState = {});\r\n  }\r\n\r\n  get patterns() {\r\n    const data = this.resources.data;\r\n    return data.Pattern != null ? data.Pattern : (data.Pattern = {});\r\n  }\r\n\r\n  get annotations() {\r\n    const data = this.dictionary.data;\r\n    return data.Annots != null ? data.Annots : (data.Annots = []);\r\n  }\r\n\r\n  get structParentTreeKey() {\r\n    const data = this.dictionary.data;\r\n    return data.StructParents != null ?\r\n      data.StructParents :\r\n      (data.StructParents = this.document.createStructParentTreeNextKey());\r\n  }\r\n\r\n  maxY() {\r\n    return this.height - this.margins.bottom;\r\n  }\r\n\r\n  write(chunk) {\r\n    return this.content.write(chunk);\r\n  }\r\n\r\n  end() {\r\n    this.dictionary.end();\r\n    this.resources.end();\r\n    return this.content.end();\r\n  }\r\n}\r\n\r\nexport default PDFPage;\r\n", "/*\r\nPDFNameTree - represents a name tree object\r\n*/\r\n\r\nimport PDFTree from \"./tree\";\r\n\r\nclass PDFNameTree extends PDFTree {\r\n  _compareKeys(a, b) {\r\n    return a.localeCompare(b);\r\n  }\r\n\r\n  _keysName() {\r\n    return \"Names\";\r\n  }\r\n\r\n  _dataForKey(k) {\r\n    return new String(k);\r\n  }\r\n}\r\n\r\nexport default PDFNameTree;\r\n", "/**\r\n * Check if value is in a range group.\r\n * @param {number} value\r\n * @param {number[]} rangeGroup\r\n * @returns {boolean}\r\n */\r\nfunction inRange(value, rangeGroup) {\r\n  if (value < rangeGroup[0]) return false;\r\n  let startRange = 0;\r\n  let endRange = rangeGroup.length / 2;\r\n  while (startRange <= endRange) {\r\n    const middleRange = Math.floor((startRange + endRange) / 2);\r\n\r\n    // actual array index\r\n    const arrayIndex = middleRange * 2;\r\n\r\n    // Check if value is in range pointed by actual index\r\n    if (\r\n      value >= rangeGroup[arrayIndex] &&\r\n      value <= rangeGroup[arrayIndex + 1]\r\n    ) {\r\n      return true;\r\n    }\r\n\r\n    if (value > rangeGroup[arrayIndex + 1]) {\r\n      // Search Right Side Of Array\r\n      startRange = middleRange + 1;\r\n    } else {\r\n      // Search Left Side Of Array\r\n      endRange = middleRange - 1;\r\n    }\r\n  }\r\n  return false;\r\n}\r\n\r\nexport { inRange };\r\n", "import { inRange } from './util';\r\n\r\n// prettier-ignore-start\r\n/**\r\n * A.1 Unassigned code points in Unicode 3.2\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-A.1\r\n */\r\nconst unassigned_code_points = [\r\n  0x0221,\r\n  0x0221,\r\n  0x0234,\r\n  0x024f,\r\n  0x02ae,\r\n  0x02af,\r\n  0x02ef,\r\n  0x02ff,\r\n  0x0350,\r\n  0x035f,\r\n  0x0370,\r\n  0x0373,\r\n  0x0376,\r\n  0x0379,\r\n  0x037b,\r\n  0x037d,\r\n  0x037f,\r\n  0x0383,\r\n  0x038b,\r\n  0x038b,\r\n  0x038d,\r\n  0x038d,\r\n  0x03a2,\r\n  0x03a2,\r\n  0x03cf,\r\n  0x03cf,\r\n  0x03f7,\r\n  0x03ff,\r\n  0x0487,\r\n  0x0487,\r\n  0x04cf,\r\n  0x04cf,\r\n  0x04f6,\r\n  0x04f7,\r\n  0x04fa,\r\n  0x04ff,\r\n  0x0510,\r\n  0x0530,\r\n  0x0557,\r\n  0x0558,\r\n  0x0560,\r\n  0x0560,\r\n  0x0588,\r\n  0x0588,\r\n  0x058b,\r\n  0x0590,\r\n  0x05a2,\r\n  0x05a2,\r\n  0x05ba,\r\n  0x05ba,\r\n  0x05c5,\r\n  0x05cf,\r\n  0x05eb,\r\n  0x05ef,\r\n  0x05f5,\r\n  0x060b,\r\n  0x060d,\r\n  0x061a,\r\n  0x061c,\r\n  0x061e,\r\n  0x0620,\r\n  0x0620,\r\n  0x063b,\r\n  0x063f,\r\n  0x0656,\r\n  0x065f,\r\n  0x06ee,\r\n  0x06ef,\r\n  0x06ff,\r\n  0x06ff,\r\n  0x070e,\r\n  0x070e,\r\n  0x072d,\r\n  0x072f,\r\n  0x074b,\r\n  0x077f,\r\n  0x07b2,\r\n  0x0900,\r\n  0x0904,\r\n  0x0904,\r\n  0x093a,\r\n  0x093b,\r\n  0x094e,\r\n  0x094f,\r\n  0x0955,\r\n  0x0957,\r\n  0x0971,\r\n  0x0980,\r\n  0x0984,\r\n  0x0984,\r\n  0x098d,\r\n  0x098e,\r\n  0x0991,\r\n  0x0992,\r\n  0x09a9,\r\n  0x09a9,\r\n  0x09b1,\r\n  0x09b1,\r\n  0x09b3,\r\n  0x09b5,\r\n  0x09ba,\r\n  0x09bb,\r\n  0x09bd,\r\n  0x09bd,\r\n  0x09c5,\r\n  0x09c6,\r\n  0x09c9,\r\n  0x09ca,\r\n  0x09ce,\r\n  0x09d6,\r\n  0x09d8,\r\n  0x09db,\r\n  0x09de,\r\n  0x09de,\r\n  0x09e4,\r\n  0x09e5,\r\n  0x09fb,\r\n  0x0a01,\r\n  0x0a03,\r\n  0x0a04,\r\n  0x0a0b,\r\n  0x0a0e,\r\n  0x0a11,\r\n  0x0a12,\r\n  0x0a29,\r\n  0x0a29,\r\n  0x0a31,\r\n  0x0a31,\r\n  0x0a34,\r\n  0x0a34,\r\n  0x0a37,\r\n  0x0a37,\r\n  0x0a3a,\r\n  0x0a3b,\r\n  0x0a3d,\r\n  0x0a3d,\r\n  0x0a43,\r\n  0x0a46,\r\n  0x0a49,\r\n  0x0a4a,\r\n  0x0a4e,\r\n  0x0a58,\r\n  0x0a5d,\r\n  0x0a5d,\r\n  0x0a5f,\r\n  0x0a65,\r\n  0x0a75,\r\n  0x0a80,\r\n  0x0a84,\r\n  0x0a84,\r\n  0x0a8c,\r\n  0x0a8c,\r\n  0x0a8e,\r\n  0x0a8e,\r\n  0x0a92,\r\n  0x0a92,\r\n  0x0aa9,\r\n  0x0aa9,\r\n  0x0ab1,\r\n  0x0ab1,\r\n  0x0ab4,\r\n  0x0ab4,\r\n  0x0aba,\r\n  0x0abb,\r\n  0x0ac6,\r\n  0x0ac6,\r\n  0x0aca,\r\n  0x0aca,\r\n  0x0ace,\r\n  0x0acf,\r\n  0x0ad1,\r\n  0x0adf,\r\n  0x0ae1,\r\n  0x0ae5,\r\n  0x0af0,\r\n  0x0b00,\r\n  0x0b04,\r\n  0x0b04,\r\n  0x0b0d,\r\n  0x0b0e,\r\n  0x0b11,\r\n  0x0b12,\r\n  0x0b29,\r\n  0x0b29,\r\n  0x0b31,\r\n  0x0b31,\r\n  0x0b34,\r\n  0x0b35,\r\n  0x0b3a,\r\n  0x0b3b,\r\n  0x0b44,\r\n  0x0b46,\r\n  0x0b49,\r\n  0x0b4a,\r\n  0x0b4e,\r\n  0x0b55,\r\n  0x0b58,\r\n  0x0b5b,\r\n  0x0b5e,\r\n  0x0b5e,\r\n  0x0b62,\r\n  0x0b65,\r\n  0x0b71,\r\n  0x0b81,\r\n  0x0b84,\r\n  0x0b84,\r\n  0x0b8b,\r\n  0x0b8d,\r\n  0x0b91,\r\n  0x0b91,\r\n  0x0b96,\r\n  0x0b98,\r\n  0x0b9b,\r\n  0x0b9b,\r\n  0x0b9d,\r\n  0x0b9d,\r\n  0x0ba0,\r\n  0x0ba2,\r\n  0x0ba5,\r\n  0x0ba7,\r\n  0x0bab,\r\n  0x0bad,\r\n  0x0bb6,\r\n  0x0bb6,\r\n  0x0bba,\r\n  0x0bbd,\r\n  0x0bc3,\r\n  0x0bc5,\r\n  0x0bc9,\r\n  0x0bc9,\r\n  0x0bce,\r\n  0x0bd6,\r\n  0x0bd8,\r\n  0x0be6,\r\n  0x0bf3,\r\n  0x0c00,\r\n  0x0c04,\r\n  0x0c04,\r\n  0x0c0d,\r\n  0x0c0d,\r\n  0x0c11,\r\n  0x0c11,\r\n  0x0c29,\r\n  0x0c29,\r\n  0x0c34,\r\n  0x0c34,\r\n  0x0c3a,\r\n  0x0c3d,\r\n  0x0c45,\r\n  0x0c45,\r\n  0x0c49,\r\n  0x0c49,\r\n  0x0c4e,\r\n  0x0c54,\r\n  0x0c57,\r\n  0x0c5f,\r\n  0x0c62,\r\n  0x0c65,\r\n  0x0c70,\r\n  0x0c81,\r\n  0x0c84,\r\n  0x0c84,\r\n  0x0c8d,\r\n  0x0c8d,\r\n  0x0c91,\r\n  0x0c91,\r\n  0x0ca9,\r\n  0x0ca9,\r\n  0x0cb4,\r\n  0x0cb4,\r\n  0x0cba,\r\n  0x0cbd,\r\n  0x0cc5,\r\n  0x0cc5,\r\n  0x0cc9,\r\n  0x0cc9,\r\n  0x0cce,\r\n  0x0cd4,\r\n  0x0cd7,\r\n  0x0cdd,\r\n  0x0cdf,\r\n  0x0cdf,\r\n  0x0ce2,\r\n  0x0ce5,\r\n  0x0cf0,\r\n  0x0d01,\r\n  0x0d04,\r\n  0x0d04,\r\n  0x0d0d,\r\n  0x0d0d,\r\n  0x0d11,\r\n  0x0d11,\r\n  0x0d29,\r\n  0x0d29,\r\n  0x0d3a,\r\n  0x0d3d,\r\n  0x0d44,\r\n  0x0d45,\r\n  0x0d49,\r\n  0x0d49,\r\n  0x0d4e,\r\n  0x0d56,\r\n  0x0d58,\r\n  0x0d5f,\r\n  0x0d62,\r\n  0x0d65,\r\n  0x0d70,\r\n  0x0d81,\r\n  0x0d84,\r\n  0x0d84,\r\n  0x0d97,\r\n  0x0d99,\r\n  0x0db2,\r\n  0x0db2,\r\n  0x0dbc,\r\n  0x0dbc,\r\n  0x0dbe,\r\n  0x0dbf,\r\n  0x0dc7,\r\n  0x0dc9,\r\n  0x0dcb,\r\n  0x0dce,\r\n  0x0dd5,\r\n  0x0dd5,\r\n  0x0dd7,\r\n  0x0dd7,\r\n  0x0de0,\r\n  0x0df1,\r\n  0x0df5,\r\n  0x0e00,\r\n  0x0e3b,\r\n  0x0e3e,\r\n  0x0e5c,\r\n  0x0e80,\r\n  0x0e83,\r\n  0x0e83,\r\n  0x0e85,\r\n  0x0e86,\r\n  0x0e89,\r\n  0x0e89,\r\n  0x0e8b,\r\n  0x0e8c,\r\n  0x0e8e,\r\n  0x0e93,\r\n  0x0e98,\r\n  0x0e98,\r\n  0x0ea0,\r\n  0x0ea0,\r\n  0x0ea4,\r\n  0x0ea4,\r\n  0x0ea6,\r\n  0x0ea6,\r\n  0x0ea8,\r\n  0x0ea9,\r\n  0x0eac,\r\n  0x0eac,\r\n  0x0eba,\r\n  0x0eba,\r\n  0x0ebe,\r\n  0x0ebf,\r\n  0x0ec5,\r\n  0x0ec5,\r\n  0x0ec7,\r\n  0x0ec7,\r\n  0x0ece,\r\n  0x0ecf,\r\n  0x0eda,\r\n  0x0edb,\r\n  0x0ede,\r\n  0x0eff,\r\n  0x0f48,\r\n  0x0f48,\r\n  0x0f6b,\r\n  0x0f70,\r\n  0x0f8c,\r\n  0x0f8f,\r\n  0x0f98,\r\n  0x0f98,\r\n  0x0fbd,\r\n  0x0fbd,\r\n  0x0fcd,\r\n  0x0fce,\r\n  0x0fd0,\r\n  0x0fff,\r\n  0x1022,\r\n  0x1022,\r\n  0x1028,\r\n  0x1028,\r\n  0x102b,\r\n  0x102b,\r\n  0x1033,\r\n  0x1035,\r\n  0x103a,\r\n  0x103f,\r\n  0x105a,\r\n  0x109f,\r\n  0x10c6,\r\n  0x10cf,\r\n  0x10f9,\r\n  0x10fa,\r\n  0x10fc,\r\n  0x10ff,\r\n  0x115a,\r\n  0x115e,\r\n  0x11a3,\r\n  0x11a7,\r\n  0x11fa,\r\n  0x11ff,\r\n  0x1207,\r\n  0x1207,\r\n  0x1247,\r\n  0x1247,\r\n  0x1249,\r\n  0x1249,\r\n  0x124e,\r\n  0x124f,\r\n  0x1257,\r\n  0x1257,\r\n  0x1259,\r\n  0x1259,\r\n  0x125e,\r\n  0x125f,\r\n  0x1287,\r\n  0x1287,\r\n  0x1289,\r\n  0x1289,\r\n  0x128e,\r\n  0x128f,\r\n  0x12af,\r\n  0x12af,\r\n  0x12b1,\r\n  0x12b1,\r\n  0x12b6,\r\n  0x12b7,\r\n  0x12bf,\r\n  0x12bf,\r\n  0x12c1,\r\n  0x12c1,\r\n  0x12c6,\r\n  0x12c7,\r\n  0x12cf,\r\n  0x12cf,\r\n  0x12d7,\r\n  0x12d7,\r\n  0x12ef,\r\n  0x12ef,\r\n  0x130f,\r\n  0x130f,\r\n  0x1311,\r\n  0x1311,\r\n  0x1316,\r\n  0x1317,\r\n  0x131f,\r\n  0x131f,\r\n  0x1347,\r\n  0x1347,\r\n  0x135b,\r\n  0x1360,\r\n  0x137d,\r\n  0x139f,\r\n  0x13f5,\r\n  0x1400,\r\n  0x1677,\r\n  0x167f,\r\n  0x169d,\r\n  0x169f,\r\n  0x16f1,\r\n  0x16ff,\r\n  0x170d,\r\n  0x170d,\r\n  0x1715,\r\n  0x171f,\r\n  0x1737,\r\n  0x173f,\r\n  0x1754,\r\n  0x175f,\r\n  0x176d,\r\n  0x176d,\r\n  0x1771,\r\n  0x1771,\r\n  0x1774,\r\n  0x177f,\r\n  0x17dd,\r\n  0x17df,\r\n  0x17ea,\r\n  0x17ff,\r\n  0x180f,\r\n  0x180f,\r\n  0x181a,\r\n  0x181f,\r\n  0x1878,\r\n  0x187f,\r\n  0x18aa,\r\n  0x1dff,\r\n  0x1e9c,\r\n  0x1e9f,\r\n  0x1efa,\r\n  0x1eff,\r\n  0x1f16,\r\n  0x1f17,\r\n  0x1f1e,\r\n  0x1f1f,\r\n  0x1f46,\r\n  0x1f47,\r\n  0x1f4e,\r\n  0x1f4f,\r\n  0x1f58,\r\n  0x1f58,\r\n  0x1f5a,\r\n  0x1f5a,\r\n  0x1f5c,\r\n  0x1f5c,\r\n  0x1f5e,\r\n  0x1f5e,\r\n  0x1f7e,\r\n  0x1f7f,\r\n  0x1fb5,\r\n  0x1fb5,\r\n  0x1fc5,\r\n  0x1fc5,\r\n  0x1fd4,\r\n  0x1fd5,\r\n  0x1fdc,\r\n  0x1fdc,\r\n  0x1ff0,\r\n  0x1ff1,\r\n  0x1ff5,\r\n  0x1ff5,\r\n  0x1fff,\r\n  0x1fff,\r\n  0x2053,\r\n  0x2056,\r\n  0x2058,\r\n  0x205e,\r\n  0x2064,\r\n  0x2069,\r\n  0x2072,\r\n  0x2073,\r\n  0x208f,\r\n  0x209f,\r\n  0x20b2,\r\n  0x20cf,\r\n  0x20eb,\r\n  0x20ff,\r\n  0x213b,\r\n  0x213c,\r\n  0x214c,\r\n  0x2152,\r\n  0x2184,\r\n  0x218f,\r\n  0x23cf,\r\n  0x23ff,\r\n  0x2427,\r\n  0x243f,\r\n  0x244b,\r\n  0x245f,\r\n  0x24ff,\r\n  0x24ff,\r\n  0x2614,\r\n  0x2615,\r\n  0x2618,\r\n  0x2618,\r\n  0x267e,\r\n  0x267f,\r\n  0x268a,\r\n  0x2700,\r\n  0x2705,\r\n  0x2705,\r\n  0x270a,\r\n  0x270b,\r\n  0x2728,\r\n  0x2728,\r\n  0x274c,\r\n  0x274c,\r\n  0x274e,\r\n  0x274e,\r\n  0x2753,\r\n  0x2755,\r\n  0x2757,\r\n  0x2757,\r\n  0x275f,\r\n  0x2760,\r\n  0x2795,\r\n  0x2797,\r\n  0x27b0,\r\n  0x27b0,\r\n  0x27bf,\r\n  0x27cf,\r\n  0x27ec,\r\n  0x27ef,\r\n  0x2b00,\r\n  0x2e7f,\r\n  0x2e9a,\r\n  0x2e9a,\r\n  0x2ef4,\r\n  0x2eff,\r\n  0x2fd6,\r\n  0x2fef,\r\n  0x2ffc,\r\n  0x2fff,\r\n  0x3040,\r\n  0x3040,\r\n  0x3097,\r\n  0x3098,\r\n  0x3100,\r\n  0x3104,\r\n  0x312d,\r\n  0x3130,\r\n  0x318f,\r\n  0x318f,\r\n  0x31b8,\r\n  0x31ef,\r\n  0x321d,\r\n  0x321f,\r\n  0x3244,\r\n  0x3250,\r\n  0x327c,\r\n  0x327e,\r\n  0x32cc,\r\n  0x32cf,\r\n  0x32ff,\r\n  0x32ff,\r\n  0x3377,\r\n  0x337a,\r\n  0x33de,\r\n  0x33df,\r\n  0x33ff,\r\n  0x33ff,\r\n  0x4db6,\r\n  0x4dff,\r\n  0x9fa6,\r\n  0x9fff,\r\n  0xa48d,\r\n  0xa48f,\r\n  0xa4c7,\r\n  0xabff,\r\n  0xd7a4,\r\n  0xd7ff,\r\n  0xfa2e,\r\n  0xfa2f,\r\n  0xfa6b,\r\n  0xfaff,\r\n  0xfb07,\r\n  0xfb12,\r\n  0xfb18,\r\n  0xfb1c,\r\n  0xfb37,\r\n  0xfb37,\r\n  0xfb3d,\r\n  0xfb3d,\r\n  0xfb3f,\r\n  0xfb3f,\r\n  0xfb42,\r\n  0xfb42,\r\n  0xfb45,\r\n  0xfb45,\r\n  0xfbb2,\r\n  0xfbd2,\r\n  0xfd40,\r\n  0xfd4f,\r\n  0xfd90,\r\n  0xfd91,\r\n  0xfdc8,\r\n  0xfdcf,\r\n  0xfdfd,\r\n  0xfdff,\r\n  0xfe10,\r\n  0xfe1f,\r\n  0xfe24,\r\n  0xfe2f,\r\n  0xfe47,\r\n  0xfe48,\r\n  0xfe53,\r\n  0xfe53,\r\n  0xfe67,\r\n  0xfe67,\r\n  0xfe6c,\r\n  0xfe6f,\r\n  0xfe75,\r\n  0xfe75,\r\n  0xfefd,\r\n  0xfefe,\r\n  0xff00,\r\n  0xff00,\r\n  0xffbf,\r\n  0xffc1,\r\n  0xffc8,\r\n  0xffc9,\r\n  0xffd0,\r\n  0xffd1,\r\n  0xffd8,\r\n  0xffd9,\r\n  0xffdd,\r\n  0xffdf,\r\n  0xffe7,\r\n  0xffe7,\r\n  0xffef,\r\n  0xfff8,\r\n  0x10000,\r\n  0x102ff,\r\n  0x1031f,\r\n  0x1031f,\r\n  0x10324,\r\n  0x1032f,\r\n  0x1034b,\r\n  0x103ff,\r\n  0x10426,\r\n  0x10427,\r\n  0x1044e,\r\n  0x1cfff,\r\n  0x1d0f6,\r\n  0x1d0ff,\r\n  0x1d127,\r\n  0x1d129,\r\n  0x1d1de,\r\n  0x1d3ff,\r\n  0x1d455,\r\n  0x1d455,\r\n  0x1d49d,\r\n  0x1d49d,\r\n  0x1d4a0,\r\n  0x1d4a1,\r\n  0x1d4a3,\r\n  0x1d4a4,\r\n  0x1d4a7,\r\n  0x1d4a8,\r\n  0x1d4ad,\r\n  0x1d4ad,\r\n  0x1d4ba,\r\n  0x1d4ba,\r\n  0x1d4bc,\r\n  0x1d4bc,\r\n  0x1d4c1,\r\n  0x1d4c1,\r\n  0x1d4c4,\r\n  0x1d4c4,\r\n  0x1d506,\r\n  0x1d506,\r\n  0x1d50b,\r\n  0x1d50c,\r\n  0x1d515,\r\n  0x1d515,\r\n  0x1d51d,\r\n  0x1d51d,\r\n  0x1d53a,\r\n  0x1d53a,\r\n  0x1d53f,\r\n  0x1d53f,\r\n  0x1d545,\r\n  0x1d545,\r\n  0x1d547,\r\n  0x1d549,\r\n  0x1d551,\r\n  0x1d551,\r\n  0x1d6a4,\r\n  0x1d6a7,\r\n  0x1d7ca,\r\n  0x1d7cd,\r\n  0x1d800,\r\n  0x1fffd,\r\n  0x2a6d7,\r\n  0x2f7ff,\r\n  0x2fa1e,\r\n  0x2fffd,\r\n  0x30000,\r\n  0x3fffd,\r\n  0x40000,\r\n  0x4fffd,\r\n  0x50000,\r\n  0x5fffd,\r\n  0x60000,\r\n  0x6fffd,\r\n  0x70000,\r\n  0x7fffd,\r\n  0x80000,\r\n  0x8fffd,\r\n  0x90000,\r\n  0x9fffd,\r\n  0xa0000,\r\n  0xafffd,\r\n  0xb0000,\r\n  0xbfffd,\r\n  0xc0000,\r\n  0xcfffd,\r\n  0xd0000,\r\n  0xdfffd,\r\n  0xe0000,\r\n  0xe0000,\r\n  0xe0002,\r\n  0xe001f,\r\n  0xe0080,\r\n  0xefffd\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isUnassignedCodePoint = character =>\r\n  inRange(character, unassigned_code_points);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * B.1 Commonly mapped to nothing\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-B.1\r\n */\r\nconst commonly_mapped_to_nothing = [\r\n  0x00ad,\r\n  0x00ad,\r\n  0x034f,\r\n  0x034f,\r\n  0x1806,\r\n  0x1806,\r\n  0x180b,\r\n  0x180b,\r\n  0x180c,\r\n  0x180c,\r\n  0x180d,\r\n  0x180d,\r\n  0x200b,\r\n  0x200b,\r\n  0x200c,\r\n  0x200c,\r\n  0x200d,\r\n  0x200d,\r\n  0x2060,\r\n  0x2060,\r\n  0xfe00,\r\n  0xfe00,\r\n  0xfe01,\r\n  0xfe01,\r\n  0xfe02,\r\n  0xfe02,\r\n  0xfe03,\r\n  0xfe03,\r\n  0xfe04,\r\n  0xfe04,\r\n  0xfe05,\r\n  0xfe05,\r\n  0xfe06,\r\n  0xfe06,\r\n  0xfe07,\r\n  0xfe07,\r\n  0xfe08,\r\n  0xfe08,\r\n  0xfe09,\r\n  0xfe09,\r\n  0xfe0a,\r\n  0xfe0a,\r\n  0xfe0b,\r\n  0xfe0b,\r\n  0xfe0c,\r\n  0xfe0c,\r\n  0xfe0d,\r\n  0xfe0d,\r\n  0xfe0e,\r\n  0xfe0e,\r\n  0xfe0f,\r\n  0xfe0f,\r\n  0xfeff,\r\n  0xfeff\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isCommonlyMappedToNothing = character =>\r\n  inRange(character, commonly_mapped_to_nothing);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * C.1.2 Non-ASCII space characters\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-C.1.2\r\n */\r\nconst non_ASCII_space_characters = [\r\n  0x00a0,\r\n  0x00a0 /* NO-BREAK SPACE */,\r\n  0x1680,\r\n  0x1680 /* OGHAM SPACE MARK */,\r\n  0x2000,\r\n  0x2000 /* EN QUAD */,\r\n  0x2001,\r\n  0x2001 /* EM QUAD */,\r\n  0x2002,\r\n  0x2002 /* EN SPACE */,\r\n  0x2003,\r\n  0x2003 /* EM SPACE */,\r\n  0x2004,\r\n  0x2004 /* THREE-PER-EM SPACE */,\r\n  0x2005,\r\n  0x2005 /* FOUR-PER-EM SPACE */,\r\n  0x2006,\r\n  0x2006 /* SIX-PER-EM SPACE */,\r\n  0x2007,\r\n  0x2007 /* FIGURE SPACE */,\r\n  0x2008,\r\n  0x2008 /* PUNCTUATION SPACE */,\r\n  0x2009,\r\n  0x2009 /* THIN SPACE */,\r\n  0x200a,\r\n  0x200a /* HAIR SPACE */,\r\n  0x200b,\r\n  0x200b /* ZERO WIDTH SPACE */,\r\n  0x202f,\r\n  0x202f /* NARROW NO-BREAK SPACE */,\r\n  0x205f,\r\n  0x205f /* MEDIUM MATHEMATICAL SPACE */,\r\n  0x3000,\r\n  0x3000 /* IDEOGRAPHIC SPACE */\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isNonASCIISpaceCharacter = character =>\r\n  inRange(character, non_ASCII_space_characters);\r\n\r\n// prettier-ignore-start\r\nconst non_ASCII_controls_characters = [\r\n  /**\r\n   * C.2.2 Non-ASCII control characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.2.2\r\n   */\r\n  0x0080,\r\n  0x009f /* [CONTROL CHARACTERS] */,\r\n  0x06dd,\r\n  0x06dd /* ARABIC END OF AYAH */,\r\n  0x070f,\r\n  0x070f /* SYRIAC ABBREVIATION MARK */,\r\n  0x180e,\r\n  0x180e /* MONGOLIAN VOWEL SEPARATOR */,\r\n  0x200c,\r\n  0x200c /* ZERO WIDTH NON-JOINER */,\r\n  0x200d,\r\n  0x200d /* ZERO WIDTH JOINER */,\r\n  0x2028,\r\n  0x2028 /* LINE SEPARATOR */,\r\n  0x2029,\r\n  0x2029 /* PARAGRAPH SEPARATOR */,\r\n  0x2060,\r\n  0x2060 /* WORD JOINER */,\r\n  0x2061,\r\n  0x2061 /* FUNCTION APPLICATION */,\r\n  0x2062,\r\n  0x2062 /* INVISIBLE TIMES */,\r\n  0x2063,\r\n  0x2063 /* INVISIBLE SEPARATOR */,\r\n  0x206a,\r\n  0x206f /* [CONTROL CHARACTERS] */,\r\n  0xfeff,\r\n  0xfeff /* ZERO WIDTH NO-BREAK SPACE */,\r\n  0xfff9,\r\n  0xfffc /* [CONTROL CHARACTERS] */,\r\n  0x1d173,\r\n  0x1d17a /* [MUSICAL CONTROL CHARACTERS] */\r\n];\r\n\r\nconst non_character_codepoints = [\r\n  /**\r\n   * C.4 Non-character code points\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.4\r\n   */\r\n  0xfdd0,\r\n  0xfdef /* [NONCHARACTER CODE POINTS] */,\r\n  0xfffe,\r\n  0xffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x1fffe,\r\n  0x1ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x2fffe,\r\n  0x2ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x3fffe,\r\n  0x3ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x4fffe,\r\n  0x4ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x5fffe,\r\n  0x5ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x6fffe,\r\n  0x6ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x7fffe,\r\n  0x7ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x8fffe,\r\n  0x8ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x9fffe,\r\n  0x9ffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xafffe,\r\n  0xaffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xbfffe,\r\n  0xbffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xcfffe,\r\n  0xcffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xdfffe,\r\n  0xdffff /* [NONCHARACTER CODE POINTS] */,\r\n  0xefffe,\r\n  0xeffff /* [NONCHARACTER CODE POINTS] */,\r\n  0x10fffe,\r\n  0x10ffff /* [NONCHARACTER CODE POINTS] */\r\n];\r\n\r\n/**\r\n * 2.3.  Prohibited Output\r\n */\r\nconst prohibited_characters = [\r\n  /**\r\n   * C.2.1 ASCII control characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.2.1\r\n   */\r\n  0,\r\n  0x001f /* [CONTROL CHARACTERS] */,\r\n  0x007f,\r\n  0x007f /* DELETE */,\r\n\r\n  /**\r\n   * C.8 Change display properties or are deprecated\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.8\r\n   */\r\n  0x0340,\r\n  0x0340 /* COMBINING GRAVE TONE MARK */,\r\n  0x0341,\r\n  0x0341 /* COMBINING ACUTE TONE MARK */,\r\n  0x200e,\r\n  0x200e /* LEFT-TO-RIGHT MARK */,\r\n  0x200f,\r\n  0x200f /* RIGHT-TO-LEFT MARK */,\r\n  0x202a,\r\n  0x202a /* LEFT-TO-RIGHT EMBEDDING */,\r\n  0x202b,\r\n  0x202b /* RIGHT-TO-LEFT EMBEDDING */,\r\n  0x202c,\r\n  0x202c /* POP DIRECTIONAL FORMATTING */,\r\n  0x202d,\r\n  0x202d /* LEFT-TO-RIGHT OVERRIDE */,\r\n  0x202e,\r\n  0x202e /* RIGHT-TO-LEFT OVERRIDE */,\r\n  0x206a,\r\n  0x206a /* INHIBIT SYMMETRIC SWAPPING */,\r\n  0x206b,\r\n  0x206b /* ACTIVATE SYMMETRIC SWAPPING */,\r\n  0x206c,\r\n  0x206c /* INHIBIT ARABIC FORM SHAPING */,\r\n  0x206d,\r\n  0x206d /* ACTIVATE ARABIC FORM SHAPING */,\r\n  0x206e,\r\n  0x206e /* NATIONAL DIGIT SHAPES */,\r\n  0x206f,\r\n  0x206f /* NOMINAL DIGIT SHAPES */,\r\n\r\n  /**\r\n   * C.7 Inappropriate for canonical representation\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.7\r\n   */\r\n  0x2ff0,\r\n  0x2ffb /* [IDEOGRAPHIC DESCRIPTION CHARACTERS] */,\r\n\r\n  /**\r\n   * C.5 Surrogate codes\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.5\r\n   */\r\n  0xd800,\r\n  0xdfff,\r\n\r\n  /**\r\n   * C.3 Private use\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.3\r\n   */\r\n  0xe000,\r\n  0xf8ff /* [PRIVATE USE, PLANE 0] */,\r\n\r\n  /**\r\n   * C.6 Inappropriate for plain text\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.6\r\n   */\r\n  0xfff9,\r\n  0xfff9 /* INTERLINEAR ANNOTATION ANCHOR */,\r\n  0xfffa,\r\n  0xfffa /* INTERLINEAR ANNOTATION SEPARATOR */,\r\n  0xfffb,\r\n  0xfffb /* INTERLINEAR ANNOTATION TERMINATOR */,\r\n  0xfffc,\r\n  0xfffc /* OBJECT REPLACEMENT CHARACTER */,\r\n  0xfffd,\r\n  0xfffd /* REPLACEMENT CHARACTER */,\r\n\r\n  /**\r\n   * C.9 Tagging characters\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.9\r\n   */\r\n  0xe0001,\r\n  0xe0001 /* LANGUAGE TAG */,\r\n  0xe0020,\r\n  0xe007f /* [TAGGING CHARACTERS] */,\r\n\r\n  /**\r\n   * C.3 Private use\r\n   * @link https://tools.ietf.org/html/rfc3454#appendix-C.3\r\n   */\r\n\r\n  0xf0000,\r\n  0xffffd /* [PRIVATE USE, PLANE 15] */,\r\n  0x100000,\r\n  0x10fffd /* [PRIVATE USE, PLANE 16] */\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isProhibitedCharacter = character =>\r\n  inRange(character, non_ASCII_space_characters) ||\r\n  inRange(character, prohibited_characters) ||\r\n  inRange(character, non_ASCII_controls_characters) ||\r\n  inRange(character, non_character_codepoints);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * D.1 Characters with bidirectional property \"R\" or \"AL\"\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-D.1\r\n */\r\nconst bidirectional_r_al = [\r\n  0x05be,\r\n  0x05be,\r\n  0x05c0,\r\n  0x05c0,\r\n  0x05c3,\r\n  0x05c3,\r\n  0x05d0,\r\n  0x05ea,\r\n  0x05f0,\r\n  0x05f4,\r\n  0x061b,\r\n  0x061b,\r\n  0x061f,\r\n  0x061f,\r\n  0x0621,\r\n  0x063a,\r\n  0x0640,\r\n  0x064a,\r\n  0x066d,\r\n  0x066f,\r\n  0x0671,\r\n  0x06d5,\r\n  0x06dd,\r\n  0x06dd,\r\n  0x06e5,\r\n  0x06e6,\r\n  0x06fa,\r\n  0x06fe,\r\n  0x0700,\r\n  0x070d,\r\n  0x0710,\r\n  0x0710,\r\n  0x0712,\r\n  0x072c,\r\n  0x0780,\r\n  0x07a5,\r\n  0x07b1,\r\n  0x07b1,\r\n  0x200f,\r\n  0x200f,\r\n  0xfb1d,\r\n  0xfb1d,\r\n  0xfb1f,\r\n  0xfb28,\r\n  0xfb2a,\r\n  0xfb36,\r\n  0xfb38,\r\n  0xfb3c,\r\n  0xfb3e,\r\n  0xfb3e,\r\n  0xfb40,\r\n  0xfb41,\r\n  0xfb43,\r\n  0xfb44,\r\n  0xfb46,\r\n  0xfbb1,\r\n  0xfbd3,\r\n  0xfd3d,\r\n  0xfd50,\r\n  0xfd8f,\r\n  0xfd92,\r\n  0xfdc7,\r\n  0xfdf0,\r\n  0xfdfc,\r\n  0xfe70,\r\n  0xfe74,\r\n  0xfe76,\r\n  0xfefc\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isBidirectionalRAL = character => inRange(character, bidirectional_r_al);\r\n\r\n// prettier-ignore-start\r\n/**\r\n * D.2 Characters with bidirectional property \"L\"\r\n * @link https://tools.ietf.org/html/rfc3454#appendix-D.2\r\n */\r\nconst bidirectional_l = [\r\n  0x0041,\r\n  0x005a,\r\n  0x0061,\r\n  0x007a,\r\n  0x00aa,\r\n  0x00aa,\r\n  0x00b5,\r\n  0x00b5,\r\n  0x00ba,\r\n  0x00ba,\r\n  0x00c0,\r\n  0x00d6,\r\n  0x00d8,\r\n  0x00f6,\r\n  0x00f8,\r\n  0x0220,\r\n  0x0222,\r\n  0x0233,\r\n  0x0250,\r\n  0x02ad,\r\n  0x02b0,\r\n  0x02b8,\r\n  0x02bb,\r\n  0x02c1,\r\n  0x02d0,\r\n  0x02d1,\r\n  0x02e0,\r\n  0x02e4,\r\n  0x02ee,\r\n  0x02ee,\r\n  0x037a,\r\n  0x037a,\r\n  0x0386,\r\n  0x0386,\r\n  0x0388,\r\n  0x038a,\r\n  0x038c,\r\n  0x038c,\r\n  0x038e,\r\n  0x03a1,\r\n  0x03a3,\r\n  0x03ce,\r\n  0x03d0,\r\n  0x03f5,\r\n  0x0400,\r\n  0x0482,\r\n  0x048a,\r\n  0x04ce,\r\n  0x04d0,\r\n  0x04f5,\r\n  0x04f8,\r\n  0x04f9,\r\n  0x0500,\r\n  0x050f,\r\n  0x0531,\r\n  0x0556,\r\n  0x0559,\r\n  0x055f,\r\n  0x0561,\r\n  0x0587,\r\n  0x0589,\r\n  0x0589,\r\n  0x0903,\r\n  0x0903,\r\n  0x0905,\r\n  0x0939,\r\n  0x093d,\r\n  0x0940,\r\n  0x0949,\r\n  0x094c,\r\n  0x0950,\r\n  0x0950,\r\n  0x0958,\r\n  0x0961,\r\n  0x0964,\r\n  0x0970,\r\n  0x0982,\r\n  0x0983,\r\n  0x0985,\r\n  0x098c,\r\n  0x098f,\r\n  0x0990,\r\n  0x0993,\r\n  0x09a8,\r\n  0x09aa,\r\n  0x09b0,\r\n  0x09b2,\r\n  0x09b2,\r\n  0x09b6,\r\n  0x09b9,\r\n  0x09be,\r\n  0x09c0,\r\n  0x09c7,\r\n  0x09c8,\r\n  0x09cb,\r\n  0x09cc,\r\n  0x09d7,\r\n  0x09d7,\r\n  0x09dc,\r\n  0x09dd,\r\n  0x09df,\r\n  0x09e1,\r\n  0x09e6,\r\n  0x09f1,\r\n  0x09f4,\r\n  0x09fa,\r\n  0x0a05,\r\n  0x0a0a,\r\n  0x0a0f,\r\n  0x0a10,\r\n  0x0a13,\r\n  0x0a28,\r\n  0x0a2a,\r\n  0x0a30,\r\n  0x0a32,\r\n  0x0a33,\r\n  0x0a35,\r\n  0x0a36,\r\n  0x0a38,\r\n  0x0a39,\r\n  0x0a3e,\r\n  0x0a40,\r\n  0x0a59,\r\n  0x0a5c,\r\n  0x0a5e,\r\n  0x0a5e,\r\n  0x0a66,\r\n  0x0a6f,\r\n  0x0a72,\r\n  0x0a74,\r\n  0x0a83,\r\n  0x0a83,\r\n  0x0a85,\r\n  0x0a8b,\r\n  0x0a8d,\r\n  0x0a8d,\r\n  0x0a8f,\r\n  0x0a91,\r\n  0x0a93,\r\n  0x0aa8,\r\n  0x0aaa,\r\n  0x0ab0,\r\n  0x0ab2,\r\n  0x0ab3,\r\n  0x0ab5,\r\n  0x0ab9,\r\n  0x0abd,\r\n  0x0ac0,\r\n  0x0ac9,\r\n  0x0ac9,\r\n  0x0acb,\r\n  0x0acc,\r\n  0x0ad0,\r\n  0x0ad0,\r\n  0x0ae0,\r\n  0x0ae0,\r\n  0x0ae6,\r\n  0x0aef,\r\n  0x0b02,\r\n  0x0b03,\r\n  0x0b05,\r\n  0x0b0c,\r\n  0x0b0f,\r\n  0x0b10,\r\n  0x0b13,\r\n  0x0b28,\r\n  0x0b2a,\r\n  0x0b30,\r\n  0x0b32,\r\n  0x0b33,\r\n  0x0b36,\r\n  0x0b39,\r\n  0x0b3d,\r\n  0x0b3e,\r\n  0x0b40,\r\n  0x0b40,\r\n  0x0b47,\r\n  0x0b48,\r\n  0x0b4b,\r\n  0x0b4c,\r\n  0x0b57,\r\n  0x0b57,\r\n  0x0b5c,\r\n  0x0b5d,\r\n  0x0b5f,\r\n  0x0b61,\r\n  0x0b66,\r\n  0x0b70,\r\n  0x0b83,\r\n  0x0b83,\r\n  0x0b85,\r\n  0x0b8a,\r\n  0x0b8e,\r\n  0x0b90,\r\n  0x0b92,\r\n  0x0b95,\r\n  0x0b99,\r\n  0x0b9a,\r\n  0x0b9c,\r\n  0x0b9c,\r\n  0x0b9e,\r\n  0x0b9f,\r\n  0x0ba3,\r\n  0x0ba4,\r\n  0x0ba8,\r\n  0x0baa,\r\n  0x0bae,\r\n  0x0bb5,\r\n  0x0bb7,\r\n  0x0bb9,\r\n  0x0bbe,\r\n  0x0bbf,\r\n  0x0bc1,\r\n  0x0bc2,\r\n  0x0bc6,\r\n  0x0bc8,\r\n  0x0bca,\r\n  0x0bcc,\r\n  0x0bd7,\r\n  0x0bd7,\r\n  0x0be7,\r\n  0x0bf2,\r\n  0x0c01,\r\n  0x0c03,\r\n  0x0c05,\r\n  0x0c0c,\r\n  0x0c0e,\r\n  0x0c10,\r\n  0x0c12,\r\n  0x0c28,\r\n  0x0c2a,\r\n  0x0c33,\r\n  0x0c35,\r\n  0x0c39,\r\n  0x0c41,\r\n  0x0c44,\r\n  0x0c60,\r\n  0x0c61,\r\n  0x0c66,\r\n  0x0c6f,\r\n  0x0c82,\r\n  0x0c83,\r\n  0x0c85,\r\n  0x0c8c,\r\n  0x0c8e,\r\n  0x0c90,\r\n  0x0c92,\r\n  0x0ca8,\r\n  0x0caa,\r\n  0x0cb3,\r\n  0x0cb5,\r\n  0x0cb9,\r\n  0x0cbe,\r\n  0x0cbe,\r\n  0x0cc0,\r\n  0x0cc4,\r\n  0x0cc7,\r\n  0x0cc8,\r\n  0x0cca,\r\n  0x0ccb,\r\n  0x0cd5,\r\n  0x0cd6,\r\n  0x0cde,\r\n  0x0cde,\r\n  0x0ce0,\r\n  0x0ce1,\r\n  0x0ce6,\r\n  0x0cef,\r\n  0x0d02,\r\n  0x0d03,\r\n  0x0d05,\r\n  0x0d0c,\r\n  0x0d0e,\r\n  0x0d10,\r\n  0x0d12,\r\n  0x0d28,\r\n  0x0d2a,\r\n  0x0d39,\r\n  0x0d3e,\r\n  0x0d40,\r\n  0x0d46,\r\n  0x0d48,\r\n  0x0d4a,\r\n  0x0d4c,\r\n  0x0d57,\r\n  0x0d57,\r\n  0x0d60,\r\n  0x0d61,\r\n  0x0d66,\r\n  0x0d6f,\r\n  0x0d82,\r\n  0x0d83,\r\n  0x0d85,\r\n  0x0d96,\r\n  0x0d9a,\r\n  0x0db1,\r\n  0x0db3,\r\n  0x0dbb,\r\n  0x0dbd,\r\n  0x0dbd,\r\n  0x0dc0,\r\n  0x0dc6,\r\n  0x0dcf,\r\n  0x0dd1,\r\n  0x0dd8,\r\n  0x0ddf,\r\n  0x0df2,\r\n  0x0df4,\r\n  0x0e01,\r\n  0x0e30,\r\n  0x0e32,\r\n  0x0e33,\r\n  0x0e40,\r\n  0x0e46,\r\n  0x0e4f,\r\n  0x0e5b,\r\n  0x0e81,\r\n  0x0e82,\r\n  0x0e84,\r\n  0x0e84,\r\n  0x0e87,\r\n  0x0e88,\r\n  0x0e8a,\r\n  0x0e8a,\r\n  0x0e8d,\r\n  0x0e8d,\r\n  0x0e94,\r\n  0x0e97,\r\n  0x0e99,\r\n  0x0e9f,\r\n  0x0ea1,\r\n  0x0ea3,\r\n  0x0ea5,\r\n  0x0ea5,\r\n  0x0ea7,\r\n  0x0ea7,\r\n  0x0eaa,\r\n  0x0eab,\r\n  0x0ead,\r\n  0x0eb0,\r\n  0x0eb2,\r\n  0x0eb3,\r\n  0x0ebd,\r\n  0x0ebd,\r\n  0x0ec0,\r\n  0x0ec4,\r\n  0x0ec6,\r\n  0x0ec6,\r\n  0x0ed0,\r\n  0x0ed9,\r\n  0x0edc,\r\n  0x0edd,\r\n  0x0f00,\r\n  0x0f17,\r\n  0x0f1a,\r\n  0x0f34,\r\n  0x0f36,\r\n  0x0f36,\r\n  0x0f38,\r\n  0x0f38,\r\n  0x0f3e,\r\n  0x0f47,\r\n  0x0f49,\r\n  0x0f6a,\r\n  0x0f7f,\r\n  0x0f7f,\r\n  0x0f85,\r\n  0x0f85,\r\n  0x0f88,\r\n  0x0f8b,\r\n  0x0fbe,\r\n  0x0fc5,\r\n  0x0fc7,\r\n  0x0fcc,\r\n  0x0fcf,\r\n  0x0fcf,\r\n  0x1000,\r\n  0x1021,\r\n  0x1023,\r\n  0x1027,\r\n  0x1029,\r\n  0x102a,\r\n  0x102c,\r\n  0x102c,\r\n  0x1031,\r\n  0x1031,\r\n  0x1038,\r\n  0x1038,\r\n  0x1040,\r\n  0x1057,\r\n  0x10a0,\r\n  0x10c5,\r\n  0x10d0,\r\n  0x10f8,\r\n  0x10fb,\r\n  0x10fb,\r\n  0x1100,\r\n  0x1159,\r\n  0x115f,\r\n  0x11a2,\r\n  0x11a8,\r\n  0x11f9,\r\n  0x1200,\r\n  0x1206,\r\n  0x1208,\r\n  0x1246,\r\n  0x1248,\r\n  0x1248,\r\n  0x124a,\r\n  0x124d,\r\n  0x1250,\r\n  0x1256,\r\n  0x1258,\r\n  0x1258,\r\n  0x125a,\r\n  0x125d,\r\n  0x1260,\r\n  0x1286,\r\n  0x1288,\r\n  0x1288,\r\n  0x128a,\r\n  0x128d,\r\n  0x1290,\r\n  0x12ae,\r\n  0x12b0,\r\n  0x12b0,\r\n  0x12b2,\r\n  0x12b5,\r\n  0x12b8,\r\n  0x12be,\r\n  0x12c0,\r\n  0x12c0,\r\n  0x12c2,\r\n  0x12c5,\r\n  0x12c8,\r\n  0x12ce,\r\n  0x12d0,\r\n  0x12d6,\r\n  0x12d8,\r\n  0x12ee,\r\n  0x12f0,\r\n  0x130e,\r\n  0x1310,\r\n  0x1310,\r\n  0x1312,\r\n  0x1315,\r\n  0x1318,\r\n  0x131e,\r\n  0x1320,\r\n  0x1346,\r\n  0x1348,\r\n  0x135a,\r\n  0x1361,\r\n  0x137c,\r\n  0x13a0,\r\n  0x13f4,\r\n  0x1401,\r\n  0x1676,\r\n  0x1681,\r\n  0x169a,\r\n  0x16a0,\r\n  0x16f0,\r\n  0x1700,\r\n  0x170c,\r\n  0x170e,\r\n  0x1711,\r\n  0x1720,\r\n  0x1731,\r\n  0x1735,\r\n  0x1736,\r\n  0x1740,\r\n  0x1751,\r\n  0x1760,\r\n  0x176c,\r\n  0x176e,\r\n  0x1770,\r\n  0x1780,\r\n  0x17b6,\r\n  0x17be,\r\n  0x17c5,\r\n  0x17c7,\r\n  0x17c8,\r\n  0x17d4,\r\n  0x17da,\r\n  0x17dc,\r\n  0x17dc,\r\n  0x17e0,\r\n  0x17e9,\r\n  0x1810,\r\n  0x1819,\r\n  0x1820,\r\n  0x1877,\r\n  0x1880,\r\n  0x18a8,\r\n  0x1e00,\r\n  0x1e9b,\r\n  0x1ea0,\r\n  0x1ef9,\r\n  0x1f00,\r\n  0x1f15,\r\n  0x1f18,\r\n  0x1f1d,\r\n  0x1f20,\r\n  0x1f45,\r\n  0x1f48,\r\n  0x1f4d,\r\n  0x1f50,\r\n  0x1f57,\r\n  0x1f59,\r\n  0x1f59,\r\n  0x1f5b,\r\n  0x1f5b,\r\n  0x1f5d,\r\n  0x1f5d,\r\n  0x1f5f,\r\n  0x1f7d,\r\n  0x1f80,\r\n  0x1fb4,\r\n  0x1fb6,\r\n  0x1fbc,\r\n  0x1fbe,\r\n  0x1fbe,\r\n  0x1fc2,\r\n  0x1fc4,\r\n  0x1fc6,\r\n  0x1fcc,\r\n  0x1fd0,\r\n  0x1fd3,\r\n  0x1fd6,\r\n  0x1fdb,\r\n  0x1fe0,\r\n  0x1fec,\r\n  0x1ff2,\r\n  0x1ff4,\r\n  0x1ff6,\r\n  0x1ffc,\r\n  0x200e,\r\n  0x200e,\r\n  0x2071,\r\n  0x2071,\r\n  0x207f,\r\n  0x207f,\r\n  0x2102,\r\n  0x2102,\r\n  0x2107,\r\n  0x2107,\r\n  0x210a,\r\n  0x2113,\r\n  0x2115,\r\n  0x2115,\r\n  0x2119,\r\n  0x211d,\r\n  0x2124,\r\n  0x2124,\r\n  0x2126,\r\n  0x2126,\r\n  0x2128,\r\n  0x2128,\r\n  0x212a,\r\n  0x212d,\r\n  0x212f,\r\n  0x2131,\r\n  0x2133,\r\n  0x2139,\r\n  0x213d,\r\n  0x213f,\r\n  0x2145,\r\n  0x2149,\r\n  0x2160,\r\n  0x2183,\r\n  0x2336,\r\n  0x237a,\r\n  0x2395,\r\n  0x2395,\r\n  0x249c,\r\n  0x24e9,\r\n  0x3005,\r\n  0x3007,\r\n  0x3021,\r\n  0x3029,\r\n  0x3031,\r\n  0x3035,\r\n  0x3038,\r\n  0x303c,\r\n  0x3041,\r\n  0x3096,\r\n  0x309d,\r\n  0x309f,\r\n  0x30a1,\r\n  0x30fa,\r\n  0x30fc,\r\n  0x30ff,\r\n  0x3105,\r\n  0x312c,\r\n  0x3131,\r\n  0x318e,\r\n  0x3190,\r\n  0x31b7,\r\n  0x31f0,\r\n  0x321c,\r\n  0x3220,\r\n  0x3243,\r\n  0x3260,\r\n  0x327b,\r\n  0x327f,\r\n  0x32b0,\r\n  0x32c0,\r\n  0x32cb,\r\n  0x32d0,\r\n  0x32fe,\r\n  0x3300,\r\n  0x3376,\r\n  0x337b,\r\n  0x33dd,\r\n  0x33e0,\r\n  0x33fe,\r\n  0x3400,\r\n  0x4db5,\r\n  0x4e00,\r\n  0x9fa5,\r\n  0xa000,\r\n  0xa48c,\r\n  0xac00,\r\n  0xd7a3,\r\n  0xd800,\r\n  0xfa2d,\r\n  0xfa30,\r\n  0xfa6a,\r\n  0xfb00,\r\n  0xfb06,\r\n  0xfb13,\r\n  0xfb17,\r\n  0xff21,\r\n  0xff3a,\r\n  0xff41,\r\n  0xff5a,\r\n  0xff66,\r\n  0xffbe,\r\n  0xffc2,\r\n  0xffc7,\r\n  0xffca,\r\n  0xffcf,\r\n  0xffd2,\r\n  0xffd7,\r\n  0xffda,\r\n  0xffdc,\r\n  0x10300,\r\n  0x1031e,\r\n  0x10320,\r\n  0x10323,\r\n  0x10330,\r\n  0x1034a,\r\n  0x10400,\r\n  0x10425,\r\n  0x10428,\r\n  0x1044d,\r\n  0x1d000,\r\n  0x1d0f5,\r\n  0x1d100,\r\n  0x1d126,\r\n  0x1d12a,\r\n  0x1d166,\r\n  0x1d16a,\r\n  0x1d172,\r\n  0x1d183,\r\n  0x1d184,\r\n  0x1d18c,\r\n  0x1d1a9,\r\n  0x1d1ae,\r\n  0x1d1dd,\r\n  0x1d400,\r\n  0x1d454,\r\n  0x1d456,\r\n  0x1d49c,\r\n  0x1d49e,\r\n  0x1d49f,\r\n  0x1d4a2,\r\n  0x1d4a2,\r\n  0x1d4a5,\r\n  0x1d4a6,\r\n  0x1d4a9,\r\n  0x1d4ac,\r\n  0x1d4ae,\r\n  0x1d4b9,\r\n  0x1d4bb,\r\n  0x1d4bb,\r\n  0x1d4bd,\r\n  0x1d4c0,\r\n  0x1d4c2,\r\n  0x1d4c3,\r\n  0x1d4c5,\r\n  0x1d505,\r\n  0x1d507,\r\n  0x1d50a,\r\n  0x1d50d,\r\n  0x1d514,\r\n  0x1d516,\r\n  0x1d51c,\r\n  0x1d51e,\r\n  0x1d539,\r\n  0x1d53b,\r\n  0x1d53e,\r\n  0x1d540,\r\n  0x1d544,\r\n  0x1d546,\r\n  0x1d546,\r\n  0x1d54a,\r\n  0x1d550,\r\n  0x1d552,\r\n  0x1d6a3,\r\n  0x1d6a8,\r\n  0x1d7c9,\r\n  0x20000,\r\n  0x2a6d6,\r\n  0x2f800,\r\n  0x2fa1d,\r\n  0xf0000,\r\n  0xffffd,\r\n  0x100000,\r\n  0x10fffd\r\n];\r\n// prettier-ignore-end\r\n\r\nconst isBidirectionalL = character => inRange(character, bidirectional_l);\r\n\r\nexport {\r\n  isUnassignedCodePoint,\r\n  isCommonlyMappedToNothing,\r\n  isNonASCIISpaceCharacter,\r\n  isProhibitedCharacter,\r\n  isBidirectionalRAL,\r\n  isBidirectionalL\r\n};\r\n", "import {\r\n  isUnassignedCodePoint,\r\n  isC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  isNonASCI<PERSON><PERSON><PERSON>haracter,\r\n  isProhibited<PERSON>haracter,\r\n  isBidirectionalRAL,\r\n  isBidirectionalL\r\n} from './lib/code-points';\r\n\r\n// 2.1.  Mapping\r\n\r\n/**\r\n * non-ASCII space characters [StringPrep, C.1.2] that can be\r\n * mapped to SPACE (U+0020)\r\n */\r\nconst mapping2space = isNonASCIISpaceCharacter;\r\n\r\n/**\r\n * the \"commonly mapped to nothing\" characters [StringPrep, B.1]\r\n * that can be mapped to nothing.\r\n */\r\nconst mapping2nothing = isCommonlyMappedToNothing;\r\n\r\n// utils\r\nconst getCodePoint = character => character.codePointAt(0);\r\nconst first = x => x[0];\r\nconst last = x => x[x.length - 1];\r\n\r\n/**\r\n * Convert provided string into an array of Unicode Code Points.\r\n * Based on https://stackoverflow.com/a/21409165/1556249\r\n * and https://www.npmjs.com/package/code-point-at.\r\n * @param {string} input\r\n * @returns {number[]}\r\n */\r\nfunction toCodePoints(input) {\r\n  const codepoints = [];\r\n  const size = input.length;\r\n\r\n  for (let i = 0; i < size; i += 1) {\r\n    const before = input.charCodeAt(i);\r\n\r\n    if (before >= 0xd800 && before <= 0xdbff && size > i + 1) {\r\n      const next = input.charCodeAt(i + 1);\r\n\r\n      if (next >= 0xdc00 && next <= 0xdfff) {\r\n        codepoints.push((before - 0xd800) * 0x400 + next - 0xdc00 + 0x10000);\r\n        i += 1;\r\n        continue;\r\n      }\r\n    }\r\n\r\n    codepoints.push(before);\r\n  }\r\n\r\n  return codepoints;\r\n}\r\n\r\n/**\r\n * SASLprep.\r\n * @param {string} input\r\n * @param {Object} opts\r\n * @param {boolean} opts.allowUnassigned\r\n * @returns {string}\r\n */\r\nfunction saslprep(input, opts = {}) {\r\n  if (typeof input !== 'string') {\r\n    throw new TypeError('Expected string.');\r\n  }\r\n\r\n  if (input.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  // 1. Map\r\n  const mapped_input = toCodePoints(input)\r\n    // 1.1 mapping to space\r\n    .map(character => (mapping2space(character) ? 0x20 : character))\r\n    // 1.2 mapping to nothing\r\n    .filter(character => !mapping2nothing(character));\r\n\r\n  // 2. Normalize\r\n  const normalized_input = String.fromCodePoint\r\n    .apply(null, mapped_input)\r\n    .normalize('NFKC');\r\n\r\n  const normalized_map = toCodePoints(normalized_input);\r\n\r\n  // 3. Prohibit\r\n  const hasProhibited = normalized_map.some(isProhibitedCharacter);\r\n\r\n  if (hasProhibited) {\r\n    throw new Error(\r\n      'Prohibited character, see https://tools.ietf.org/html/rfc4013#section-2.3'\r\n    );\r\n  }\r\n\r\n  // Unassigned Code Points\r\n  if (opts.allowUnassigned !== true) {\r\n    const hasUnassigned = normalized_map.some(isUnassignedCodePoint);\r\n\r\n    if (hasUnassigned) {\r\n      throw new Error(\r\n        'Unassigned code point, see https://tools.ietf.org/html/rfc4013#section-2.5'\r\n      );\r\n    }\r\n  }\r\n\r\n  // 4. check bidi\r\n\r\n  const hasBidiRAL = normalized_map.some(isBidirectionalRAL);\r\n\r\n  const hasBidiL = normalized_map.some(isBidirectionalL);\r\n\r\n  // 4.1 If a string contains any RandALCat character, the string MUST NOT\r\n  // contain any LCat character.\r\n  if (hasBidiRAL && hasBidiL) {\r\n    throw new Error(\r\n      'String must not contain RandALCat and LCat at the same time,' +\r\n        ' see https://tools.ietf.org/html/rfc3454#section-6'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * 4.2 If a string contains any RandALCat character, a RandALCat\r\n   * character MUST be the first character of the string, and a\r\n   * RandALCat character MUST be the last character of the string.\r\n   */\r\n\r\n  const isFirstBidiRAL = isBidirectionalRAL(\r\n    getCodePoint(first(normalized_input))\r\n  );\r\n  const isLastBidiRAL = isBidirectionalRAL(\r\n    getCodePoint(last(normalized_input))\r\n  );\r\n\r\n  if (hasBidiRAL && !(isFirstBidiRAL && isLastBidiRAL)) {\r\n    throw new Error(\r\n      'Bidirectional RandALCat character must be the first and the last' +\r\n        ' character of the string, see https://tools.ietf.org/html/rfc3454#section-6'\r\n    );\r\n  }\r\n\r\n  return normalized_input;\r\n}\r\n\r\nexport default saslprep;\r\n", "/*\r\n   PDFSecurity - represents PDF security settings\r\n   By <PERSON> <<EMAIL>>\r\n */\r\n\r\nimport CryptoJS from 'crypto-js';\r\nimport saslprep from './saslprep/index';\r\n\r\nclass PDFSecurity {\r\n  static generateFileID(info = {}) {\r\n    let infoStr = `${info.CreationDate.getTime()}\\n`;\r\n\r\n    for (let key in info) {\r\n      // eslint-disable-next-line no-prototype-builtins\r\n      if (!info.hasOwnProperty(key)) {\r\n        continue;\r\n      }\r\n      infoStr += `${key}: ${info[key].valueOf()}\\n`;\r\n    }\r\n\r\n    return wordArrayToBuffer(CryptoJS.MD5(infoStr));\r\n  }\r\n\r\n  static generateRandomWordArray(bytes) {\r\n    return CryptoJS.lib.WordArray.random(bytes);\r\n  }\r\n\r\n  static create(document, options = {}) {\r\n    if (!options.ownerPassword && !options.userPassword) {\r\n      return null;\r\n    }\r\n    return new PDFSecurity(document, options);\r\n  }\r\n\r\n  constructor(document, options = {}) {\r\n    if (!options.ownerPassword && !options.userPassword) {\r\n      throw new Error('None of owner password and user password is defined.');\r\n    }\r\n\r\n    this.document = document;\r\n    this._setupEncryption(options);\r\n  }\r\n\r\n  _setupEncryption(options) {\r\n    switch (options.pdfVersion) {\r\n      case '1.4':\r\n      case '1.5':\r\n        this.version = 2;\r\n        break;\r\n      case '1.6':\r\n      case '1.7':\r\n        this.version = 4;\r\n        break;\r\n      case '1.7ext3':\r\n        this.version = 5;\r\n        break;\r\n      default:\r\n        this.version = 1;\r\n        break;\r\n    }\r\n\r\n    const encDict = {\r\n      Filter: 'Standard'\r\n    };\r\n\r\n    switch (this.version) {\r\n      case 1:\r\n      case 2:\r\n      case 4:\r\n        this._setupEncryptionV1V2V4(this.version, encDict, options);\r\n        break;\r\n      case 5:\r\n        this._setupEncryptionV5(encDict, options);\r\n        break;\r\n    }\r\n\r\n    this.dictionary = this.document.ref(encDict);\r\n  }\r\n\r\n  _setupEncryptionV1V2V4(v, encDict, options) {\r\n    let r, permissions;\r\n    switch (v) {\r\n      case 1:\r\n        r = 2;\r\n        this.keyBits = 40;\r\n        permissions = getPermissionsR2(options.permissions);\r\n        break;\r\n      case 2:\r\n        r = 3;\r\n        this.keyBits = 128;\r\n        permissions = getPermissionsR3(options.permissions);\r\n        break;\r\n      case 4:\r\n        r = 4;\r\n        this.keyBits = 128;\r\n        permissions = getPermissionsR3(options.permissions);\r\n        break;\r\n    }\r\n\r\n    const paddedUserPassword = processPasswordR2R3R4(options.userPassword);\r\n    const paddedOwnerPassword = options.ownerPassword\r\n      ? processPasswordR2R3R4(options.ownerPassword)\r\n      : paddedUserPassword;\r\n\r\n    const ownerPasswordEntry = getOwnerPasswordR2R3R4(\r\n      r,\r\n      this.keyBits,\r\n      paddedUserPassword,\r\n      paddedOwnerPassword\r\n    );\r\n    this.encryptionKey = getEncryptionKeyR2R3R4(\r\n      r,\r\n      this.keyBits,\r\n      this.document._id,\r\n      paddedUserPassword,\r\n      ownerPasswordEntry,\r\n      permissions\r\n    );\r\n    let userPasswordEntry;\r\n    if (r === 2) {\r\n      userPasswordEntry = getUserPasswordR2(this.encryptionKey);\r\n    } else {\r\n      userPasswordEntry = getUserPasswordR3R4(\r\n        this.document._id,\r\n        this.encryptionKey\r\n      );\r\n    }\r\n\r\n    encDict.V = v;\r\n    if (v >= 2) {\r\n      encDict.Length = this.keyBits;\r\n    }\r\n    if (v === 4) {\r\n      encDict.CF = {\r\n        StdCF: {\r\n          AuthEvent: 'DocOpen',\r\n          CFM: 'AESV2',\r\n          Length: this.keyBits / 8\r\n        }\r\n      };\r\n      encDict.StmF = 'StdCF';\r\n      encDict.StrF = 'StdCF';\r\n    }\r\n    encDict.R = r;\r\n    encDict.O = wordArrayToBuffer(ownerPasswordEntry);\r\n    encDict.U = wordArrayToBuffer(userPasswordEntry);\r\n    encDict.P = permissions;\r\n  }\r\n\r\n  _setupEncryptionV5(encDict, options) {\r\n    this.keyBits = 256;\r\n    const permissions = getPermissionsR3(options.permissions);\r\n\r\n    const processedUserPassword = processPasswordR5(options.userPassword);\r\n    const processedOwnerPassword = options.ownerPassword\r\n      ? processPasswordR5(options.ownerPassword)\r\n      : processedUserPassword;\r\n\r\n    this.encryptionKey = getEncryptionKeyR5(\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const userPasswordEntry = getUserPasswordR5(\r\n      processedUserPassword,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const userKeySalt = CryptoJS.lib.WordArray.create(\r\n      userPasswordEntry.words.slice(10, 12),\r\n      8\r\n    );\r\n    const userEncryptionKeyEntry = getUserEncryptionKeyR5(\r\n      processedUserPassword,\r\n      userKeySalt,\r\n      this.encryptionKey\r\n    );\r\n    const ownerPasswordEntry = getOwnerPasswordR5(\r\n      processedOwnerPassword,\r\n      userPasswordEntry,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n    const ownerKeySalt = CryptoJS.lib.WordArray.create(\r\n      ownerPasswordEntry.words.slice(10, 12),\r\n      8\r\n    );\r\n    const ownerEncryptionKeyEntry = getOwnerEncryptionKeyR5(\r\n      processedOwnerPassword,\r\n      ownerKeySalt,\r\n      userPasswordEntry,\r\n      this.encryptionKey\r\n    );\r\n    const permsEntry = getEncryptedPermissionsR5(\r\n      permissions,\r\n      this.encryptionKey,\r\n      PDFSecurity.generateRandomWordArray\r\n    );\r\n\r\n    encDict.V = 5;\r\n    encDict.Length = this.keyBits;\r\n    encDict.CF = {\r\n      StdCF: {\r\n        AuthEvent: 'DocOpen',\r\n        CFM: 'AESV3',\r\n        Length: this.keyBits / 8\r\n      }\r\n    };\r\n    encDict.StmF = 'StdCF';\r\n    encDict.StrF = 'StdCF';\r\n    encDict.R = 5;\r\n    encDict.O = wordArrayToBuffer(ownerPasswordEntry);\r\n    encDict.OE = wordArrayToBuffer(ownerEncryptionKeyEntry);\r\n    encDict.U = wordArrayToBuffer(userPasswordEntry);\r\n    encDict.UE = wordArrayToBuffer(userEncryptionKeyEntry);\r\n    encDict.P = permissions;\r\n    encDict.Perms = wordArrayToBuffer(permsEntry);\r\n  }\r\n\r\n  getEncryptFn(obj, gen) {\r\n    let digest;\r\n    if (this.version < 5) {\r\n      digest = this.encryptionKey\r\n        .clone()\r\n        .concat(\r\n          CryptoJS.lib.WordArray.create(\r\n            [\r\n              ((obj & 0xff) << 24) |\r\n                ((obj & 0xff00) << 8) |\r\n                ((obj >> 8) & 0xff00) |\r\n                (gen & 0xff),\r\n              (gen & 0xff00) << 16\r\n            ],\r\n            5\r\n          )\r\n        );\r\n    }\r\n\r\n    if (this.version === 1 || this.version === 2) {\r\n      let key = CryptoJS.MD5(digest);\r\n      key.sigBytes = Math.min(16, this.keyBits / 8 + 5);\r\n      return buffer =>\r\n        wordArrayToBuffer(\r\n          CryptoJS.RC4.encrypt(CryptoJS.lib.WordArray.create(buffer), key)\r\n            .ciphertext\r\n        );\r\n    }\r\n\r\n    let key;\r\n    if (this.version === 4) {\r\n      key = CryptoJS.MD5(\r\n        digest.concat(CryptoJS.lib.WordArray.create([0x73416c54], 4))\r\n      );\r\n    } else {\r\n      key = this.encryptionKey;\r\n    }\r\n\r\n    const iv = PDFSecurity.generateRandomWordArray(16);\r\n    const options = {\r\n      mode: CryptoJS.mode.CBC,\r\n      padding: CryptoJS.pad.Pkcs7,\r\n      iv\r\n    };\r\n\r\n    return buffer =>\r\n      wordArrayToBuffer(\r\n        iv\r\n          .clone()\r\n          .concat(\r\n            CryptoJS.AES.encrypt(\r\n              CryptoJS.lib.WordArray.create(buffer),\r\n              key,\r\n              options\r\n            ).ciphertext\r\n          )\r\n      );\r\n  }\r\n\r\n  end() {\r\n    this.dictionary.end();\r\n  }\r\n}\r\n\r\nfunction getPermissionsR2(permissionObject = {}) {\r\n  let permissions = 0xffffffc0 >> 0;\r\n  if (permissionObject.printing) {\r\n    permissions |= 0b000000000100;\r\n  }\r\n  if (permissionObject.modifying) {\r\n    permissions |= 0b000000001000;\r\n  }\r\n  if (permissionObject.copying) {\r\n    permissions |= 0b000000010000;\r\n  }\r\n  if (permissionObject.annotating) {\r\n    permissions |= 0b000000100000;\r\n  }\r\n  return permissions;\r\n}\r\n\r\nfunction getPermissionsR3(permissionObject = {}) {\r\n  let permissions = 0xfffff0c0 >> 0;\r\n  if (permissionObject.printing === 'lowResolution') {\r\n    permissions |= 0b000000000100;\r\n  }\r\n  if (permissionObject.printing === 'highResolution') {\r\n    permissions |= 0b100000000100;\r\n  }\r\n  if (permissionObject.modifying) {\r\n    permissions |= 0b000000001000;\r\n  }\r\n  if (permissionObject.copying) {\r\n    permissions |= 0b000000010000;\r\n  }\r\n  if (permissionObject.annotating) {\r\n    permissions |= 0b000000100000;\r\n  }\r\n  if (permissionObject.fillingForms) {\r\n    permissions |= 0b000100000000;\r\n  }\r\n  if (permissionObject.contentAccessibility) {\r\n    permissions |= 0b001000000000;\r\n  }\r\n  if (permissionObject.documentAssembly) {\r\n    permissions |= 0b010000000000;\r\n  }\r\n  return permissions;\r\n}\r\n\r\nfunction getUserPasswordR2(encryptionKey) {\r\n  return CryptoJS.RC4.encrypt(processPasswordR2R3R4(), encryptionKey)\r\n    .ciphertext;\r\n}\r\n\r\nfunction getUserPasswordR3R4(documentId, encryptionKey) {\r\n  const key = encryptionKey.clone();\r\n  let cipher = CryptoJS.MD5(\r\n    processPasswordR2R3R4().concat(CryptoJS.lib.WordArray.create(documentId))\r\n  );\r\n  for (let i = 0; i < 20; i++) {\r\n    const xorRound = Math.ceil(key.sigBytes / 4);\r\n    for (let j = 0; j < xorRound; j++) {\r\n      key.words[j] =\r\n        encryptionKey.words[j] ^ (i | (i << 8) | (i << 16) | (i << 24));\r\n    }\r\n    cipher = CryptoJS.RC4.encrypt(cipher, key).ciphertext;\r\n  }\r\n  return cipher.concat(CryptoJS.lib.WordArray.create(null, 16));\r\n}\r\n\r\nfunction getOwnerPasswordR2R3R4(\r\n  r,\r\n  keyBits,\r\n  paddedUserPassword,\r\n  paddedOwnerPassword\r\n) {\r\n  let digest = paddedOwnerPassword;\r\n  let round = r >= 3 ? 51 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    digest = CryptoJS.MD5(digest);\r\n  }\r\n\r\n  const key = digest.clone();\r\n  key.sigBytes = keyBits / 8;\r\n  let cipher = paddedUserPassword;\r\n  round = r >= 3 ? 20 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    const xorRound = Math.ceil(key.sigBytes / 4);\r\n    for (let j = 0; j < xorRound; j++) {\r\n      key.words[j] = digest.words[j] ^ (i | (i << 8) | (i << 16) | (i << 24));\r\n    }\r\n    cipher = CryptoJS.RC4.encrypt(cipher, key).ciphertext;\r\n  }\r\n  return cipher;\r\n}\r\n\r\nfunction getEncryptionKeyR2R3R4(\r\n  r,\r\n  keyBits,\r\n  documentId,\r\n  paddedUserPassword,\r\n  ownerPasswordEntry,\r\n  permissions\r\n) {\r\n  let key = paddedUserPassword\r\n    .clone()\r\n    .concat(ownerPasswordEntry)\r\n    .concat(CryptoJS.lib.WordArray.create([lsbFirstWord(permissions)], 4))\r\n    .concat(CryptoJS.lib.WordArray.create(documentId));\r\n  const round = r >= 3 ? 51 : 1;\r\n  for (let i = 0; i < round; i++) {\r\n    key = CryptoJS.MD5(key);\r\n    key.sigBytes = keyBits / 8;\r\n  }\r\n  return key;\r\n}\r\n\r\nfunction getUserPasswordR5(processedUserPassword, generateRandomWordArray) {\r\n  const validationSalt = generateRandomWordArray(8);\r\n  const keySalt = generateRandomWordArray(8);\r\n  return CryptoJS.SHA256(processedUserPassword.clone().concat(validationSalt))\r\n    .concat(validationSalt)\r\n    .concat(keySalt);\r\n}\r\n\r\nfunction getUserEncryptionKeyR5(\r\n  processedUserPassword,\r\n  userKeySalt,\r\n  encryptionKey\r\n) {\r\n  const key = CryptoJS.SHA256(\r\n    processedUserPassword.clone().concat(userKeySalt)\r\n  );\r\n  const options = {\r\n    mode: CryptoJS.mode.CBC,\r\n    padding: CryptoJS.pad.NoPadding,\r\n    iv: CryptoJS.lib.WordArray.create(null, 16)\r\n  };\r\n  return CryptoJS.AES.encrypt(encryptionKey, key, options).ciphertext;\r\n}\r\n\r\nfunction getOwnerPasswordR5(\r\n  processedOwnerPassword,\r\n  userPasswordEntry,\r\n  generateRandomWordArray\r\n) {\r\n  const validationSalt = generateRandomWordArray(8);\r\n  const keySalt = generateRandomWordArray(8);\r\n  return CryptoJS.SHA256(\r\n    processedOwnerPassword\r\n      .clone()\r\n      .concat(validationSalt)\r\n      .concat(userPasswordEntry)\r\n  )\r\n    .concat(validationSalt)\r\n    .concat(keySalt);\r\n}\r\n\r\nfunction getOwnerEncryptionKeyR5(\r\n  processedOwnerPassword,\r\n  ownerKeySalt,\r\n  userPasswordEntry,\r\n  encryptionKey\r\n) {\r\n  const key = CryptoJS.SHA256(\r\n    processedOwnerPassword\r\n      .clone()\r\n      .concat(ownerKeySalt)\r\n      .concat(userPasswordEntry)\r\n  );\r\n  const options = {\r\n    mode: CryptoJS.mode.CBC,\r\n    padding: CryptoJS.pad.NoPadding,\r\n    iv: CryptoJS.lib.WordArray.create(null, 16)\r\n  };\r\n  return CryptoJS.AES.encrypt(encryptionKey, key, options).ciphertext;\r\n}\r\n\r\nfunction getEncryptionKeyR5(generateRandomWordArray) {\r\n  return generateRandomWordArray(32);\r\n}\r\n\r\nfunction getEncryptedPermissionsR5(\r\n  permissions,\r\n  encryptionKey,\r\n  generateRandomWordArray\r\n) {\r\n  const cipher = CryptoJS.lib.WordArray.create(\r\n    [lsbFirstWord(permissions), 0xffffffff, 0x54616462],\r\n    12\r\n  ).concat(generateRandomWordArray(4));\r\n  const options = {\r\n    mode: CryptoJS.mode.ECB,\r\n    padding: CryptoJS.pad.NoPadding\r\n  };\r\n  return CryptoJS.AES.encrypt(cipher, encryptionKey, options).ciphertext;\r\n}\r\n\r\nfunction processPasswordR2R3R4(password = '') {\r\n  const out = Buffer.alloc(32);\r\n  const length = password.length;\r\n  let index = 0;\r\n  while (index < length && index < 32) {\r\n    const code = password.charCodeAt(index);\r\n    if (code > 0xff) {\r\n      throw new Error('Password contains one or more invalid characters.');\r\n    }\r\n    out[index] = code;\r\n    index++;\r\n  }\r\n  while (index < 32) {\r\n    out[index] = PASSWORD_PADDING[index - length];\r\n    index++;\r\n  }\r\n  return CryptoJS.lib.WordArray.create(out);\r\n}\r\n\r\nfunction processPasswordR5(password = '') {\r\n  password = unescape(encodeURIComponent(saslprep(password)));\r\n  const length = Math.min(127, password.length);\r\n  const out = Buffer.alloc(length);\r\n\r\n  for (let i = 0; i < length; i++) {\r\n    out[i] = password.charCodeAt(i);\r\n  }\r\n\r\n  return CryptoJS.lib.WordArray.create(out);\r\n}\r\n\r\nfunction lsbFirstWord(data) {\r\n  return (\r\n    ((data & 0xff) << 24) |\r\n    ((data & 0xff00) << 8) |\r\n    ((data >> 8) & 0xff00) |\r\n    ((data >> 24) & 0xff)\r\n  );\r\n}\r\n\r\nfunction wordArrayToBuffer(wordArray) {\r\n  const byteArray = [];\r\n  for (let i = 0; i < wordArray.sigBytes; i++) {\r\n    byteArray.push(\r\n      (wordArray.words[Math.floor(i / 4)] >> (8 * (3 - (i % 4)))) & 0xff\r\n    );\r\n  }\r\n  return Buffer.from(byteArray);\r\n}\r\n\r\nconst PASSWORD_PADDING = [\r\n  0x28,\r\n  0xbf,\r\n  0x4e,\r\n  0x5e,\r\n  0x4e,\r\n  0x75,\r\n  0x8a,\r\n  0x41,\r\n  0x64,\r\n  0x00,\r\n  0x4e,\r\n  0x56,\r\n  0xff,\r\n  0xfa,\r\n  0x01,\r\n  0x08,\r\n  0x2e,\r\n  0x2e,\r\n  0x00,\r\n  0xb6,\r\n  0xd0,\r\n  0x68,\r\n  0x3e,\r\n  0x80,\r\n  0x2f,\r\n  0x0c,\r\n  0xa9,\r\n  0xfe,\r\n  0x64,\r\n  0x53,\r\n  0x69,\r\n  0x7a\r\n];\r\n\r\nexport default PDFSecurity;\r\n", "import PDFObject from './object';\r\n\r\nconst { number } = PDFObject;\r\n\r\nclass PDFGradient {\r\n  constructor(doc) {\r\n    this.doc = doc;\r\n    this.stops = [];\r\n    this.embedded = false;\r\n    this.transform = [1, 0, 0, 1, 0, 0];\r\n  }\r\n\r\n  stop(pos, color, opacity) {\r\n    if (opacity == null) {\r\n      opacity = 1;\r\n    }\r\n    color = this.doc._normalizeColor(color);\r\n\r\n    if (this.stops.length === 0) {\r\n      if (color.length === 3) {\r\n        this._colorSpace = 'DeviceRGB';\r\n      } else if (color.length === 4) {\r\n        this._colorSpace = 'DeviceCMYK';\r\n      } else if (color.length === 1) {\r\n        this._colorSpace = 'DeviceGray';\r\n      } else {\r\n        throw new Error('Unknown color space');\r\n      }\r\n    } else if (\r\n      (this._colorSpace === 'DeviceRGB' && color.length !== 3) ||\r\n      (this._colorSpace === 'DeviceCMYK' && color.length !== 4) ||\r\n      (this._colorSpace === 'DeviceGray' && color.length !== 1)\r\n    ) {\r\n      throw new Error('All gradient stops must use the same color space');\r\n    }\r\n\r\n    opacity = Math.max(0, Math.min(1, opacity));\r\n    this.stops.push([pos, color, opacity]);\r\n    return this;\r\n  }\r\n\r\n  setTransform(m11, m12, m21, m22, dx, dy) {\r\n    this.transform = [m11, m12, m21, m22, dx, dy];\r\n    return this;\r\n  }\r\n\r\n  embed(m) {\r\n    let fn;\r\n    const stopsLength = this.stops.length;\r\n    if (stopsLength === 0) {\r\n      return;\r\n    }\r\n    this.embedded = true;\r\n    this.matrix = m;\r\n\r\n    // if the last stop comes before 100%, add a copy at 100%\r\n    const last = this.stops[stopsLength - 1];\r\n    if (last[0] < 1) {\r\n      this.stops.push([1, last[1], last[2]]);\r\n    }\r\n\r\n    const bounds = [];\r\n    const encode = [];\r\n    const stops = [];\r\n\r\n    for (let i = 0; i < stopsLength - 1; i++) {\r\n      encode.push(0, 1);\r\n      if (i + 2 !== stopsLength) {\r\n        bounds.push(this.stops[i + 1][0]);\r\n      }\r\n\r\n      fn = this.doc.ref({\r\n        FunctionType: 2,\r\n        Domain: [0, 1],\r\n        C0: this.stops[i + 0][1],\r\n        C1: this.stops[i + 1][1],\r\n        N: 1\r\n      });\r\n\r\n      stops.push(fn);\r\n      fn.end();\r\n    }\r\n\r\n    // if there are only two stops, we don't need a stitching function\r\n    if (stopsLength === 1) {\r\n      fn = stops[0];\r\n    } else {\r\n      fn = this.doc.ref({\r\n        FunctionType: 3, // stitching function\r\n        Domain: [0, 1],\r\n        Functions: stops,\r\n        Bounds: bounds,\r\n        Encode: encode\r\n      });\r\n\r\n      fn.end();\r\n    }\r\n\r\n    this.id = `Sh${++this.doc._gradCount}`;\r\n\r\n    const shader = this.shader(fn);\r\n    shader.end();\r\n\r\n    const pattern = this.doc.ref({\r\n      Type: 'Pattern',\r\n      PatternType: 2,\r\n      Shading: shader,\r\n      Matrix: this.matrix.map(number)\r\n    });\r\n\r\n    pattern.end();\r\n\r\n    if (this.stops.some(stop => stop[2] < 1)) {\r\n      let grad = this.opacityGradient();\r\n      grad._colorSpace = 'DeviceGray';\r\n\r\n      for (let stop of this.stops) {\r\n        grad.stop(stop[0], [stop[2]]);\r\n      }\r\n\r\n      grad = grad.embed(this.matrix);\r\n\r\n      const pageBBox = [0, 0, this.doc.page.width, this.doc.page.height];\r\n\r\n      const form = this.doc.ref({\r\n        Type: 'XObject',\r\n        Subtype: 'Form',\r\n        FormType: 1,\r\n        BBox: pageBBox,\r\n        Group: {\r\n          Type: 'Group',\r\n          S: 'Transparency',\r\n          CS: 'DeviceGray'\r\n        },\r\n        Resources: {\r\n          ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI'],\r\n          Pattern: {\r\n            Sh1: grad\r\n          }\r\n        }\r\n      });\r\n\r\n      form.write('/Pattern cs /Sh1 scn');\r\n      form.end(`${pageBBox.join(' ')} re f`);\r\n\r\n      const gstate = this.doc.ref({\r\n        Type: 'ExtGState',\r\n        SMask: {\r\n          Type: 'Mask',\r\n          S: 'Luminosity',\r\n          G: form\r\n        }\r\n      });\r\n\r\n      gstate.end();\r\n\r\n      const opacityPattern = this.doc.ref({\r\n        Type: 'Pattern',\r\n        PatternType: 1,\r\n        PaintType: 1,\r\n        TilingType: 2,\r\n        BBox: pageBBox,\r\n        XStep: pageBBox[2],\r\n        YStep: pageBBox[3],\r\n        Resources: {\r\n          ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI'],\r\n          Pattern: {\r\n            Sh1: pattern\r\n          },\r\n          ExtGState: {\r\n            Gs1: gstate\r\n          }\r\n        }\r\n      });\r\n\r\n      opacityPattern.write('/Gs1 gs /Pattern cs /Sh1 scn');\r\n      opacityPattern.end(`${pageBBox.join(' ')} re f`);\r\n\r\n      this.doc.page.patterns[this.id] = opacityPattern;\r\n    } else {\r\n      this.doc.page.patterns[this.id] = pattern;\r\n    }\r\n\r\n    return pattern;\r\n  }\r\n\r\n  apply(op) {\r\n    // apply gradient transform to existing document ctm\r\n    const [m0, m1, m2, m3, m4, m5] = this.doc._ctm;\r\n    const [m11, m12, m21, m22, dx, dy] = this.transform;\r\n    const m = [\r\n      m0 * m11 + m2 * m12,\r\n      m1 * m11 + m3 * m12,\r\n      m0 * m21 + m2 * m22,\r\n      m1 * m21 + m3 * m22,\r\n      m0 * dx + m2 * dy + m4,\r\n      m1 * dx + m3 * dy + m5\r\n    ];\r\n\r\n    if (!this.embedded || m.join(' ') !== this.matrix.join(' ')) {\r\n      this.embed(m);\r\n    }\r\n    return this.doc.addContent(`/${this.id} ${op}`);\r\n  }\r\n}\r\n\r\nclass PDFLinearGradient extends PDFGradient {\r\n  constructor(doc, x1, y1, x2, y2) {\r\n    super(doc);\r\n    this.x1 = x1;\r\n    this.y1 = y1;\r\n    this.x2 = x2;\r\n    this.y2 = y2;\r\n  }\r\n\r\n  shader(fn) {\r\n    return this.doc.ref({\r\n      ShadingType: 2,\r\n      ColorSpace: this._colorSpace,\r\n      Coords: [this.x1, this.y1, this.x2, this.y2],\r\n      Function: fn,\r\n      Extend: [true, true]\r\n    });\r\n  }\r\n\r\n  opacityGradient() {\r\n    return new PDFLinearGradient(this.doc, this.x1, this.y1, this.x2, this.y2);\r\n  }\r\n}\r\n\r\nclass PDFRadialGradient extends PDFGradient {\r\n  constructor(doc, x1, y1, r1, x2, y2, r2) {\r\n    super(doc);\r\n    this.doc = doc;\r\n    this.x1 = x1;\r\n    this.y1 = y1;\r\n    this.r1 = r1;\r\n    this.x2 = x2;\r\n    this.y2 = y2;\r\n    this.r2 = r2;\r\n  }\r\n\r\n  shader(fn) {\r\n    return this.doc.ref({\r\n      ShadingType: 3,\r\n      ColorSpace: this._colorSpace,\r\n      Coords: [this.x1, this.y1, this.r1, this.x2, this.y2, this.r2],\r\n      Function: fn,\r\n      Extend: [true, true]\r\n    });\r\n  }\r\n\r\n  opacityGradient() {\r\n    return new PDFRadialGradient(\r\n      this.doc,\r\n      this.x1,\r\n      this.y1,\r\n      this.r1,\r\n      this.x2,\r\n      this.y2,\r\n      this.r2\r\n    );\r\n  }\r\n}\r\n\r\nexport default { PDFGradient, PDFLinearGradient, PDFRadialGradient };\r\n", "import Gradient from '../gradient';\r\n\r\nconst { PDFGradient, PDFLinearGradient, PDFRadialGradient } = Gradient;\r\n\r\nexport default {\r\n  initColor() {\r\n    // The opacity dictionaries\r\n    this._opacityRegistry = {};\r\n    this._opacityCount = 0;\r\n    return (this._gradCount = 0);\r\n  },\r\n\r\n  _normalizeColor(color) {\r\n    if (color instanceof PDFGradient) {\r\n      return color;\r\n    }\r\n\r\n    if (typeof color === 'string') {\r\n      if (color.charAt(0) === '#') {\r\n        if (color.length === 4) {\r\n          color = color.replace(\r\n            /#([0-9A-F])([0-9A-F])([0-9A-F])/i,\r\n            '#$1$1$2$2$3$3'\r\n          );\r\n        }\r\n        const hex = parseInt(color.slice(1), 16);\r\n        color = [hex >> 16, (hex >> 8) & 0xff, hex & 0xff];\r\n      } else if (namedColors[color]) {\r\n        color = namedColors[color];\r\n      }\r\n    }\r\n\r\n    if (Array.isArray(color)) {\r\n      // RGB\r\n      if (color.length === 3) {\r\n        color = color.map(part => part / 255);\r\n        // CMYK\r\n      } else if (color.length === 4) {\r\n        color = color.map(part => part / 100);\r\n      }\r\n      return color;\r\n    }\r\n\r\n    return null;\r\n  },\r\n\r\n  _setColor(color, stroke) {\r\n    color = this._normalizeColor(color);\r\n    if (!color) {\r\n      return false;\r\n    }\r\n\r\n    const op = stroke ? 'SCN' : 'scn';\r\n\r\n    if (color instanceof PDFGradient) {\r\n      this._setColorSpace('Pattern', stroke);\r\n      color.apply(op);\r\n    } else {\r\n      const space = color.length === 4 ? 'DeviceCMYK' : 'DeviceRGB';\r\n      this._setColorSpace(space, stroke);\r\n\r\n      color = color.join(' ');\r\n      this.addContent(`${color} ${op}`);\r\n    }\r\n\r\n    return true;\r\n  },\r\n\r\n  _setColorSpace(space, stroke) {\r\n    const op = stroke ? 'CS' : 'cs';\r\n    return this.addContent(`/${space} ${op}`);\r\n  },\r\n\r\n  fillColor(color, opacity) {\r\n    const set = this._setColor(color, false);\r\n    if (set) {\r\n      this.fillOpacity(opacity);\r\n    }\r\n\r\n    // save this for text wrapper, which needs to reset\r\n    // the fill color on new pages\r\n    this._fillColor = [color, opacity];\r\n    return this;\r\n  },\r\n\r\n  strokeColor(color, opacity) {\r\n    const set = this._setColor(color, true);\r\n    if (set) {\r\n      this.strokeOpacity(opacity);\r\n    }\r\n    return this;\r\n  },\r\n\r\n  opacity(opacity) {\r\n    this._doOpacity(opacity, opacity);\r\n    return this;\r\n  },\r\n\r\n  fillOpacity(opacity) {\r\n    this._doOpacity(opacity, null);\r\n    return this;\r\n  },\r\n\r\n  strokeOpacity(opacity) {\r\n    this._doOpacity(null, opacity);\r\n    return this;\r\n  },\r\n\r\n  _doOpacity(fillOpacity, strokeOpacity) {\r\n    let dictionary, name;\r\n    if (fillOpacity == null && strokeOpacity == null) {\r\n      return;\r\n    }\r\n\r\n    if (fillOpacity != null) {\r\n      fillOpacity = Math.max(0, Math.min(1, fillOpacity));\r\n    }\r\n    if (strokeOpacity != null) {\r\n      strokeOpacity = Math.max(0, Math.min(1, strokeOpacity));\r\n    }\r\n    const key = `${fillOpacity}_${strokeOpacity}`;\r\n\r\n    if (this._opacityRegistry[key]) {\r\n      [dictionary, name] = this._opacityRegistry[key];\r\n    } else {\r\n      dictionary = { Type: 'ExtGState' };\r\n\r\n      if (fillOpacity != null) {\r\n        dictionary.ca = fillOpacity;\r\n      }\r\n      if (strokeOpacity != null) {\r\n        dictionary.CA = strokeOpacity;\r\n      }\r\n\r\n      dictionary = this.ref(dictionary);\r\n      dictionary.end();\r\n      const id = ++this._opacityCount;\r\n      name = `Gs${id}`;\r\n      this._opacityRegistry[key] = [dictionary, name];\r\n    }\r\n\r\n    this.page.ext_gstates[name] = dictionary;\r\n    return this.addContent(`/${name} gs`);\r\n  },\r\n\r\n  linearGradient(x1, y1, x2, y2) {\r\n    return new PDFLinearGradient(this, x1, y1, x2, y2);\r\n  },\r\n\r\n  radialGradient(x1, y1, r1, x2, y2, r2) {\r\n    return new PDFRadialGradient(this, x1, y1, r1, x2, y2, r2);\r\n  }\r\n};\r\n\r\nvar namedColors = {\r\n  aliceblue: [240, 248, 255],\r\n  antiquewhite: [250, 235, 215],\r\n  aqua: [0, 255, 255],\r\n  aquamarine: [127, 255, 212],\r\n  azure: [240, 255, 255],\r\n  beige: [245, 245, 220],\r\n  bisque: [255, 228, 196],\r\n  black: [0, 0, 0],\r\n  blanchedalmond: [255, 235, 205],\r\n  blue: [0, 0, 255],\r\n  blueviolet: [138, 43, 226],\r\n  brown: [165, 42, 42],\r\n  burlywood: [222, 184, 135],\r\n  cadetblue: [95, 158, 160],\r\n  chartreuse: [127, 255, 0],\r\n  chocolate: [210, 105, 30],\r\n  coral: [255, 127, 80],\r\n  cornflowerblue: [100, 149, 237],\r\n  cornsilk: [255, 248, 220],\r\n  crimson: [220, 20, 60],\r\n  cyan: [0, 255, 255],\r\n  darkblue: [0, 0, 139],\r\n  darkcyan: [0, 139, 139],\r\n  darkgoldenrod: [184, 134, 11],\r\n  darkgray: [169, 169, 169],\r\n  darkgreen: [0, 100, 0],\r\n  darkgrey: [169, 169, 169],\r\n  darkkhaki: [189, 183, 107],\r\n  darkmagenta: [139, 0, 139],\r\n  darkolivegreen: [85, 107, 47],\r\n  darkorange: [255, 140, 0],\r\n  darkorchid: [153, 50, 204],\r\n  darkred: [139, 0, 0],\r\n  darksalmon: [233, 150, 122],\r\n  darkseagreen: [143, 188, 143],\r\n  darkslateblue: [72, 61, 139],\r\n  darkslategray: [47, 79, 79],\r\n  darkslategrey: [47, 79, 79],\r\n  darkturquoise: [0, 206, 209],\r\n  darkviolet: [148, 0, 211],\r\n  deeppink: [255, 20, 147],\r\n  deepskyblue: [0, 191, 255],\r\n  dimgray: [105, 105, 105],\r\n  dimgrey: [105, 105, 105],\r\n  dodgerblue: [30, 144, 255],\r\n  firebrick: [178, 34, 34],\r\n  floralwhite: [255, 250, 240],\r\n  forestgreen: [34, 139, 34],\r\n  fuchsia: [255, 0, 255],\r\n  gainsboro: [220, 220, 220],\r\n  ghostwhite: [248, 248, 255],\r\n  gold: [255, 215, 0],\r\n  goldenrod: [218, 165, 32],\r\n  gray: [128, 128, 128],\r\n  grey: [128, 128, 128],\r\n  green: [0, 128, 0],\r\n  greenyellow: [173, 255, 47],\r\n  honeydew: [240, 255, 240],\r\n  hotpink: [255, 105, 180],\r\n  indianred: [205, 92, 92],\r\n  indigo: [75, 0, 130],\r\n  ivory: [255, 255, 240],\r\n  khaki: [240, 230, 140],\r\n  lavender: [230, 230, 250],\r\n  lavenderblush: [255, 240, 245],\r\n  lawngreen: [124, 252, 0],\r\n  lemonchiffon: [255, 250, 205],\r\n  lightblue: [173, 216, 230],\r\n  lightcoral: [240, 128, 128],\r\n  lightcyan: [224, 255, 255],\r\n  lightgoldenrodyellow: [250, 250, 210],\r\n  lightgray: [211, 211, 211],\r\n  lightgreen: [144, 238, 144],\r\n  lightgrey: [211, 211, 211],\r\n  lightpink: [255, 182, 193],\r\n  lightsalmon: [255, 160, 122],\r\n  lightseagreen: [32, 178, 170],\r\n  lightskyblue: [135, 206, 250],\r\n  lightslategray: [119, 136, 153],\r\n  lightslategrey: [119, 136, 153],\r\n  lightsteelblue: [176, 196, 222],\r\n  lightyellow: [255, 255, 224],\r\n  lime: [0, 255, 0],\r\n  limegreen: [50, 205, 50],\r\n  linen: [250, 240, 230],\r\n  magenta: [255, 0, 255],\r\n  maroon: [128, 0, 0],\r\n  mediumaquamarine: [102, 205, 170],\r\n  mediumblue: [0, 0, 205],\r\n  mediumorchid: [186, 85, 211],\r\n  mediumpurple: [147, 112, 219],\r\n  mediumseagreen: [60, 179, 113],\r\n  mediumslateblue: [123, 104, 238],\r\n  mediumspringgreen: [0, 250, 154],\r\n  mediumturquoise: [72, 209, 204],\r\n  mediumvioletred: [199, 21, 133],\r\n  midnightblue: [25, 25, 112],\r\n  mintcream: [245, 255, 250],\r\n  mistyrose: [255, 228, 225],\r\n  moccasin: [255, 228, 181],\r\n  navajowhite: [255, 222, 173],\r\n  navy: [0, 0, 128],\r\n  oldlace: [253, 245, 230],\r\n  olive: [128, 128, 0],\r\n  olivedrab: [107, 142, 35],\r\n  orange: [255, 165, 0],\r\n  orangered: [255, 69, 0],\r\n  orchid: [218, 112, 214],\r\n  palegoldenrod: [238, 232, 170],\r\n  palegreen: [152, 251, 152],\r\n  paleturquoise: [175, 238, 238],\r\n  palevioletred: [219, 112, 147],\r\n  papayawhip: [255, 239, 213],\r\n  peachpuff: [255, 218, 185],\r\n  peru: [205, 133, 63],\r\n  pink: [255, 192, 203],\r\n  plum: [221, 160, 221],\r\n  powderblue: [176, 224, 230],\r\n  purple: [128, 0, 128],\r\n  red: [255, 0, 0],\r\n  rosybrown: [188, 143, 143],\r\n  royalblue: [65, 105, 225],\r\n  saddlebrown: [139, 69, 19],\r\n  salmon: [250, 128, 114],\r\n  sandybrown: [244, 164, 96],\r\n  seagreen: [46, 139, 87],\r\n  seashell: [255, 245, 238],\r\n  sienna: [160, 82, 45],\r\n  silver: [192, 192, 192],\r\n  skyblue: [135, 206, 235],\r\n  slateblue: [106, 90, 205],\r\n  slategray: [112, 128, 144],\r\n  slategrey: [112, 128, 144],\r\n  snow: [255, 250, 250],\r\n  springgreen: [0, 255, 127],\r\n  steelblue: [70, 130, 180],\r\n  tan: [210, 180, 140],\r\n  teal: [0, 128, 128],\r\n  thistle: [216, 191, 216],\r\n  tomato: [255, 99, 71],\r\n  turquoise: [64, 224, 208],\r\n  violet: [238, 130, 238],\r\n  wheat: [245, 222, 179],\r\n  white: [255, 255, 255],\r\n  whitesmoke: [245, 245, 245],\r\n  yellow: [255, 255, 0],\r\n  yellowgreen: [154, 205, 50]\r\n};\r\n", "let cx, cy, px, py, sx, sy;\r\n\r\ncx = cy = px = py = sx = sy = 0;\r\n\r\nconst parameters = {\r\n  A: 7,\r\n  a: 7,\r\n  C: 6,\r\n  c: 6,\r\n  H: 1,\r\n  h: 1,\r\n  L: 2,\r\n  l: 2,\r\n  M: 2,\r\n  m: 2,\r\n  Q: 4,\r\n  q: 4,\r\n  S: 4,\r\n  s: 4,\r\n  T: 2,\r\n  t: 2,\r\n  V: 1,\r\n  v: 1,\r\n  Z: 0,\r\n  z: 0\r\n};\r\n\r\nconst parse = function(path) {\r\n  let cmd;\r\n  const ret = [];\r\n  let args = [];\r\n  let curArg = '';\r\n  let foundDecimal = false;\r\n  let params = 0;\r\n\r\n  for (let c of path) {\r\n    if (parameters[c] != null) {\r\n      params = parameters[c];\r\n      if (cmd) {\r\n        // save existing command\r\n        if (curArg.length > 0) {\r\n          args[args.length] = +curArg;\r\n        }\r\n        ret[ret.length] = { cmd, args };\r\n\r\n        args = [];\r\n        curArg = '';\r\n        foundDecimal = false;\r\n      }\r\n\r\n      cmd = c;\r\n    } else if (\r\n      [' ', ','].includes(c) ||\r\n      (c === '-' && curArg.length > 0 && curArg[curArg.length - 1] !== 'e') ||\r\n      (c === '.' && foundDecimal)\r\n    ) {\r\n      if (curArg.length === 0) {\r\n        continue;\r\n      }\r\n\r\n      if (args.length === params) {\r\n        // handle reused commands\r\n        ret[ret.length] = { cmd, args };\r\n        args = [+curArg];\r\n\r\n        // handle assumed commands\r\n        if (cmd === 'M') {\r\n          cmd = 'L';\r\n        }\r\n        if (cmd === 'm') {\r\n          cmd = 'l';\r\n        }\r\n      } else {\r\n        args[args.length] = +curArg;\r\n      }\r\n\r\n      foundDecimal = c === '.';\r\n\r\n      // fix for negative numbers or repeated decimals with no delimeter between commands\r\n      curArg = ['-', '.'].includes(c) ? c : '';\r\n    } else {\r\n      curArg += c;\r\n      if (c === '.') {\r\n        foundDecimal = true;\r\n      }\r\n    }\r\n  }\r\n\r\n  // add the last command\r\n  if (curArg.length > 0) {\r\n    if (args.length === params) {\r\n      // handle reused commands\r\n      ret[ret.length] = { cmd, args };\r\n      args = [+curArg];\r\n\r\n      // handle assumed commands\r\n      if (cmd === 'M') {\r\n        cmd = 'L';\r\n      }\r\n      if (cmd === 'm') {\r\n        cmd = 'l';\r\n      }\r\n    } else {\r\n      args[args.length] = +curArg;\r\n    }\r\n  }\r\n\r\n  ret[ret.length] = { cmd, args };\r\n\r\n  return ret;\r\n};\r\n\r\nconst apply = function(commands, doc) {\r\n  // current point, control point, and subpath starting point\r\n  cx = cy = px = py = sx = sy = 0;\r\n\r\n  // run the commands\r\n  for (let i = 0; i < commands.length; i++) {\r\n    const c = commands[i];\r\n    if (typeof runners[c.cmd] === 'function') {\r\n      runners[c.cmd](doc, c.args);\r\n    }\r\n  }\r\n};\r\n\r\nconst runners = {\r\n  M(doc, a) {\r\n    cx = a[0];\r\n    cy = a[1];\r\n    px = py = null;\r\n    sx = cx;\r\n    sy = cy;\r\n    return doc.moveTo(cx, cy);\r\n  },\r\n\r\n  m(doc, a) {\r\n    cx += a[0];\r\n    cy += a[1];\r\n    px = py = null;\r\n    sx = cx;\r\n    sy = cy;\r\n    return doc.moveTo(cx, cy);\r\n  },\r\n\r\n  C(doc, a) {\r\n    cx = a[4];\r\n    cy = a[5];\r\n    px = a[2];\r\n    py = a[3];\r\n    return doc.bezierCurveTo(...a);\r\n  },\r\n\r\n  c(doc, a) {\r\n    doc.bezierCurveTo(\r\n      a[0] + cx,\r\n      a[1] + cy,\r\n      a[2] + cx,\r\n      a[3] + cy,\r\n      a[4] + cx,\r\n      a[5] + cy\r\n    );\r\n    px = cx + a[2];\r\n    py = cy + a[3];\r\n    cx += a[4];\r\n    return (cy += a[5]);\r\n  },\r\n\r\n  S(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    }\r\n\r\n    doc.bezierCurveTo(cx - (px - cx), cy - (py - cy), a[0], a[1], a[2], a[3]);\r\n    px = a[0];\r\n    py = a[1];\r\n    cx = a[2];\r\n    return (cy = a[3]);\r\n  },\r\n\r\n  s(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    }\r\n\r\n    doc.bezierCurveTo(\r\n      cx - (px - cx),\r\n      cy - (py - cy),\r\n      cx + a[0],\r\n      cy + a[1],\r\n      cx + a[2],\r\n      cy + a[3]\r\n    );\r\n    px = cx + a[0];\r\n    py = cy + a[1];\r\n    cx += a[2];\r\n    return (cy += a[3]);\r\n  },\r\n\r\n  Q(doc, a) {\r\n    px = a[0];\r\n    py = a[1];\r\n    cx = a[2];\r\n    cy = a[3];\r\n    return doc.quadraticCurveTo(a[0], a[1], cx, cy);\r\n  },\r\n\r\n  q(doc, a) {\r\n    doc.quadraticCurveTo(a[0] + cx, a[1] + cy, a[2] + cx, a[3] + cy);\r\n    px = cx + a[0];\r\n    py = cy + a[1];\r\n    cx += a[2];\r\n    return (cy += a[3]);\r\n  },\r\n\r\n  T(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    } else {\r\n      px = cx - (px - cx);\r\n      py = cy - (py - cy);\r\n    }\r\n\r\n    doc.quadraticCurveTo(px, py, a[0], a[1]);\r\n    px = cx - (px - cx);\r\n    py = cy - (py - cy);\r\n    cx = a[0];\r\n    return (cy = a[1]);\r\n  },\r\n\r\n  t(doc, a) {\r\n    if (px === null) {\r\n      px = cx;\r\n      py = cy;\r\n    } else {\r\n      px = cx - (px - cx);\r\n      py = cy - (py - cy);\r\n    }\r\n\r\n    doc.quadraticCurveTo(px, py, cx + a[0], cy + a[1]);\r\n    cx += a[0];\r\n    return (cy += a[1]);\r\n  },\r\n\r\n  A(doc, a) {\r\n    solveArc(doc, cx, cy, a);\r\n    cx = a[5];\r\n    return (cy = a[6]);\r\n  },\r\n\r\n  a(doc, a) {\r\n    a[5] += cx;\r\n    a[6] += cy;\r\n    solveArc(doc, cx, cy, a);\r\n    cx = a[5];\r\n    return (cy = a[6]);\r\n  },\r\n\r\n  L(doc, a) {\r\n    cx = a[0];\r\n    cy = a[1];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  l(doc, a) {\r\n    cx += a[0];\r\n    cy += a[1];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  H(doc, a) {\r\n    cx = a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  h(doc, a) {\r\n    cx += a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  V(doc, a) {\r\n    cy = a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  v(doc, a) {\r\n    cy += a[0];\r\n    px = py = null;\r\n    return doc.lineTo(cx, cy);\r\n  },\r\n\r\n  Z(doc) {\r\n    doc.closePath();\r\n    cx = sx;\r\n    return (cy = sy);\r\n  },\r\n\r\n  z(doc) {\r\n    doc.closePath();\r\n    cx = sx;\r\n    return (cy = sy);\r\n  }\r\n};\r\n\r\nconst solveArc = function(doc, x, y, coords) {\r\n  const [rx, ry, rot, large, sweep, ex, ey] = coords;\r\n  const segs = arcToSegments(ex, ey, rx, ry, large, sweep, rot, x, y);\r\n\r\n  for (let seg of segs) {\r\n    const bez = segmentToBezier(...seg);\r\n    doc.bezierCurveTo(...bez);\r\n  }\r\n};\r\n\r\n// from Inkscape svgtopdf, thanks!\r\nconst arcToSegments = function(x, y, rx, ry, large, sweep, rotateX, ox, oy) {\r\n  const th = rotateX * (Math.PI / 180);\r\n  const sin_th = Math.sin(th);\r\n  const cos_th = Math.cos(th);\r\n  rx = Math.abs(rx);\r\n  ry = Math.abs(ry);\r\n  px = cos_th * (ox - x) * 0.5 + sin_th * (oy - y) * 0.5;\r\n  py = cos_th * (oy - y) * 0.5 - sin_th * (ox - x) * 0.5;\r\n  let pl = (px * px) / (rx * rx) + (py * py) / (ry * ry);\r\n  if (pl > 1) {\r\n    pl = Math.sqrt(pl);\r\n    rx *= pl;\r\n    ry *= pl;\r\n  }\r\n\r\n  const a00 = cos_th / rx;\r\n  const a01 = sin_th / rx;\r\n  const a10 = -sin_th / ry;\r\n  const a11 = cos_th / ry;\r\n  const x0 = a00 * ox + a01 * oy;\r\n  const y0 = a10 * ox + a11 * oy;\r\n  const x1 = a00 * x + a01 * y;\r\n  const y1 = a10 * x + a11 * y;\r\n\r\n  const d = (x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0);\r\n  let sfactor_sq = 1 / d - 0.25;\r\n  if (sfactor_sq < 0) {\r\n    sfactor_sq = 0;\r\n  }\r\n  let sfactor = Math.sqrt(sfactor_sq);\r\n  if (sweep === large) {\r\n    sfactor = -sfactor;\r\n  }\r\n\r\n  const xc = 0.5 * (x0 + x1) - sfactor * (y1 - y0);\r\n  const yc = 0.5 * (y0 + y1) + sfactor * (x1 - x0);\r\n\r\n  const th0 = Math.atan2(y0 - yc, x0 - xc);\r\n  const th1 = Math.atan2(y1 - yc, x1 - xc);\r\n\r\n  let th_arc = th1 - th0;\r\n  if (th_arc < 0 && sweep === 1) {\r\n    th_arc += 2 * Math.PI;\r\n  } else if (th_arc > 0 && sweep === 0) {\r\n    th_arc -= 2 * Math.PI;\r\n  }\r\n\r\n  const segments = Math.ceil(Math.abs(th_arc / (Math.PI * 0.5 + 0.001)));\r\n  const result = [];\r\n\r\n  for (let i = 0; i < segments; i++) {\r\n    const th2 = th0 + (i * th_arc) / segments;\r\n    const th3 = th0 + ((i + 1) * th_arc) / segments;\r\n    result[i] = [xc, yc, th2, th3, rx, ry, sin_th, cos_th];\r\n  }\r\n\r\n  return result;\r\n};\r\n\r\nconst segmentToBezier = function(cx, cy, th0, th1, rx, ry, sin_th, cos_th) {\r\n  const a00 = cos_th * rx;\r\n  const a01 = -sin_th * ry;\r\n  const a10 = sin_th * rx;\r\n  const a11 = cos_th * ry;\r\n\r\n  const th_half = 0.5 * (th1 - th0);\r\n  const t =\r\n    ((8 / 3) * Math.sin(th_half * 0.5) * Math.sin(th_half * 0.5)) /\r\n    Math.sin(th_half);\r\n  const x1 = cx + Math.cos(th0) - t * Math.sin(th0);\r\n  const y1 = cy + Math.sin(th0) + t * Math.cos(th0);\r\n  const x3 = cx + Math.cos(th1);\r\n  const y3 = cy + Math.sin(th1);\r\n  const x2 = x3 + t * Math.sin(th1);\r\n  const y2 = y3 - t * Math.cos(th1);\r\n\r\n  return [\r\n    a00 * x1 + a01 * y1,\r\n    a10 * x1 + a11 * y1,\r\n    a00 * x2 + a01 * y2,\r\n    a10 * x2 + a11 * y2,\r\n    a00 * x3 + a01 * y3,\r\n    a10 * x3 + a11 * y3\r\n  ];\r\n};\r\n\r\nclass SVGPath {\r\n  static apply(doc, path) {\r\n    const commands = parse(path);\r\n    apply(commands, doc);\r\n  }\r\n}\r\n\r\nexport default SVGPath;\r\n", "import SVGPath from '../path';\r\nimport PDFObject from '../object';\r\n\r\nconst { number } = PDFObject;\r\n\r\n// This constant is used to approximate a symmetrical arc using a cubic\r\n// Bezier curve.\r\nconst KAPPA = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);\r\nexport default {\r\n  initVector() {\r\n    this._ctm = [1, 0, 0, 1, 0, 0]; // current transformation matrix\r\n    return (this._ctmStack = []);\r\n  },\r\n\r\n  save() {\r\n    this._ctmStack.push(this._ctm.slice());\r\n    // TODO: save/restore colorspace and styles so not setting it unnessesarily all the time?\r\n    return this.addContent('q');\r\n  },\r\n\r\n  restore() {\r\n    this._ctm = this._ctmStack.pop() || [1, 0, 0, 1, 0, 0];\r\n    return this.addContent('Q');\r\n  },\r\n\r\n  closePath() {\r\n    return this.addContent('h');\r\n  },\r\n\r\n  lineWidth(w) {\r\n    return this.addContent(`${number(w)} w`);\r\n  },\r\n\r\n  _CAP_STYLES: {\r\n    BUTT: 0,\r\n    ROUND: 1,\r\n    SQUARE: 2\r\n  },\r\n\r\n  lineCap(c) {\r\n    if (typeof c === 'string') {\r\n      c = this._CAP_STYLES[c.toUpperCase()];\r\n    }\r\n    return this.addContent(`${c} J`);\r\n  },\r\n\r\n  _JOIN_STYLES: {\r\n    MITER: 0,\r\n    ROUND: 1,\r\n    BEVEL: 2\r\n  },\r\n\r\n  lineJoin(j) {\r\n    if (typeof j === 'string') {\r\n      j = this._JOIN_STYLES[j.toUpperCase()];\r\n    }\r\n    return this.addContent(`${j} j`);\r\n  },\r\n\r\n  miterLimit(m) {\r\n    return this.addContent(`${number(m)} M`);\r\n  },\r\n\r\n  dash(length, options = {}) {\r\n    const originalLength = length;\r\n    if (!Array.isArray(length)) {\r\n      length = [length, options.space || length];\r\n    }\r\n\r\n    const valid = length.every(x => Number.isFinite(x) && x > 0);\r\n    if (!valid) {\r\n      throw new Error(\r\n        `dash(${JSON.stringify(originalLength)}, ${JSON.stringify(\r\n          options\r\n        )}) invalid, lengths must be numeric and greater than zero`\r\n      );\r\n    }\r\n\r\n    length = length.map(number).join(' ');\r\n    return this.addContent(`[${length}] ${number(options.phase || 0)} d`);\r\n  },\r\n\r\n  undash() {\r\n    return this.addContent('[] 0 d');\r\n  },\r\n\r\n  moveTo(x, y) {\r\n    return this.addContent(`${number(x)} ${number(y)} m`);\r\n  },\r\n\r\n  lineTo(x, y) {\r\n    return this.addContent(`${number(x)} ${number(y)} l`);\r\n  },\r\n\r\n  bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y) {\r\n    return this.addContent(\r\n      `${number(cp1x)} ${number(cp1y)} ${number(cp2x)} ${number(cp2y)} ${number(\r\n        x\r\n      )} ${number(y)} c`\r\n    );\r\n  },\r\n\r\n  quadraticCurveTo(cpx, cpy, x, y) {\r\n    return this.addContent(\r\n      `${number(cpx)} ${number(cpy)} ${number(x)} ${number(y)} v`\r\n    );\r\n  },\r\n\r\n  rect(x, y, w, h) {\r\n    return this.addContent(\r\n      `${number(x)} ${number(y)} ${number(w)} ${number(h)} re`\r\n    );\r\n  },\r\n\r\n  roundedRect(x, y, w, h, r) {\r\n    if (r == null) {\r\n      r = 0;\r\n    }\r\n    r = Math.min(r, 0.5 * w, 0.5 * h);\r\n\r\n    // amount to inset control points from corners (see `ellipse`)\r\n    const c = r * (1.0 - KAPPA);\r\n\r\n    this.moveTo(x + r, y);\r\n    this.lineTo(x + w - r, y);\r\n    this.bezierCurveTo(x + w - c, y, x + w, y + c, x + w, y + r);\r\n    this.lineTo(x + w, y + h - r);\r\n    this.bezierCurveTo(x + w, y + h - c, x + w - c, y + h, x + w - r, y + h);\r\n    this.lineTo(x + r, y + h);\r\n    this.bezierCurveTo(x + c, y + h, x, y + h - c, x, y + h - r);\r\n    this.lineTo(x, y + r);\r\n    this.bezierCurveTo(x, y + c, x + c, y, x + r, y);\r\n    return this.closePath();\r\n  },\r\n\r\n  ellipse(x, y, r1, r2) {\r\n    // based on http://stackoverflow.com/questions/2172798/how-to-draw-an-oval-in-html5-canvas/2173084#2173084\r\n    if (r2 == null) {\r\n      r2 = r1;\r\n    }\r\n    x -= r1;\r\n    y -= r2;\r\n    const ox = r1 * KAPPA;\r\n    const oy = r2 * KAPPA;\r\n    const xe = x + r1 * 2;\r\n    const ye = y + r2 * 2;\r\n    const xm = x + r1;\r\n    const ym = y + r2;\r\n\r\n    this.moveTo(x, ym);\r\n    this.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);\r\n    this.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);\r\n    this.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);\r\n    this.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);\r\n    return this.closePath();\r\n  },\r\n\r\n  circle(x, y, radius) {\r\n    return this.ellipse(x, y, radius);\r\n  },\r\n\r\n  arc(x, y, radius, startAngle, endAngle, anticlockwise) {\r\n    if (anticlockwise == null) {\r\n      anticlockwise = false;\r\n    }\r\n    const TWO_PI = 2.0 * Math.PI;\r\n    const HALF_PI = 0.5 * Math.PI;\r\n\r\n    let deltaAng = endAngle - startAngle;\r\n\r\n    if (Math.abs(deltaAng) > TWO_PI) {\r\n      // draw only full circle if more than that is specified\r\n      deltaAng = TWO_PI;\r\n    } else if (deltaAng !== 0 && anticlockwise !== deltaAng < 0) {\r\n      // necessary to flip direction of rendering\r\n      const dir = anticlockwise ? -1 : 1;\r\n      deltaAng = dir * TWO_PI + deltaAng;\r\n    }\r\n\r\n    const numSegs = Math.ceil(Math.abs(deltaAng) / HALF_PI);\r\n    const segAng = deltaAng / numSegs;\r\n    const handleLen = (segAng / HALF_PI) * KAPPA * radius;\r\n    let curAng = startAngle;\r\n\r\n    // component distances between anchor point and control point\r\n    let deltaCx = -Math.sin(curAng) * handleLen;\r\n    let deltaCy = Math.cos(curAng) * handleLen;\r\n\r\n    // anchor point\r\n    let ax = x + Math.cos(curAng) * radius;\r\n    let ay = y + Math.sin(curAng) * radius;\r\n\r\n    // calculate and render segments\r\n    this.moveTo(ax, ay);\r\n\r\n    for (let segIdx = 0; segIdx < numSegs; segIdx++) {\r\n      // starting control point\r\n      const cp1x = ax + deltaCx;\r\n      const cp1y = ay + deltaCy;\r\n\r\n      // step angle\r\n      curAng += segAng;\r\n\r\n      // next anchor point\r\n      ax = x + Math.cos(curAng) * radius;\r\n      ay = y + Math.sin(curAng) * radius;\r\n\r\n      // next control point delta\r\n      deltaCx = -Math.sin(curAng) * handleLen;\r\n      deltaCy = Math.cos(curAng) * handleLen;\r\n\r\n      // ending control point\r\n      const cp2x = ax - deltaCx;\r\n      const cp2y = ay - deltaCy;\r\n\r\n      // render segment\r\n      this.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, ax, ay);\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  polygon(...points) {\r\n    this.moveTo(...(points.shift() || []));\r\n    for (let point of points) {\r\n      this.lineTo(...(point || []));\r\n    }\r\n    return this.closePath();\r\n  },\r\n\r\n  path(path) {\r\n    SVGPath.apply(this, path);\r\n    return this;\r\n  },\r\n\r\n  _windingRule(rule) {\r\n    if (/even-?odd/.test(rule)) {\r\n      return '*';\r\n    }\r\n\r\n    return '';\r\n  },\r\n\r\n  fill(color, rule) {\r\n    if (/(even-?odd)|(non-?zero)/.test(color)) {\r\n      rule = color;\r\n      color = null;\r\n    }\r\n\r\n    if (color) {\r\n      this.fillColor(color);\r\n    }\r\n    return this.addContent(`f${this._windingRule(rule)}`);\r\n  },\r\n\r\n  stroke(color) {\r\n    if (color) {\r\n      this.strokeColor(color);\r\n    }\r\n    return this.addContent('S');\r\n  },\r\n\r\n  fillAndStroke(fillColor, strokeColor, rule) {\r\n    if (strokeColor == null) {\r\n      strokeColor = fillColor;\r\n    }\r\n    const isFillRule = /(even-?odd)|(non-?zero)/;\r\n    if (isFillRule.test(fillColor)) {\r\n      rule = fillColor;\r\n      fillColor = null;\r\n    }\r\n\r\n    if (isFillRule.test(strokeColor)) {\r\n      rule = strokeColor;\r\n      strokeColor = fillColor;\r\n    }\r\n\r\n    if (fillColor) {\r\n      this.fillColor(fillColor);\r\n      this.strokeColor(strokeColor);\r\n    }\r\n\r\n    return this.addContent(`B${this._windingRule(rule)}`);\r\n  },\r\n\r\n  clip(rule) {\r\n    return this.addContent(`W${this._windingRule(rule)} n`);\r\n  },\r\n\r\n  transform(m11, m12, m21, m22, dx, dy) {\r\n    // keep track of the current transformation matrix\r\n    const m = this._ctm;\r\n    const [m0, m1, m2, m3, m4, m5] = m;\r\n    m[0] = m0 * m11 + m2 * m12;\r\n    m[1] = m1 * m11 + m3 * m12;\r\n    m[2] = m0 * m21 + m2 * m22;\r\n    m[3] = m1 * m21 + m3 * m22;\r\n    m[4] = m0 * dx + m2 * dy + m4;\r\n    m[5] = m1 * dx + m3 * dy + m5;\r\n\r\n    const values = [m11, m12, m21, m22, dx, dy].map(v => number(v)).join(' ');\r\n    return this.addContent(`${values} cm`);\r\n  },\r\n\r\n  translate(x, y) {\r\n    return this.transform(1, 0, 0, 1, x, y);\r\n  },\r\n\r\n  rotate(angle, options = {}) {\r\n    let y;\r\n    const rad = (angle * Math.PI) / 180;\r\n    const cos = Math.cos(rad);\r\n    const sin = Math.sin(rad);\r\n    let x = (y = 0);\r\n\r\n    if (options.origin != null) {\r\n      [x, y] = options.origin;\r\n      const x1 = x * cos - y * sin;\r\n      const y1 = x * sin + y * cos;\r\n      x -= x1;\r\n      y -= y1;\r\n    }\r\n\r\n    return this.transform(cos, sin, -sin, cos, x, y);\r\n  },\r\n\r\n  scale(xFactor, yFactor, options = {}) {\r\n    let y;\r\n    if (yFactor == null) {\r\n      yFactor = xFactor;\r\n    }\r\n    if (typeof yFactor === 'object') {\r\n      options = yFactor;\r\n      yFactor = xFactor;\r\n    }\r\n\r\n    let x = (y = 0);\r\n    if (options.origin != null) {\r\n      [x, y] = options.origin;\r\n      x -= xFactor * x;\r\n      y -= yFactor * y;\r\n    }\r\n\r\n    return this.transform(xFactor, 0, 0, yFactor, x, y);\r\n  }\r\n};\r\n", "import fs from 'fs';\r\n\r\nconst WIN_ANSI_MAP = {\r\n  402: 131,\r\n  8211: 150,\r\n  8212: 151,\r\n  8216: 145,\r\n  8217: 146,\r\n  8218: 130,\r\n  8220: 147,\r\n  8221: 148,\r\n  8222: 132,\r\n  8224: 134,\r\n  8225: 135,\r\n  8226: 149,\r\n  8230: 133,\r\n  8364: 128,\r\n  8240: 137,\r\n  8249: 139,\r\n  8250: 155,\r\n  710: 136,\r\n  8482: 153,\r\n  338: 140,\r\n  339: 156,\r\n  732: 152,\r\n  352: 138,\r\n  353: 154,\r\n  376: 159,\r\n  381: 142,\r\n  382: 158\r\n};\r\n\r\nconst characters = `\\\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n.notdef       .notdef        .notdef        .notdef\r\n  \r\nspace         exclam         quotedbl       numbersign\r\ndollar        percent        ampersand      quotesingle\r\nparenleft     parenright     asterisk       plus\r\ncomma         hyphen         period         slash\r\nzero          one            two            three\r\nfour          five           six            seven\r\neight         nine           colon          semicolon\r\nless          equal          greater        question\r\n  \r\nat            A              B              C\r\nD             E              F              G\r\nH             I              J              K\r\nL             M              N              O\r\nP             Q              R              S\r\nT             U              V              W\r\nX             Y              Z              bracketleft\r\nbackslash     bracketright   asciicircum    underscore\r\n  \r\ngrave         a              b              c\r\nd             e              f              g\r\nh             i              j              k\r\nl             m              n              o\r\np             q              r              s\r\nt             u              v              w\r\nx             y              z              braceleft\r\nbar           braceright     asciitilde     .notdef\r\n  \r\nEuro          .notdef        quotesinglbase florin\r\nquotedblbase  ellipsis       dagger         daggerdbl\r\ncircumflex    perthousand    Scaron         guilsinglleft\r\nOE            .notdef        Zcaron         .notdef\r\n.notdef       quoteleft      quoteright     quotedblleft\r\nquotedblright bullet         endash         emdash\r\ntilde         trademark      scaron         guilsinglright\r\noe            .notdef        zcaron         ydieresis\r\n  \r\nspace         exclamdown     cent           sterling\r\ncurrency      yen            brokenbar      section\r\ndieresis      copyright      ordfeminine    guillemotleft\r\nlogicalnot    hyphen         registered     macron\r\ndegree        plusminus      twosuperior    threesuperior\r\nacute         mu             paragraph      periodcentered\r\ncedilla       onesuperior    ordmasculine   guillemotright\r\nonequarter    onehalf        threequarters  questiondown\r\n  \r\nAgrave        Aacute         Acircumflex    Atilde\r\nAdieresis     Aring          AE             Ccedilla\r\nEgrave        Eacute         Ecircumflex    Edieresis\r\nIgrave        Iacute         Icircumflex    Idieresis\r\nEth           Ntilde         Ograve         Oacute\r\nOcircumflex   Otilde         Odieresis      multiply\r\nOslash        Ugrave         Uacute         Ucircumflex\r\nUdieresis     Yacute         Thorn          germandbls\r\n  \r\nagrave        aacute         acircumflex    atilde\r\nadieresis     aring          ae             ccedilla\r\negrave        eacute         ecircumflex    edieresis\r\nigrave        iacute         icircumflex    idieresis\r\neth           ntilde         ograve         oacute\r\nocircumflex   otilde         odieresis      divide\r\noslash        ugrave         uacute         ucircumflex\r\nudieresis     yacute         thorn          ydieresis\\\r\n`.split(/\\s+/);\r\n\r\nclass AFMFont {\r\n  static open(filename) {\r\n    return new AFMFont(fs.readFileSync(filename, 'utf8'));\r\n  }\r\n\r\n  constructor(contents) {\r\n    this.contents = contents;\r\n    this.attributes = {};\r\n    this.glyphWidths = {};\r\n    this.boundingBoxes = {};\r\n    this.kernPairs = {};\r\n\r\n    this.parse();\r\n    // todo: remove charWidths since appears to not be used\r\n    this.charWidths = new Array(256);\r\n    for (let char = 0; char <= 255; char++) {\r\n      this.charWidths[char] = this.glyphWidths[characters[char]];\r\n    }\r\n\r\n    this.bbox = this.attributes['FontBBox'].split(/\\s+/).map(e => +e);\r\n    this.ascender = +(this.attributes['Ascender'] || 0);\r\n    this.descender = +(this.attributes['Descender'] || 0);\r\n    this.xHeight = +(this.attributes['XHeight'] || 0);\r\n    this.capHeight = +(this.attributes['CapHeight'] || 0);\r\n    this.lineGap =\r\n      this.bbox[3] - this.bbox[1] - (this.ascender - this.descender);\r\n  }\r\n\r\n  parse() {\r\n    let section = '';\r\n    for (let line of this.contents.split('\\n')) {\r\n      var match;\r\n      var a;\r\n      if ((match = line.match(/^Start(\\w+)/))) {\r\n        section = match[1];\r\n        continue;\r\n      } else if ((match = line.match(/^End(\\w+)/))) {\r\n        section = '';\r\n        continue;\r\n      }\r\n\r\n      switch (section) {\r\n        case 'FontMetrics':\r\n          match = line.match(/(^\\w+)\\s+(.*)/);\r\n          var key = match[1];\r\n          var value = match[2];\r\n\r\n          if ((a = this.attributes[key])) {\r\n            if (!Array.isArray(a)) {\r\n              a = this.attributes[key] = [a];\r\n            }\r\n            a.push(value);\r\n          } else {\r\n            this.attributes[key] = value;\r\n          }\r\n          break;\r\n\r\n        case 'CharMetrics':\r\n          if (!/^CH?\\s/.test(line)) {\r\n            continue;\r\n          }\r\n          var name = line.match(/\\bN\\s+(\\.?\\w+)\\s*;/)[1];\r\n          this.glyphWidths[name] = +line.match(/\\bWX\\s+(\\d+)\\s*;/)[1];\r\n          break;\r\n\r\n        case 'KernPairs':\r\n          match = line.match(/^KPX\\s+(\\.?\\w+)\\s+(\\.?\\w+)\\s+(-?\\d+)/);\r\n          if (match) {\r\n            this.kernPairs[match[1] + '\\0' + match[2]] = parseInt(match[3]);\r\n          }\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  encodeText(text) {\r\n    const res = [];\r\n    for (let i = 0, len = text.length; i < len; i++) {\r\n      let char = text.charCodeAt(i);\r\n      char = WIN_ANSI_MAP[char] || char;\r\n      res.push(char.toString(16));\r\n    }\r\n\r\n    return res;\r\n  }\r\n\r\n  glyphsForString(string) {\r\n    const glyphs = [];\r\n\r\n    for (let i = 0, len = string.length; i < len; i++) {\r\n      const charCode = string.charCodeAt(i);\r\n      glyphs.push(this.characterToGlyph(charCode));\r\n    }\r\n\r\n    return glyphs;\r\n  }\r\n\r\n  characterToGlyph(character) {\r\n    return characters[WIN_ANSI_MAP[character] || character] || '.notdef';\r\n  }\r\n\r\n  widthOfGlyph(glyph) {\r\n    return this.glyphWidths[glyph] || 0;\r\n  }\r\n\r\n  getKernPair(left, right) {\r\n    return this.kernPairs[left + '\\0' + right] || 0;\r\n  }\r\n\r\n  advancesForGlyphs(glyphs) {\r\n    const advances = [];\r\n\r\n    for (let index = 0; index < glyphs.length; index++) {\r\n      const left = glyphs[index];\r\n      const right = glyphs[index + 1];\r\n      advances.push(this.widthOfGlyph(left) + this.getKernPair(left, right));\r\n    }\r\n\r\n    return advances;\r\n  }\r\n}\r\n\r\nexport default AFMFont;\r\n", "class PDFFont {\r\n  constructor() {}\r\n\r\n  encode() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  widthOfString() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  ref() {\r\n    return this.dictionary != null\r\n      ? this.dictionary\r\n      : (this.dictionary = this.document.ref());\r\n  }\r\n\r\n  finalize() {\r\n    if (this.embedded || this.dictionary == null) {\r\n      return;\r\n    }\r\n\r\n    this.embed();\r\n    return (this.embedded = true);\r\n  }\r\n\r\n  embed() {\r\n    throw new Error('Must be implemented by subclasses');\r\n  }\r\n\r\n  lineHeight(size, includeGap) {\r\n    if (includeGap == null) {\r\n      includeGap = false;\r\n    }\r\n    const gap = includeGap ? this.lineGap : 0;\r\n    return ((this.ascender + gap - this.descender) / 1000) * size;\r\n  }\r\n}\r\n\r\nexport default PDFFont;\r\n", "import AFMFont from './afm';\r\nimport PDFFont from '../font';\r\nimport fs from 'fs';\r\n\r\n// This insanity is so bundlers can inline the font files\r\nconst STANDARD_FONTS = {\r\n  Courier() {\r\n    return fs.readFileSync(__dirname + '/data/Courier.afm', 'utf8');\r\n  },\r\n  'Courier-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-Bold.afm', 'utf8');\r\n  },\r\n  'Courier-Oblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-Oblique.afm', 'utf8');\r\n  },\r\n  'Courier-BoldOblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Courier-BoldOblique.afm', 'utf8');\r\n  },\r\n  Helvetica() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica.afm', 'utf8');\r\n  },\r\n  'Helvetica-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica-Bold.afm', 'utf8');\r\n  },\r\n  'Helvetica-Oblique'() {\r\n    return fs.readFileSync(__dirname + '/data/Helvetica-Oblique.afm', 'utf8');\r\n  },\r\n  'Helvetica-BoldOblique'() {\r\n    return fs.readFileSync(\r\n      __dirname + '/data/Helvetica-BoldOblique.afm',\r\n      'utf8'\r\n    );\r\n  },\r\n  'Times-Roman'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Roman.afm', 'utf8');\r\n  },\r\n  'Times-Bold'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Bold.afm', 'utf8');\r\n  },\r\n  'Times-Italic'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-Italic.afm', 'utf8');\r\n  },\r\n  'Times-BoldItalic'() {\r\n    return fs.readFileSync(__dirname + '/data/Times-BoldItalic.afm', 'utf8');\r\n  },\r\n  Symbol() {\r\n    return fs.readFileSync(__dirname + '/data/Symbol.afm', 'utf8');\r\n  },\r\n  ZapfDingbats() {\r\n    return fs.readFileSync(__dirname + '/data/ZapfDingbats.afm', 'utf8');\r\n  }\r\n};\r\n\r\nclass StandardFont extends PDFFont {\r\n  constructor(document, name, id) {\r\n    super();\r\n    this.document = document;\r\n    this.name = name;\r\n    this.id = id;\r\n    this.font = new AFMFont(STANDARD_FONTS[this.name]());\r\n    ({\r\n      ascender: this.ascender,\r\n      descender: this.descender,\r\n      bbox: this.bbox,\r\n      lineGap: this.lineGap,\r\n      xHeight: this.xHeight,\r\n      capHeight: this.capHeight\r\n    } = this.font);\r\n  }\r\n\r\n  embed() {\r\n    this.dictionary.data = {\r\n      Type: 'Font',\r\n      BaseFont: this.name,\r\n      Subtype: 'Type1',\r\n      Encoding: 'WinAnsiEncoding'\r\n    };\r\n\r\n    return this.dictionary.end();\r\n  }\r\n\r\n  encode(text) {\r\n    const encoded = this.font.encodeText(text);\r\n    const glyphs = this.font.glyphsForString(`${text}`);\r\n    const advances = this.font.advancesForGlyphs(glyphs);\r\n    const positions = [];\r\n    for (let i = 0; i < glyphs.length; i++) {\r\n      const glyph = glyphs[i];\r\n      positions.push({\r\n        xAdvance: advances[i],\r\n        yAdvance: 0,\r\n        xOffset: 0,\r\n        yOffset: 0,\r\n        advanceWidth: this.font.widthOfGlyph(glyph)\r\n      });\r\n    }\r\n\r\n    return [encoded, positions];\r\n  }\r\n\r\n  widthOfString(string, size) {\r\n    const glyphs = this.font.glyphsForString(`${string}`);\r\n    const advances = this.font.advancesForGlyphs(glyphs);\r\n\r\n    let width = 0;\r\n    for (let advance of advances) {\r\n      width += advance;\r\n    }\r\n\r\n    const scale = size / 1000;\r\n    return width * scale;\r\n  }\r\n\r\n  static isStandardFont(name) {\r\n    return name in STANDARD_FONTS;\r\n  }\r\n}\r\n\r\nexport default StandardFont;\r\n", "import PDFFont from '../font';\r\n\r\nconst toHex = function(num) {\r\n  return `0000${num.toString(16)}`.slice(-4);\r\n};\r\n\r\nclass EmbeddedFont extends PDFFont {\r\n  constructor(document, font, id) {\r\n    super();\r\n    this.document = document;\r\n    this.font = font;\r\n    this.id = id;\r\n    this.subset = this.font.createSubset();\r\n    this.unicode = [[0]];\r\n    this.widths = [this.font.getGlyph(0).advanceWidth];\r\n\r\n    this.name = this.font.postscriptName;\r\n    this.scale = 1000 / this.font.unitsPerEm;\r\n    this.ascender = this.font.ascent * this.scale;\r\n    this.descender = this.font.descent * this.scale;\r\n    this.xHeight = this.font.xHeight * this.scale;\r\n    this.capHeight = this.font.capHeight * this.scale;\r\n    this.lineGap = this.font.lineGap * this.scale;\r\n    this.bbox = this.font.bbox;\r\n\r\n    if (document.options.fontLayoutCache !== false) {\r\n      this.layoutCache = Object.create(null);\r\n    }\r\n  }\r\n\r\n  layoutRun(text, features) {\r\n    const run = this.font.layout(text, features);\r\n\r\n    // Normalize position values\r\n    for (let i = 0; i < run.positions.length; i++) {\r\n      const position = run.positions[i];\r\n      for (let key in position) {\r\n        position[key] *= this.scale;\r\n      }\r\n\r\n      position.advanceWidth = run.glyphs[i].advanceWidth * this.scale;\r\n    }\r\n\r\n    return run;\r\n  }\r\n\r\n  layoutCached(text) {\r\n    if (!this.layoutCache) {\r\n      return this.layoutRun(text);\r\n    }\r\n    let cached;\r\n    if ((cached = this.layoutCache[text])) {\r\n      return cached;\r\n    }\r\n\r\n    const run = this.layoutRun(text);\r\n    this.layoutCache[text] = run;\r\n    return run;\r\n  }\r\n\r\n  layout(text, features, onlyWidth) {\r\n    // Skip the cache if any user defined features are applied\r\n    if (features) {\r\n      return this.layoutRun(text, features);\r\n    }\r\n\r\n    let glyphs = onlyWidth ? null : [];\r\n    let positions = onlyWidth ? null : [];\r\n    let advanceWidth = 0;\r\n\r\n    // Split the string by words to increase cache efficiency.\r\n    // For this purpose, spaces and tabs are a good enough delimeter.\r\n    let last = 0;\r\n    let index = 0;\r\n    while (index <= text.length) {\r\n      var needle;\r\n      if (\r\n        (index === text.length && last < index) ||\r\n        ((needle = text.charAt(index)), [' ', '\\t'].includes(needle))\r\n      ) {\r\n        const run = this.layoutCached(text.slice(last, ++index));\r\n        if (!onlyWidth) {\r\n          glyphs = glyphs.concat(run.glyphs);\r\n          positions = positions.concat(run.positions);\r\n        }\r\n\r\n        advanceWidth += run.advanceWidth;\r\n        last = index;\r\n      } else {\r\n        index++;\r\n      }\r\n    }\r\n\r\n    return { glyphs, positions, advanceWidth };\r\n  }\r\n\r\n  encode(text, features) {\r\n    const { glyphs, positions } = this.layout(text, features);\r\n\r\n    const res = [];\r\n    for (let i = 0; i < glyphs.length; i++) {\r\n      const glyph = glyphs[i];\r\n      const gid = this.subset.includeGlyph(glyph.id);\r\n      res.push(`0000${gid.toString(16)}`.slice(-4));\r\n\r\n      if (this.widths[gid] == null) {\r\n        this.widths[gid] = glyph.advanceWidth * this.scale;\r\n      }\r\n      if (this.unicode[gid] == null) {\r\n        this.unicode[gid] = glyph.codePoints;\r\n      }\r\n    }\r\n\r\n    return [res, positions];\r\n  }\r\n\r\n  widthOfString(string, size, features) {\r\n    const width = this.layout(string, features, true).advanceWidth;\r\n    const scale = size / 1000;\r\n    return width * scale;\r\n  }\r\n\r\n  embed() {\r\n    const isCFF = this.subset.cff != null;\r\n    const fontFile = this.document.ref();\r\n\r\n    if (isCFF) {\r\n      fontFile.data.Subtype = 'CIDFontType0C';\r\n    }\r\n\r\n    this.subset\r\n      .encodeStream()\r\n      .on('data', data => fontFile.write(data))\r\n      .on('end', () => fontFile.end());\r\n\r\n    const familyClass =\r\n      ((this.font['OS/2'] != null\r\n        ? this.font['OS/2'].sFamilyClass\r\n        : undefined) || 0) >> 8;\r\n    let flags = 0;\r\n    if (this.font.post.isFixedPitch) {\r\n      flags |= 1 << 0;\r\n    }\r\n    if (1 <= familyClass && familyClass <= 7) {\r\n      flags |= 1 << 1;\r\n    }\r\n    flags |= 1 << 2; // assume the font uses non-latin characters\r\n    if (familyClass === 10) {\r\n      flags |= 1 << 3;\r\n    }\r\n    if (this.font.head.macStyle.italic) {\r\n      flags |= 1 << 6;\r\n    }\r\n\r\n    // generate a tag (6 uppercase letters. 17 is the char code offset from '0' to 'A'. 73 will map to 'Z')\r\n    const tag = [1, 2, 3, 4, 5, 6]\r\n      .map(i => String.fromCharCode((this.id.charCodeAt(i) || 73) + 17))\r\n      .join('');\r\n    const name = tag + '+' + this.font.postscriptName;\r\n\r\n    const { bbox } = this.font;\r\n    const descriptor = this.document.ref({\r\n      Type: 'FontDescriptor',\r\n      FontName: name,\r\n      Flags: flags,\r\n      FontBBox: [\r\n        bbox.minX * this.scale,\r\n        bbox.minY * this.scale,\r\n        bbox.maxX * this.scale,\r\n        bbox.maxY * this.scale\r\n      ],\r\n      ItalicAngle: this.font.italicAngle,\r\n      Ascent: this.ascender,\r\n      Descent: this.descender,\r\n      CapHeight: (this.font.capHeight || this.font.ascent) * this.scale,\r\n      XHeight: (this.font.xHeight || 0) * this.scale,\r\n      StemV: 0\r\n    }); // not sure how to calculate this\r\n\r\n    if (isCFF) {\r\n      descriptor.data.FontFile3 = fontFile;\r\n    } else {\r\n      descriptor.data.FontFile2 = fontFile;\r\n    }\r\n\r\n    descriptor.end();\r\n\r\n    const descendantFontData = {\r\n      Type: 'Font',\r\n      Subtype: 'CIDFontType0',\r\n      BaseFont: name,\r\n      CIDSystemInfo: {\r\n        Registry: new String('Adobe'),\r\n        Ordering: new String('Identity'),\r\n        Supplement: 0\r\n      },\r\n      FontDescriptor: descriptor,\r\n      W: [0, this.widths]\r\n    };\r\n\r\n    if (!isCFF) {\r\n      descendantFontData.Subtype = 'CIDFontType2';\r\n      descendantFontData.CIDToGIDMap = 'Identity';\r\n    }\r\n\r\n    const descendantFont = this.document.ref(descendantFontData);\r\n\r\n    descendantFont.end();\r\n\r\n    this.dictionary.data = {\r\n      Type: 'Font',\r\n      Subtype: 'Type0',\r\n      BaseFont: name,\r\n      Encoding: 'Identity-H',\r\n      DescendantFonts: [descendantFont],\r\n      ToUnicode: this.toUnicodeCmap()\r\n    };\r\n\r\n    return this.dictionary.end();\r\n  }\r\n\r\n  // Maps the glyph ids encoded in the PDF back to unicode strings\r\n  // Because of ligature substitutions and the like, there may be one or more\r\n  // unicode characters represented by each glyph.\r\n  toUnicodeCmap() {\r\n    const cmap = this.document.ref();\r\n\r\n    const entries = [];\r\n    for (let codePoints of this.unicode) {\r\n      const encoded = [];\r\n\r\n      // encode codePoints to utf16\r\n      for (let value of codePoints) {\r\n        if (value > 0xffff) {\r\n          value -= 0x10000;\r\n          encoded.push(toHex(((value >>> 10) & 0x3ff) | 0xd800));\r\n          value = 0xdc00 | (value & 0x3ff);\r\n        }\r\n\r\n        encoded.push(toHex(value));\r\n      }\r\n\r\n      entries.push(`<${encoded.join(' ')}>`);\r\n    }\r\n\r\n    cmap.end(`\\\r\n/CIDInit /ProcSet findresource begin\r\n12 dict begin\r\nbegincmap\r\n/CIDSystemInfo <<\r\n  /Registry (Adobe)\r\n  /Ordering (UCS)\r\n  /Supplement 0\r\n>> def\r\n/CMapName /Adobe-Identity-UCS def\r\n/CMapType 2 def\r\n1 begincodespacerange\r\n<0000><ffff>\r\nendcodespacerange\r\n1 beginbfrange\r\n<0000> <${toHex(entries.length - 1)}> [${entries.join(' ')}]\r\nendbfrange\r\nendcmap\r\nCMapName currentdict /CMap defineresource pop\r\nend\r\nend\\\r\n`);\r\n\r\n    return cmap;\r\n  }\r\n}\r\n\r\nexport default EmbeddedFont;\r\n", "import fs from 'fs';\r\nimport fontkit from 'fontkit';\r\nimport StandardFont from './font/standard';\r\nimport EmbeddedFont from './font/embedded';\r\n\r\nclass PDFFontFactory {\r\n  static open(document, src, family, id) {\r\n    let font;\r\n    if (typeof src === 'string') {\r\n      if (StandardFont.isStandardFont(src)) {\r\n        return new StandardFont(document, src, id);\r\n      }\r\n\r\n      src = fs.readFileSync(src);\r\n    }\r\n    if (Buffer.isBuffer(src)) {\r\n      font = fontkit.create(src, family);\r\n    } else if (src instanceof Uint8Array) {\r\n      font = fontkit.create(Buffer.from(src), family);\r\n    } else if (src instanceof ArrayBuffer) {\r\n      font = fontkit.create(Buffer.from(new Uint8Array(src)), family);\r\n    }\r\n\r\n    if (font == null) {\r\n      throw new Error('Not a supported font format or standard PDF font.');\r\n    }\r\n\r\n    return new EmbeddedFont(document, font, id);\r\n  }\r\n}\r\n\r\nexport default PDFFontFactory;\r\n", "import PDFFontFactory from '../font_factory';\r\n\r\nexport default {\r\n  initFonts(defaultFont = 'Helvetica') {\r\n    // Lookup table for embedded fonts\r\n    this._fontFamilies = {};\r\n    this._fontCount = 0;\r\n\r\n    // Font state\r\n    this._fontSize = 12;\r\n    this._font = null;\r\n\r\n    this._registeredFonts = {};\r\n\r\n    // Set the default font\r\n    if (defaultFont) {\r\n      this.font(defaultFont);\r\n    }\r\n  },\r\n\r\n  font(src, family, size) {\r\n    let cacheKey, font;\r\n    if (typeof family === 'number') {\r\n      size = family;\r\n      family = null;\r\n    }\r\n\r\n    // check registered fonts if src is a string\r\n    if (typeof src === 'string' && this._registeredFonts[src]) {\r\n      cacheKey = src;\r\n      ({ src, family } = this._registeredFonts[src]);\r\n    } else {\r\n      cacheKey = family || src;\r\n      if (typeof cacheKey !== 'string') {\r\n        cacheKey = null;\r\n      }\r\n    }\r\n\r\n    if (size != null) {\r\n      this.fontSize(size);\r\n    }\r\n\r\n    // fast path: check if the font is already in the PDF\r\n    if ((font = this._fontFamilies[cacheKey])) {\r\n      this._font = font;\r\n      return this;\r\n    }\r\n\r\n    // load the font\r\n    const id = `F${++this._fontCount}`;\r\n    this._font = PDFFontFactory.open(this, src, family, id);\r\n\r\n    // check for existing font familes with the same name already in the PDF\r\n    // useful if the font was passed as a buffer\r\n    if ((font = this._fontFamilies[this._font.name])) {\r\n      this._font = font;\r\n      return this;\r\n    }\r\n\r\n    // save the font for reuse later\r\n    if (cacheKey) {\r\n      this._fontFamilies[cacheKey] = this._font;\r\n    }\r\n\r\n    if (this._font.name) {\r\n      this._fontFamilies[this._font.name] = this._font;\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  fontSize(_fontSize) {\r\n    this._fontSize = _fontSize;\r\n    return this;\r\n  },\r\n\r\n  currentLineHeight(includeGap) {\r\n    if (includeGap == null) {\r\n      includeGap = false;\r\n    }\r\n    return this._font.lineHeight(this._fontSize, includeGap);\r\n  },\r\n\r\n  registerFont(name, src, family) {\r\n    this._registeredFonts[name] = {\r\n      src,\r\n      family\r\n    };\r\n\r\n    return this;\r\n  }\r\n};\r\n", "import { EventEmitter } from 'events';\r\nimport LineBreaker from 'linebreak';\r\n\r\nclass LineWrapper extends EventEmitter {\r\n  constructor(document, options) {\r\n    super();\r\n    this.document = document;\r\n    this.indent = options.indent || 0;\r\n    this.characterSpacing = options.characterSpacing || 0;\r\n    this.wordSpacing = options.wordSpacing === 0;\r\n    this.columns = options.columns || 1;\r\n    this.columnGap = options.columnGap != null ? options.columnGap : 18; // 1/4 inch\r\n    this.lineWidth =\r\n      (options.width - this.columnGap * (this.columns - 1)) / this.columns;\r\n    this.spaceLeft = this.lineWidth;\r\n    this.startX = this.document.x;\r\n    this.startY = this.document.y;\r\n    this.column = 1;\r\n    this.ellipsis = options.ellipsis;\r\n    this.continuedX = 0;\r\n    this.features = options.features;\r\n\r\n    // calculate the maximum Y position the text can appear at\r\n    if (options.height != null) {\r\n      this.height = options.height;\r\n      this.maxY = this.startY + options.height;\r\n    } else {\r\n      this.maxY = this.document.page.maxY();\r\n    }\r\n\r\n    // handle paragraph indents\r\n    this.on('firstLine', options => {\r\n      // if this is the first line of the text segment, and\r\n      // we're continuing where we left off, indent that much\r\n      // otherwise use the user specified indent option\r\n      const indent = this.continuedX || this.indent;\r\n      this.document.x += indent;\r\n      this.lineWidth -= indent;\r\n\r\n      return this.once('line', () => {\r\n        this.document.x -= indent;\r\n        this.lineWidth += indent;\r\n        if (options.continued && !this.continuedX) {\r\n          this.continuedX = this.indent;\r\n        }\r\n        if (!options.continued) {\r\n          return (this.continuedX = 0);\r\n        }\r\n      });\r\n    });\r\n\r\n    // handle left aligning last lines of paragraphs\r\n    this.on('lastLine', options => {\r\n      const { align } = options;\r\n      if (align === 'justify') {\r\n        options.align = 'left';\r\n      }\r\n      this.lastLine = true;\r\n\r\n      return this.once('line', () => {\r\n        this.document.y += options.paragraphGap || 0;\r\n        options.align = align;\r\n        return (this.lastLine = false);\r\n      });\r\n    });\r\n  }\r\n\r\n  wordWidth(word) {\r\n    return (\r\n      this.document.widthOfString(word, this) +\r\n      this.characterSpacing +\r\n      this.wordSpacing\r\n    );\r\n  }\r\n\r\n  eachWord(text, fn) {\r\n    // setup a unicode line breaker\r\n    let bk;\r\n    const breaker = new LineBreaker(text);\r\n    let last = null;\r\n    const wordWidths = Object.create(null);\r\n\r\n    while ((bk = breaker.nextBreak())) {\r\n      var shouldContinue;\r\n      let word = text.slice(\r\n        (last != null ? last.position : undefined) || 0,\r\n        bk.position\r\n      );\r\n      let w =\r\n        wordWidths[word] != null\r\n          ? wordWidths[word]\r\n          : (wordWidths[word] = this.wordWidth(word));\r\n\r\n      // if the word is longer than the whole line, chop it up\r\n      // TODO: break by grapheme clusters, not JS string characters\r\n      if (w > this.lineWidth + this.continuedX) {\r\n        // make some fake break objects\r\n        let lbk = last;\r\n        const fbk = {};\r\n\r\n        while (word.length) {\r\n          // fit as much of the word as possible into the space we have\r\n          var l, mightGrow;\r\n          if (w > this.spaceLeft) {\r\n            // start our check at the end of our available space - this method is faster than a loop of each character and it resolves\r\n            // an issue with long loops when processing massive words, such as a huge number of spaces\r\n            l = Math.ceil(this.spaceLeft / (w / word.length));\r\n            w = this.wordWidth(word.slice(0, l));\r\n            mightGrow = w <= this.spaceLeft && l < word.length;\r\n          } else {\r\n            l = word.length;\r\n          }\r\n          let mustShrink = w > this.spaceLeft && l > 0;\r\n          // shrink or grow word as necessary after our near-guess above\r\n          while (mustShrink || mightGrow) {\r\n            if (mustShrink) {\r\n              w = this.wordWidth(word.slice(0, --l));\r\n              mustShrink = w > this.spaceLeft && l > 0;\r\n            } else {\r\n              w = this.wordWidth(word.slice(0, ++l));\r\n              mustShrink = w > this.spaceLeft && l > 0;\r\n              mightGrow = w <= this.spaceLeft && l < word.length;\r\n            }\r\n          }\r\n\r\n          // check for the edge case where a single character cannot fit into a line.\r\n          if (l === 0 && this.spaceLeft === this.lineWidth) {\r\n            l = 1;\r\n          }\r\n\r\n          // send a required break unless this is the last piece and a linebreak is not specified\r\n          fbk.required = bk.required || l < word.length;\r\n          shouldContinue = fn(word.slice(0, l), w, fbk, lbk);\r\n          lbk = { required: false };\r\n\r\n          // get the remaining piece of the word\r\n          word = word.slice(l);\r\n          w = this.wordWidth(word);\r\n\r\n          if (shouldContinue === false) {\r\n            break;\r\n          }\r\n        }\r\n      } else {\r\n        // otherwise just emit the break as it was given to us\r\n        shouldContinue = fn(word, w, bk, last);\r\n      }\r\n\r\n      if (shouldContinue === false) {\r\n        break;\r\n      }\r\n      last = bk;\r\n    }\r\n  }\r\n\r\n  wrap(text, options) {\r\n    // override options from previous continued fragments\r\n    if (options.indent != null) {\r\n      this.indent = options.indent;\r\n    }\r\n    if (options.characterSpacing != null) {\r\n      this.characterSpacing = options.characterSpacing;\r\n    }\r\n    if (options.wordSpacing != null) {\r\n      this.wordSpacing = options.wordSpacing;\r\n    }\r\n    if (options.ellipsis != null) {\r\n      this.ellipsis = options.ellipsis;\r\n    }\r\n\r\n    // make sure we're actually on the page\r\n    // and that the first line of is never by\r\n    // itself at the bottom of a page (orphans)\r\n    const nextY = this.document.y + this.document.currentLineHeight(true);\r\n    if (this.document.y > this.maxY || nextY > this.maxY) {\r\n      this.nextSection();\r\n    }\r\n\r\n    let buffer = '';\r\n    let textWidth = 0;\r\n    let wc = 0;\r\n    let lc = 0;\r\n\r\n    let { y } = this.document; // used to reset Y pos if options.continued (below)\r\n    const emitLine = () => {\r\n      options.textWidth = textWidth + this.wordSpacing * (wc - 1);\r\n      options.wordCount = wc;\r\n      options.lineWidth = this.lineWidth;\r\n      ({ y } = this.document);\r\n      this.emit('line', buffer, options, this);\r\n      return lc++;\r\n    };\r\n\r\n    this.emit('sectionStart', options, this);\r\n\r\n    this.eachWord(text, (word, w, bk, last) => {\r\n      if (last == null || last.required) {\r\n        this.emit('firstLine', options, this);\r\n        this.spaceLeft = this.lineWidth;\r\n      }\r\n\r\n      if (w <= this.spaceLeft) {\r\n        buffer += word;\r\n        textWidth += w;\r\n        wc++;\r\n      }\r\n\r\n      if (bk.required || w > this.spaceLeft) {\r\n        // if the user specified a max height and an ellipsis, and is about to pass the\r\n        // max height and max columns after the next line, append the ellipsis\r\n        const lh = this.document.currentLineHeight(true);\r\n        if (\r\n          this.height != null &&\r\n          this.ellipsis &&\r\n          this.document.y + lh * 2 > this.maxY &&\r\n          this.column >= this.columns\r\n        ) {\r\n          if (this.ellipsis === true) {\r\n            this.ellipsis = '…';\r\n          } // map default ellipsis character\r\n          buffer = buffer.replace(/\\s+$/, '');\r\n          textWidth = this.wordWidth(buffer + this.ellipsis);\r\n\r\n          // remove characters from the buffer until the ellipsis fits\r\n          // to avoid infinite loop need to stop while-loop if buffer is empty string\r\n          while (buffer && textWidth > this.lineWidth) {\r\n            buffer = buffer.slice(0, -1).replace(/\\s+$/, '');\r\n            textWidth = this.wordWidth(buffer + this.ellipsis);\r\n          }\r\n          // need to add ellipsis only if there is enough space for it\r\n          if (textWidth <= this.lineWidth) {\r\n            buffer = buffer + this.ellipsis;\r\n          }\r\n\r\n          textWidth = this.wordWidth(buffer);\r\n        }\r\n\r\n        if (bk.required) {\r\n          if (w > this.spaceLeft) {\r\n            emitLine();\r\n            buffer = word;\r\n            textWidth = w;\r\n            wc = 1;\r\n          }\r\n\r\n          this.emit('lastLine', options, this);\r\n        }\r\n\r\n        emitLine();\r\n\r\n        // if we've reached the edge of the page,\r\n        // continue on a new page or column\r\n        if (this.document.y + lh > this.maxY) {\r\n          const shouldContinue = this.nextSection();\r\n\r\n          // stop if we reached the maximum height\r\n          if (!shouldContinue) {\r\n            wc = 0;\r\n            buffer = '';\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // reset the space left and buffer\r\n        if (bk.required) {\r\n          this.spaceLeft = this.lineWidth;\r\n          buffer = '';\r\n          textWidth = 0;\r\n          return (wc = 0);\r\n        } else {\r\n          // reset the space left and buffer\r\n          this.spaceLeft = this.lineWidth - w;\r\n          buffer = word;\r\n          textWidth = w;\r\n          return (wc = 1);\r\n        }\r\n      } else {\r\n        return (this.spaceLeft -= w);\r\n      }\r\n    });\r\n\r\n    if (wc > 0) {\r\n      this.emit('lastLine', options, this);\r\n      emitLine();\r\n    }\r\n\r\n    this.emit('sectionEnd', options, this);\r\n\r\n    // if the wrap is set to be continued, save the X position\r\n    // to start the first line of the next segment at, and reset\r\n    // the y position\r\n    if (options.continued === true) {\r\n      if (lc > 1) {\r\n        this.continuedX = 0;\r\n      }\r\n      this.continuedX += options.textWidth || 0;\r\n      return (this.document.y = y);\r\n    } else {\r\n      return (this.document.x = this.startX);\r\n    }\r\n  }\r\n\r\n  nextSection(options) {\r\n    this.emit('sectionEnd', options, this);\r\n\r\n    if (++this.column > this.columns) {\r\n      // if a max height was specified by the user, we're done.\r\n      // otherwise, the default is to make a new page at the bottom.\r\n      if (this.height != null) {\r\n        return false;\r\n      }\r\n\r\n      this.document.continueOnNewPage();\r\n      this.column = 1;\r\n      this.startY = this.document.page.margins.top;\r\n      this.maxY = this.document.page.maxY();\r\n      this.document.x = this.startX;\r\n      if (this.document._fillColor) {\r\n        this.document.fillColor(...this.document._fillColor);\r\n      }\r\n      this.emit('pageBreak', options, this);\r\n    } else {\r\n      this.document.x += this.lineWidth + this.columnGap;\r\n      this.document.y = this.startY;\r\n      this.emit('columnBreak', options, this);\r\n    }\r\n\r\n    this.emit('sectionStart', options, this);\r\n    return true;\r\n  }\r\n}\r\n\r\nexport default LineWrapper;\r\n", "import LineWrapper from '../line_wrapper';\r\nimport PDFObject from '../object';\r\n\r\nconst { number } = PDFObject;\r\n\r\nexport default {\r\n  initText() {\r\n    this._line = this._line.bind(this);\r\n    // Current coordinates\r\n    this.x = 0;\r\n    this.y = 0;\r\n    return (this._lineGap = 0);\r\n  },\r\n\r\n  lineGap(_lineGap) {\r\n    this._lineGap = _lineGap;\r\n    return this;\r\n  },\r\n\r\n  moveDown(lines) {\r\n    if (lines == null) {\r\n      lines = 1;\r\n    }\r\n    this.y += this.currentLineHeight(true) * lines + this._lineGap;\r\n    return this;\r\n  },\r\n\r\n  moveUp(lines) {\r\n    if (lines == null) {\r\n      lines = 1;\r\n    }\r\n    this.y -= this.currentLineHeight(true) * lines + this._lineGap;\r\n    return this;\r\n  },\r\n\r\n  _text(text, x, y, options, lineCallback) {\r\n    options = this._initOptions(x, y, options);\r\n\r\n    // Convert text to a string\r\n    text = text == null ? '' : `${text}`;\r\n\r\n    // if the wordSpacing option is specified, remove multiple consecutive spaces\r\n    if (options.wordSpacing) {\r\n      text = text.replace(/\\s{2,}/g, ' ');\r\n    }\r\n\r\n    const addStructure = () => {\r\n      if (options.structParent) {\r\n        options.structParent.add(this.struct(options.structType || 'P',\r\n          [ this.markStructureContent(options.structType || 'P') ]));\r\n      }\r\n    };\r\n\r\n    // word wrapping\r\n    if (options.width) {\r\n      let wrapper = this._wrapper;\r\n      if (!wrapper) {\r\n        wrapper = new LineWrapper(this, options);\r\n        wrapper.on('line', lineCallback);\r\n        wrapper.on('firstLine', addStructure);\r\n      }\r\n\r\n      this._wrapper = options.continued ? wrapper : null;\r\n      this._textOptions = options.continued ? options : null;\r\n      wrapper.wrap(text, options);\r\n\r\n      // render paragraphs as single lines\r\n    } else {\r\n      for (let line of text.split('\\n')) {\r\n        addStructure();\r\n        lineCallback(line, options);\r\n      }\r\n    }\r\n\r\n    return this;\r\n  },\r\n\r\n  text(text, x, y, options) {\r\n    return this._text(text, x, y, options, this._line);\r\n  },\r\n\r\n  widthOfString(string, options = {}) {\r\n    return (\r\n      this._font.widthOfString(string, this._fontSize, options.features) +\r\n      (options.characterSpacing || 0) * (string.length - 1)\r\n    );\r\n  },\r\n\r\n  heightOfString(text, options) {\r\n    const { x, y } = this;\r\n\r\n    options = this._initOptions(options);\r\n    options.height = Infinity; // don't break pages\r\n\r\n    const lineGap = options.lineGap || this._lineGap || 0;\r\n    this._text(text, this.x, this.y, options, () => {\r\n      return (this.y += this.currentLineHeight(true) + lineGap);\r\n    });\r\n\r\n    const height = this.y - y;\r\n    this.x = x;\r\n    this.y = y;\r\n\r\n    return height;\r\n  },\r\n\r\n  list(list, x, y, options, wrapper) {\r\n    options = this._initOptions(x, y, options);\r\n\r\n    const listType = options.listType || 'bullet';\r\n    const unit = Math.round((this._font.ascender / 1000) * this._fontSize);\r\n    const midLine = unit / 2;\r\n    const r = options.bulletRadius || unit / 3;\r\n    const indent =\r\n      options.textIndent || (listType === 'bullet' ? r * 5 : unit * 2);\r\n    const itemIndent =\r\n      options.bulletIndent || (listType === 'bullet' ? r * 8 : unit * 2);\r\n\r\n    let level = 1;\r\n    const items = [];\r\n    const levels = [];\r\n    const numbers = [];\r\n\r\n    var flatten = function(list) {\r\n      let n = 1;\r\n      for (let i = 0; i < list.length; i++) {\r\n        const item = list[i];\r\n        if (Array.isArray(item)) {\r\n          level++;\r\n          flatten(item);\r\n          level--;\r\n        } else {\r\n          items.push(item);\r\n          levels.push(level);\r\n          if (listType !== 'bullet') {\r\n            numbers.push(n++);\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    flatten(list);\r\n\r\n    const label = function(n) {\r\n      switch (listType) {\r\n        case 'numbered':\r\n          return `${n}.`;\r\n        case 'lettered':\r\n          var letter = String.fromCharCode(((n - 1) % 26) + 65);\r\n          var times = Math.floor((n - 1) / 26 + 1);\r\n          var text = Array(times + 1).join(letter);\r\n          return `${text}.`;\r\n      }\r\n    };\r\n\r\n    wrapper = new LineWrapper(this, options);\r\n    wrapper.on('line', this._line);\r\n\r\n    level = 1;\r\n    let i = 0;\r\n    wrapper.on('firstLine', () => {\r\n      let item, itemType, labelType, bodyType;\r\n      if (options.structParent) {\r\n        if (options.structTypes) {\r\n          [ itemType, labelType, bodyType ] = options.structTypes;\r\n        } else {\r\n          [ itemType, labelType, bodyType ] = [ 'LI', 'Lbl', 'LBody' ];\r\n        }\r\n      }\r\n\r\n      if (itemType) {\r\n        item = this.struct(itemType);\r\n        options.structParent.add(item);\r\n      } else if (options.structParent) {\r\n        item = options.structParent;\r\n      }\r\n\r\n      let l;\r\n      if ((l = levels[i++]) !== level) {\r\n        const diff = itemIndent * (l - level);\r\n        this.x += diff;\r\n        wrapper.lineWidth -= diff;\r\n        level = l;\r\n      }\r\n\r\n      if (item && (labelType || bodyType)) {\r\n        item.add(this.struct(labelType || bodyType,\r\n          [ this.markStructureContent(labelType || bodyType) ]));\r\n      }\r\n      switch (listType) {\r\n        case 'bullet':\r\n          this.circle(this.x - indent + r, this.y + midLine, r);\r\n          this.fill();\r\n          break;\r\n        case 'numbered':\r\n        case 'lettered':\r\n          var text = label(numbers[i - 1]);\r\n          this._fragment(text, this.x - indent, this.y, options);\r\n          break;\r\n      }\r\n\r\n      if (item && labelType && bodyType) {\r\n        item.add(this.struct(bodyType, [ this.markStructureContent(bodyType) ]));\r\n      }\r\n      if (item && item !== options.structParent) {\r\n        item.end();\r\n      }\r\n    });\r\n\r\n    wrapper.on('sectionStart', () => {\r\n      const pos = indent + itemIndent * (level - 1);\r\n      this.x += pos;\r\n      return (wrapper.lineWidth -= pos);\r\n    });\r\n\r\n    wrapper.on('sectionEnd', () => {\r\n      const pos = indent + itemIndent * (level - 1);\r\n      this.x -= pos;\r\n      return (wrapper.lineWidth += pos);\r\n    });\r\n\r\n    wrapper.wrap(items.join('\\n'), options);\r\n\r\n    return this;\r\n  },\r\n\r\n  _initOptions(x = {}, y, options = {}) {\r\n    if (typeof x === 'object') {\r\n      options = x;\r\n      x = null;\r\n    }\r\n\r\n    // clone options object\r\n    const result = Object.assign({}, options);\r\n\r\n    // extend options with previous values for continued text\r\n    if (this._textOptions) {\r\n      for (let key in this._textOptions) {\r\n        const val = this._textOptions[key];\r\n        if (key !== 'continued') {\r\n          if (result[key] === undefined) {\r\n            result[key] = val;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // Update the current position\r\n    if (x != null) {\r\n      this.x = x;\r\n    }\r\n    if (y != null) {\r\n      this.y = y;\r\n    }\r\n\r\n    // wrap to margins if no x or y position passed\r\n    if (result.lineBreak !== false) {\r\n      if (result.width == null) {\r\n        result.width = this.page.width - this.x - this.page.margins.right;\r\n      }\r\n      result.width = Math.max(result.width, 0);\r\n    }\r\n\r\n    if (!result.columns) {\r\n      result.columns = 0;\r\n    }\r\n    if (result.columnGap == null) {\r\n      result.columnGap = 18;\r\n    } // 1/4 inch\r\n\r\n    return result;\r\n  },\r\n\r\n  _line(text, options = {}, wrapper) {\r\n    this._fragment(text, this.x, this.y, options);\r\n    const lineGap = options.lineGap || this._lineGap || 0;\r\n\r\n    if (!wrapper) {\r\n      return (this.x += this.widthOfString(text));\r\n    } else {\r\n      return (this.y += this.currentLineHeight(true) + lineGap);\r\n    }\r\n  },\r\n\r\n  _fragment(text, x, y, options) {\r\n    let dy, encoded, i, positions, textWidth, words;\r\n    text = `${text}`.replace(/\\n/g, '');\r\n    if (text.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // handle options\r\n    const align = options.align || 'left';\r\n    let wordSpacing = options.wordSpacing || 0;\r\n    const characterSpacing = options.characterSpacing || 0;\r\n\r\n    // text alignments\r\n    if (options.width) {\r\n      switch (align) {\r\n        case 'right':\r\n          textWidth = this.widthOfString(text.replace(/\\s+$/, ''), options);\r\n          x += options.lineWidth - textWidth;\r\n          break;\r\n\r\n        case 'center':\r\n          x += options.lineWidth / 2 - options.textWidth / 2;\r\n          break;\r\n\r\n        case 'justify':\r\n          // calculate the word spacing value\r\n          words = text.trim().split(/\\s+/);\r\n          textWidth = this.widthOfString(text.replace(/\\s+/g, ''), options);\r\n          var spaceWidth = this.widthOfString(' ') + characterSpacing;\r\n          wordSpacing = Math.max(\r\n            0,\r\n            (options.lineWidth - textWidth) / Math.max(1, words.length - 1) -\r\n              spaceWidth\r\n          );\r\n          break;\r\n      }\r\n    }\r\n\r\n    // text baseline alignments based on http://wiki.apache.org/xmlgraphics-fop/LineLayout/AlignmentHandling\r\n    if (typeof options.baseline === 'number') {\r\n      dy = -options.baseline;\r\n    } else {\r\n      switch (options.baseline) {\r\n        case 'svg-middle':\r\n          dy = 0.5 * this._font.xHeight;\r\n          break;\r\n        case 'middle':\r\n        case 'svg-central':\r\n          dy = 0.5 * (this._font.descender + this._font.ascender);\r\n          break;\r\n        case 'bottom':\r\n        case 'ideographic':\r\n          dy = this._font.descender;\r\n          break;\r\n        case 'alphabetic':\r\n          dy = 0;\r\n          break;\r\n        case 'mathematical':\r\n          dy = 0.5 * this._font.ascender;\r\n          break;\r\n        case 'hanging':\r\n          dy = 0.8 * this._font.ascender;\r\n          break;\r\n        case 'top':\r\n          dy = this._font.ascender;\r\n          break;\r\n        default:\r\n          dy = this._font.ascender;\r\n      }\r\n      dy = (dy / 1000) * this._fontSize;\r\n    }\r\n\r\n    // calculate the actual rendered width of the string after word and character spacing\r\n    const renderedWidth =\r\n      options.textWidth +\r\n      wordSpacing * (options.wordCount - 1) +\r\n      characterSpacing * (text.length - 1);\r\n\r\n    // create link annotations if the link option is given\r\n    if (options.link != null) {\r\n      this.link(x, y, renderedWidth, this.currentLineHeight(), options.link);\r\n    }\r\n    if (options.goTo != null) {\r\n      this.goTo(x, y, renderedWidth, this.currentLineHeight(), options.goTo);\r\n    }\r\n    if (options.destination != null) {\r\n      this.addNamedDestination(options.destination, 'XYZ', x, y, null);\r\n    }\r\n\r\n    // create underline\r\n    if (options.underline) {\r\n      this.save();\r\n      if (!options.stroke) {\r\n        this.strokeColor(...(this._fillColor || []));\r\n      }\r\n\r\n      const lineWidth =\r\n        this._fontSize < 10 ? 0.5 : Math.floor(this._fontSize / 10);\r\n      this.lineWidth(lineWidth);\r\n\r\n      let lineY = (y + this.currentLineHeight())  - lineWidth\r\n      this.moveTo(x, lineY);\r\n      this.lineTo(x + renderedWidth, lineY);\r\n      this.stroke();\r\n      this.restore();\r\n    }\r\n    \r\n    // create strikethrough line\r\n    if (options.strike) {\r\n      this.save();\r\n      if (!options.stroke) {\r\n        this.strokeColor(...(this._fillColor || []));\r\n      }\r\n\r\n      const lineWidth =\r\n        this._fontSize < 10 ? 0.5 : Math.floor(this._fontSize / 10);\r\n      this.lineWidth(lineWidth);\r\n\r\n      let lineY = y + this.currentLineHeight() / 2;\r\n      this.moveTo(x, lineY);\r\n      this.lineTo(x + renderedWidth, lineY);\r\n      this.stroke();\r\n      this.restore();\r\n    }\r\n\r\n    this.save();\r\n\r\n    // oblique (angle in degrees or boolean)\r\n    if (options.oblique) {\r\n      let skew;\r\n      if (typeof options.oblique === 'number') {\r\n        skew = -Math.tan((options.oblique * Math.PI) / 180);\r\n      } else {\r\n        skew = -0.25;\r\n      }\r\n      this.transform(1, 0, 0, 1, x, y);\r\n      this.transform(1, 0, skew, 1, -skew * dy, 0);\r\n      this.transform(1, 0, 0, 1, -x, -y);\r\n    }\r\n\r\n    // flip coordinate system\r\n    this.transform(1, 0, 0, -1, 0, this.page.height);\r\n    y = this.page.height - y - dy;\r\n\r\n    // add current font to page if necessary\r\n    if (this.page.fonts[this._font.id] == null) {\r\n      this.page.fonts[this._font.id] = this._font.ref();\r\n    }\r\n\r\n    // begin the text object\r\n    this.addContent('BT');\r\n\r\n    // text position\r\n    this.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);\r\n\r\n    // font and font size\r\n    this.addContent(`/${this._font.id} ${number(this._fontSize)} Tf`);\r\n\r\n    // rendering mode\r\n    const mode = options.fill && options.stroke ? 2 : options.stroke ? 1 : 0;\r\n    if (mode) {\r\n      this.addContent(`${mode} Tr`);\r\n    }\r\n\r\n    // Character spacing\r\n    if (characterSpacing) {\r\n      this.addContent(`${number(characterSpacing)} Tc`);\r\n    }\r\n\r\n    // Add the actual text\r\n    // If we have a word spacing value, we need to encode each word separately\r\n    // since the normal Tw operator only works on character code 32, which isn't\r\n    // used for embedded fonts.\r\n    if (wordSpacing) {\r\n      words = text.trim().split(/\\s+/);\r\n      wordSpacing += this.widthOfString(' ') + characterSpacing;\r\n      wordSpacing *= 1000 / this._fontSize;\r\n\r\n      encoded = [];\r\n      positions = [];\r\n      for (let word of words) {\r\n        const [encodedWord, positionsWord] = this._font.encode(\r\n          word,\r\n          options.features\r\n        );\r\n        encoded = encoded.concat(encodedWord);\r\n        positions = positions.concat(positionsWord);\r\n\r\n        // add the word spacing to the end of the word\r\n        // clone object because of cache\r\n        const space = {};\r\n        const object = positions[positions.length - 1];\r\n        for (let key in object) {\r\n          const val = object[key];\r\n          space[key] = val;\r\n        }\r\n        space.xAdvance += wordSpacing;\r\n        positions[positions.length - 1] = space;\r\n      }\r\n    } else {\r\n      [encoded, positions] = this._font.encode(text, options.features);\r\n    }\r\n\r\n    const scale = this._fontSize / 1000;\r\n    const commands = [];\r\n    let last = 0;\r\n    let hadOffset = false;\r\n\r\n    // Adds a segment of text to the TJ command buffer\r\n    const addSegment = cur => {\r\n      if (last < cur) {\r\n        const hex = encoded.slice(last, cur).join('');\r\n        const advance =\r\n          positions[cur - 1].xAdvance - positions[cur - 1].advanceWidth;\r\n        commands.push(`<${hex}> ${number(-advance)}`);\r\n      }\r\n\r\n      return (last = cur);\r\n    };\r\n\r\n    // Flushes the current TJ commands to the output stream\r\n    const flush = i => {\r\n      addSegment(i);\r\n\r\n      if (commands.length > 0) {\r\n        this.addContent(`[${commands.join(' ')}] TJ`);\r\n        return (commands.length = 0);\r\n      }\r\n    };\r\n\r\n    for (i = 0; i < positions.length; i++) {\r\n      // If we have an x or y offset, we have to break out of the current TJ command\r\n      // so we can move the text position.\r\n      const pos = positions[i];\r\n      if (pos.xOffset || pos.yOffset) {\r\n        // Flush the current buffer\r\n        flush(i);\r\n\r\n        // Move the text position and flush just the current character\r\n        this.addContent(\r\n          `1 0 0 1 ${number(x + pos.xOffset * scale)} ${number(\r\n            y + pos.yOffset * scale\r\n          )} Tm`\r\n        );\r\n        flush(i + 1);\r\n\r\n        hadOffset = true;\r\n      } else {\r\n        // If the last character had an offset, reset the text position\r\n        if (hadOffset) {\r\n          this.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);\r\n          hadOffset = false;\r\n        }\r\n\r\n        // Group segments that don't have any advance adjustments\r\n        if (pos.xAdvance - pos.advanceWidth !== 0) {\r\n          addSegment(i + 1);\r\n        }\r\n      }\r\n\r\n      x += pos.xAdvance * scale;\r\n    }\r\n\r\n    // Flush any remaining commands\r\n    flush(i);\r\n\r\n    // end the text object\r\n    this.addContent('ET');\r\n\r\n    // restore flipped coordinate system\r\n    return this.restore();\r\n  }\r\n};\r\n", "const MARKERS = [\r\n  0xffc0,\r\n  0xffc1,\r\n  0xffc2,\r\n  0xffc3,\r\n  0xffc5,\r\n  0xffc6,\r\n  0xffc7,\r\n  0xffc8,\r\n  0xffc9,\r\n  0xffca,\r\n  0xffcb,\r\n  0xffcc,\r\n  0xffcd,\r\n  0xffce,\r\n  0xffcf\r\n];\r\n\r\nconst COLOR_SPACE_MAP = {\r\n  1: 'DeviceGray',\r\n  3: 'DeviceRGB',\r\n  4: 'DeviceCMYK'\r\n};\r\n\r\nclass JPEG {\r\n  constructor(data, label) {\r\n    let marker;\r\n    this.data = data;\r\n    this.label = label;\r\n    if (this.data.readUInt16BE(0) !== 0xffd8) {\r\n      throw 'SOI not found in JPEG';\r\n    }\r\n\r\n    let pos = 2;\r\n    while (pos < this.data.length) {\r\n      marker = this.data.readUInt16BE(pos);\r\n      pos += 2;\r\n      if (MARKERS.includes(marker)) {\r\n        break;\r\n      }\r\n      pos += this.data.readUInt16BE(pos);\r\n    }\r\n\r\n    if (!MARKERS.includes(marker)) {\r\n      throw 'Invalid JPEG.';\r\n    }\r\n    pos += 2;\r\n\r\n    this.bits = this.data[pos++];\r\n    this.height = this.data.readUInt16BE(pos);\r\n    pos += 2;\r\n\r\n    this.width = this.data.readUInt16BE(pos);\r\n    pos += 2;\r\n\r\n    const channels = this.data[pos++];\r\n    this.colorSpace = COLOR_SPACE_MAP[channels];\r\n\r\n    this.obj = null;\r\n  }\r\n\r\n  embed(document) {\r\n    if (this.obj) {\r\n      return;\r\n    }\r\n\r\n    this.obj = document.ref({\r\n      Type: 'XObject',\r\n      Subtype: 'Image',\r\n      BitsPerComponent: this.bits,\r\n      Width: this.width,\r\n      Height: this.height,\r\n      ColorSpace: this.colorSpace,\r\n      Filter: 'DCTDecode'\r\n    });\r\n\r\n    // add extra decode params for CMYK images. By swapping the\r\n    // min and max values from the default, we invert the colors. See\r\n    // section 4.8.4 of the spec.\r\n    if (this.colorSpace === 'DeviceCMYK') {\r\n      this.obj.data['Decode'] = [1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0];\r\n    }\r\n\r\n    this.obj.end(this.data);\r\n\r\n    // free memory\r\n    return (this.data = null);\r\n  }\r\n}\r\n\r\nexport default JPEG;\r\n", "import zlib from 'zlib';\r\nimport PNG from 'png-js';\r\n\r\nclass PNGImage {\r\n  constructor(data, label) {\r\n    this.label = label;\r\n    this.image = new PNG(data);\r\n    this.width = this.image.width;\r\n    this.height = this.image.height;\r\n    this.imgData = this.image.imgData;\r\n    this.obj = null;\r\n  }\r\n\r\n  embed(document) {\r\n    let dataDecoded = false;\r\n\r\n    this.document = document;\r\n    if (this.obj) {\r\n      return;\r\n    }\r\n\r\n    const hasAlphaChannel = this.image.hasAlphaChannel;\r\n    const isInterlaced = this.image.interlaceMethod === 1;\r\n\r\n    this.obj = this.document.ref({\r\n      Type: 'XObject',\r\n      Subtype: 'Image',\r\n      BitsPerComponent: hasAlphaChannel ? 8 : this.image.bits,\r\n      Width: this.width,\r\n      Height: this.height,\r\n      Filter: 'FlateDecode'\r\n    });\r\n\r\n    if (!hasAlphaChannel) {\r\n      const params = this.document.ref({\r\n        Predictor: isInterlaced ? 1 : 15,\r\n        Colors: this.image.colors,\r\n        BitsPerComponent: this.image.bits,\r\n        Columns: this.width\r\n      });\r\n\r\n      this.obj.data['DecodeParms'] = params;\r\n      params.end();\r\n    }\r\n\r\n    if (this.image.palette.length === 0) {\r\n      this.obj.data['ColorSpace'] = this.image.colorSpace;\r\n    } else {\r\n      // embed the color palette in the PDF as an object stream\r\n      const palette = this.document.ref();\r\n      palette.end(Buffer.from(this.image.palette));\r\n\r\n      // build the color space array for the image\r\n      this.obj.data['ColorSpace'] = [\r\n        'Indexed',\r\n        'DeviceRGB',\r\n        this.image.palette.length / 3 - 1,\r\n        palette\r\n      ];\r\n    }\r\n\r\n    // For PNG color types 0, 2 and 3, the transparency data is stored in\r\n    // a dedicated PNG chunk.\r\n    if (this.image.transparency.grayscale != null) {\r\n      // Use Color Key Masking (spec section 4.8.5)\r\n      // An array with N elements, where N is two times the number of color components.\r\n      const val = this.image.transparency.grayscale;\r\n      this.obj.data['Mask'] = [val, val];\r\n    } else if (this.image.transparency.rgb) {\r\n      // Use Color Key Masking (spec section 4.8.5)\r\n      // An array with N elements, where N is two times the number of color components.\r\n      const { rgb } = this.image.transparency;\r\n      const mask = [];\r\n      for (let x of rgb) {\r\n        mask.push(x, x);\r\n      }\r\n\r\n      this.obj.data['Mask'] = mask;\r\n    } else if (this.image.transparency.indexed) {\r\n      // Create a transparency SMask for the image based on the data\r\n      // in the PLTE and tRNS sections. See below for details on SMasks.\r\n      dataDecoded = true;\r\n      return this.loadIndexedAlphaChannel();\r\n    } else if (hasAlphaChannel) {\r\n      // For PNG color types 4 and 6, the transparency data is stored as a alpha\r\n      // channel mixed in with the main image data. Separate this data out into an\r\n      // SMask object and store it separately in the PDF.\r\n      dataDecoded = true;\r\n      return this.splitAlphaChannel();\r\n    }\r\n\r\n    if (isInterlaced && !dataDecoded) {\r\n      return this.decodeData();\r\n    }\r\n\r\n    this.finalize();\r\n  }\r\n\r\n  finalize() {\r\n    if (this.alphaChannel) {\r\n      const sMask = this.document.ref({\r\n        Type: 'XObject',\r\n        Subtype: 'Image',\r\n        Height: this.height,\r\n        Width: this.width,\r\n        BitsPerComponent: 8,\r\n        Filter: 'FlateDecode',\r\n        ColorSpace: 'DeviceGray',\r\n        Decode: [0, 1]\r\n      });\r\n\r\n      sMask.end(this.alphaChannel);\r\n      this.obj.data['SMask'] = sMask;\r\n    }\r\n\r\n    // add the actual image data\r\n    this.obj.end(this.imgData);\r\n\r\n    // free memory\r\n    this.image = null;\r\n    return (this.imgData = null);\r\n  }\r\n\r\n  splitAlphaChannel() {\r\n    return this.image.decodePixels(pixels => {\r\n      let a, p;\r\n      const colorCount = this.image.colors;\r\n      const pixelCount = this.width * this.height;\r\n      const imgData = Buffer.alloc(pixelCount * colorCount);\r\n      const alphaChannel = Buffer.alloc(pixelCount);\r\n\r\n      let i = (p = a = 0);\r\n      const len = pixels.length;\r\n      // For 16bit images copy only most significant byte (MSB) - PNG data is always stored in network byte order (MSB first)\r\n      const skipByteCount = this.image.bits === 16 ? 1 : 0;\r\n      while (i < len) {\r\n        for (let colorIndex = 0; colorIndex < colorCount; colorIndex++) {\r\n          imgData[p++] = pixels[i++];\r\n          i += skipByteCount;\r\n        }\r\n        alphaChannel[a++] = pixels[i++];\r\n        i += skipByteCount;\r\n      }\r\n\r\n      this.imgData = zlib.deflateSync(imgData);\r\n      this.alphaChannel = zlib.deflateSync(alphaChannel);\r\n      return this.finalize();\r\n    });\r\n  }\r\n\r\n  loadIndexedAlphaChannel() {\r\n    const transparency = this.image.transparency.indexed;\r\n    return this.image.decodePixels(pixels => {\r\n      const alphaChannel = Buffer.alloc(this.width * this.height);\r\n\r\n      let i = 0;\r\n      for (let j = 0, end = pixels.length; j < end; j++) {\r\n        alphaChannel[i++] = transparency[pixels[j]];\r\n      }\r\n\r\n      this.alphaChannel = zlib.deflateSync(alphaChannel);\r\n      return this.finalize();\r\n    });\r\n  }\r\n\r\n  decodeData() {\r\n    this.image.decodePixels(pixels => {\r\n      this.imgData = zlib.deflateSync(pixels);\r\n      this.finalize();\r\n    });\r\n  }\r\n}\r\n\r\nexport default PNGImage;\r\n", "/*\r\nPDFImage - embeds images in PDF documents\r\nBy <PERSON> Govett\r\n*/\r\n\r\nimport fs from 'fs';\r\nimport JPEG from './image/jpeg';\r\nimport PNG from './image/png';\r\n\r\nclass PDFImage {\r\n  static open(src, label) {\r\n    let data;\r\n    if (Buffer.isBuffer(src)) {\r\n      data = src;\r\n    } else if (src instanceof ArrayBuffer) {\r\n      data = Buffer.from(new Uint8Array(src));\r\n    } else {\r\n      let match;\r\n      if ((match = /^data:.+;base64,(.*)$/.exec(src))) {\r\n        data = Buffer.from(match[1], 'base64');\r\n      } else {\r\n        data = fs.readFileSync(src);\r\n        if (!data) {\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (data[0] === 0xff && data[1] === 0xd8) {\r\n      return new JPEG(data, label);\r\n    } else if (data[0] === 0x89 && data.toString('ascii', 1, 4) === 'PNG') {\r\n      return new PNG(data, label);\r\n    } else {\r\n      throw new Error('Unknown image format.');\r\n    }\r\n  }\r\n}\r\n\r\nexport default PDFImage;\r\n", "import PDFImage from '../image';\r\n\r\nexport default {\r\n  initImages() {\r\n    this._imageRegistry = {};\r\n    return (this._imageCount = 0);\r\n  },\r\n\r\n  image(src, x, y, options = {}) {\r\n    let bh, bp, bw, image, ip, left, left1;\r\n    if (typeof x === 'object') {\r\n      options = x;\r\n      x = null;\r\n    }\r\n\r\n    x = (left = x != null ? x : options.x) != null ? left : this.x;\r\n    y = (left1 = y != null ? y : options.y) != null ? left1 : this.y;\r\n\r\n    if (typeof src === 'string') {\r\n      image = this._imageRegistry[src];\r\n    }\r\n\r\n    if (!image) {\r\n      if (src.width && src.height) {\r\n        image = src;\r\n      } else {\r\n        image = this.openImage(src);\r\n      }\r\n    }\r\n\r\n    if (!image.obj) {\r\n      image.embed(this);\r\n    }\r\n\r\n    if (this.page.xobjects[image.label] == null) {\r\n      this.page.xobjects[image.label] = image.obj;\r\n    }\r\n\r\n    let w = options.width || image.width;\r\n    let h = options.height || image.height;\r\n\r\n    if (options.width && !options.height) {\r\n      const wp = w / image.width;\r\n      w = image.width * wp;\r\n      h = image.height * wp;\r\n    } else if (options.height && !options.width) {\r\n      const hp = h / image.height;\r\n      w = image.width * hp;\r\n      h = image.height * hp;\r\n    } else if (options.scale) {\r\n      w = image.width * options.scale;\r\n      h = image.height * options.scale;\r\n    } else if (options.fit) {\r\n      [bw, bh] = options.fit;\r\n      bp = bw / bh;\r\n      ip = image.width / image.height;\r\n      if (ip > bp) {\r\n        w = bw;\r\n        h = bw / ip;\r\n      } else {\r\n        h = bh;\r\n        w = bh * ip;\r\n      }\r\n    } else if (options.cover) {\r\n      [bw, bh] = options.cover;\r\n      bp = bw / bh;\r\n      ip = image.width / image.height;\r\n      if (ip > bp) {\r\n        h = bh;\r\n        w = bh * ip;\r\n      } else {\r\n        w = bw;\r\n        h = bw / ip;\r\n      }\r\n    }\r\n\r\n    if (options.fit || options.cover) {\r\n      if (options.align === 'center') {\r\n        x = x + bw / 2 - w / 2;\r\n      } else if (options.align === 'right') {\r\n        x = x + bw - w;\r\n      }\r\n\r\n      if (options.valign === 'center') {\r\n        y = y + bh / 2 - h / 2;\r\n      } else if (options.valign === 'bottom') {\r\n        y = y + bh - h;\r\n      }\r\n    }\r\n\r\n    // create link annotations if the link option is given\r\n    if (options.link != null) {\r\n      this.link(x, y, w, h, options.link);\r\n    }\r\n    if (options.goTo != null) {\r\n      this.goTo(x, y, w, h, options.goTo);\r\n    }\r\n    if (options.destination != null) {\r\n      this.addNamedDestination(options.destination, 'XYZ', x, y, null);\r\n    }\r\n\r\n    // Set the current y position to below the image if it is in the document flow\r\n    if (this.y === y) {\r\n      this.y += h;\r\n    }\r\n\r\n    this.save();\r\n    this.transform(w, 0, 0, -h, x, y + h);\r\n    this.addContent(`/${image.label} Do`);\r\n    this.restore();\r\n\r\n    return this;\r\n  },\r\n\r\n  openImage(src) {\r\n    let image;\r\n    if (typeof src === 'string') {\r\n      image = this._imageRegistry[src];\r\n    }\r\n\r\n    if (!image) {\r\n      image = PDFImage.open(src, `I${++this._imageCount}`);\r\n      if (typeof src === 'string') {\r\n        this._imageRegistry[src] = image;\r\n      }\r\n    }\r\n\r\n    return image;\r\n  }\r\n};\r\n", "export default {\r\n  annotate(x, y, w, h, options) {\r\n    options.Type = 'Annot';\r\n    options.Rect = this._convertRect(x, y, w, h);\r\n    options.Border = [0, 0, 0];\r\n\r\n    if (options.Subtype === 'Link' && typeof options.F === 'undefined') {\r\n      options.F = 1 << 2; // Print Annotation Flag\r\n    }\r\n\r\n    if (options.Subtype !== 'Link') {\r\n      if (options.C == null) {\r\n        options.C = this._normalizeColor(options.color || [0, 0, 0]);\r\n      }\r\n    } // convert colors\r\n    delete options.color;\r\n\r\n    if (typeof options.Dest === 'string') {\r\n      options.Dest = new String(options.Dest);\r\n    }\r\n\r\n    // Capitalize keys\r\n    for (let key in options) {\r\n      const val = options[key];\r\n      options[key[0].toUpperCase() + key.slice(1)] = val;\r\n    }\r\n\r\n    const ref = this.ref(options);\r\n    this.page.annotations.push(ref);\r\n    ref.end();\r\n    return this;\r\n  },\r\n\r\n  note(x, y, w, h, contents, options = {}) {\r\n    options.Subtype = 'Text';\r\n    options.Contents = new String(contents);\r\n    options.Name = 'Comment';\r\n    if (options.color == null) {\r\n      options.color = [243, 223, 92];\r\n    }\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  goTo(x, y, w, h, name, options = {}) {\r\n    options.Subtype = 'Link';\r\n    options.A = this.ref({\r\n      S: 'GoTo',\r\n      D: new String(name)\r\n    });\r\n    options.A.end();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  link(x, y, w, h, url, options = {}) {\r\n    options.Subtype = 'Link';\r\n\r\n    if (typeof url === 'number') {\r\n      // Link to a page in the document (the page must already exist)\r\n      const pages = this._root.data.Pages.data;\r\n      if (url >= 0 && url < pages.Kids.length) {\r\n        options.A = this.ref({\r\n          S: 'GoTo',\r\n          D: [pages.Kids[url], 'XYZ', null, null, null]\r\n        });\r\n        options.A.end();\r\n      } else {\r\n        throw new Error(`The document has no page ${url}`);\r\n      }\r\n    } else {\r\n      // Link to an external url\r\n      options.A = this.ref({\r\n        S: 'URI',\r\n        URI: new String(url)\r\n      });\r\n      options.A.end();\r\n    }\r\n\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  _markup(x, y, w, h, options = {}) {\r\n    const [x1, y1, x2, y2] = this._convertRect(x, y, w, h);\r\n    options.QuadPoints = [x1, y2, x2, y2, x1, y1, x2, y1];\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  highlight(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Highlight';\r\n    if (options.color == null) {\r\n      options.color = [241, 238, 148];\r\n    }\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  underline(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Underline';\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  strike(x, y, w, h, options = {}) {\r\n    options.Subtype = 'StrikeOut';\r\n    return this._markup(x, y, w, h, options);\r\n  },\r\n\r\n  lineAnnotation(x1, y1, x2, y2, options = {}) {\r\n    options.Subtype = 'Line';\r\n    options.Contents = new String();\r\n    options.L = [x1, this.page.height - y1, x2, this.page.height - y2];\r\n    return this.annotate(x1, y1, x2, y2, options);\r\n  },\r\n\r\n  rectAnnotation(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Square';\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  ellipseAnnotation(x, y, w, h, options = {}) {\r\n    options.Subtype = 'Circle';\r\n    options.Contents = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  textAnnotation(x, y, w, h, text, options = {}) {\r\n    options.Subtype = 'FreeText';\r\n    options.Contents = new String(text);\r\n    options.DA = new String();\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  fileAnnotation(x, y, w, h, file = {}, options = {}) {\r\n    // create hidden file\r\n    const filespec = this.file(\r\n      file.src,\r\n      Object.assign({ hidden: true }, file)\r\n    );\r\n\r\n    options.Subtype = 'FileAttachment';\r\n    options.FS = filespec;\r\n\r\n    // add description from filespec unless description (Contents) has already been set\r\n    if (options.Contents) {\r\n      options.Contents = new String(options.Contents);\r\n    } else if (filespec.data.Desc) {\r\n      options.Contents = filespec.data.Desc;\r\n    }\r\n\r\n    return this.annotate(x, y, w, h, options);\r\n  },\r\n\r\n  _convertRect(x1, y1, w, h) {\r\n    // flip y1 and y2\r\n    let y2 = y1;\r\n    y1 += h;\r\n\r\n    // make x2\r\n    let x2 = x1 + w;\r\n\r\n    // apply current transformation matrix to points\r\n    const [m0, m1, m2, m3, m4, m5] = this._ctm;\r\n    x1 = m0 * x1 + m2 * y1 + m4;\r\n    y1 = m1 * x1 + m3 * y1 + m5;\r\n    x2 = m0 * x2 + m2 * y2 + m4;\r\n    y2 = m1 * x2 + m3 * y2 + m5;\r\n\r\n    return [x1, y1, x2, y2];\r\n  }\r\n};\r\n", "class PDFOutline {\r\n  constructor(document, parent, title, dest, options = { expanded: false }) {\r\n    this.document = document;\r\n    this.options = options;\r\n    this.outlineData = {};\r\n\r\n    if (dest !== null) {\r\n      this.outlineData['Dest'] = [dest.dictionary, 'Fit'];\r\n    }\r\n\r\n    if (parent !== null) {\r\n      this.outlineData['Parent'] = parent;\r\n    }\r\n\r\n    if (title !== null) {\r\n      this.outlineData['Title'] = new String(title);\r\n    }\r\n\r\n    this.dictionary = this.document.ref(this.outlineData);\r\n    this.children = [];\r\n  }\r\n\r\n  addItem(title, options = { expanded: false }) {\r\n    const result = new PDFOutline(\r\n      this.document,\r\n      this.dictionary,\r\n      title,\r\n      this.document.page,\r\n      options\r\n    );\r\n    this.children.push(result);\r\n\r\n    return result;\r\n  }\r\n\r\n  endOutline() {\r\n    if (this.children.length > 0) {\r\n      if (this.options.expanded) {\r\n        this.outlineData.Count = this.children.length;\r\n      }\r\n\r\n      const first = this.children[0],\r\n        last = this.children[this.children.length - 1];\r\n      this.outlineData.First = first.dictionary;\r\n      this.outlineData.Last = last.dictionary;\r\n\r\n      for (let i = 0, len = this.children.length; i < len; i++) {\r\n        const child = this.children[i];\r\n        if (i > 0) {\r\n          child.outlineData.Prev = this.children[i - 1].dictionary;\r\n        }\r\n        if (i < this.children.length - 1) {\r\n          child.outlineData.Next = this.children[i + 1].dictionary;\r\n        }\r\n        child.endOutline();\r\n      }\r\n    }\r\n\r\n    return this.dictionary.end();\r\n  }\r\n}\r\n\r\nexport default PDFOutline;\r\n", "import PDFOutline from '../outline';\r\n\r\nexport default {\r\n  initOutline() {\r\n    return (this.outline = new PDFOutline(this, null, null, null));\r\n  },\r\n\r\n  endOutline() {\r\n    this.outline.endOutline();\r\n    if (this.outline.children.length > 0) {\r\n      this._root.data.Outlines = this.outline.dictionary;\r\n      return (this._root.data.PageMode = 'UseOutlines');\r\n    }\r\n  }\r\n};\r\n", "/*\r\nPDFStructureContent - a reference to a marked structure content\r\nBy <PERSON>\r\n*/\r\n\r\nclass PDFStructureContent {\r\n  constructor(pageRef, mcid) {\r\n    this.refs = [{ pageRef, mcid }];\r\n  }\r\n\r\n  push(structContent) {\r\n    structContent.refs.forEach((ref) => this.refs.push(ref));\r\n  }\r\n}\r\n\r\nexport default PDFStructureContent;\r\n", "/*\r\nPDFStructureElement - represents an element in the PDF logical structure tree\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFStructureContent from \"./structure_content\";\r\n\r\nclass PDFStructureElement {\r\n  constructor(document, type, options = {}, children = null) {\r\n    this.document = document;\r\n\r\n    this._attached = false;\r\n    this._ended = false;\r\n    this._flushed = false;\r\n    this.dictionary = document.ref({\r\n      // Type: \"StructElem\",\r\n      S: type\r\n    });\r\n\r\n    const data = this.dictionary.data;\r\n\r\n    if (Array.isArray(options) || this._isValidChild(options)) {\r\n      children = options;\r\n      options = {};\r\n    }\r\n\r\n    if (typeof options.title !== 'undefined') {\r\n      data.T = new String(options.title);\r\n    }\r\n    if (typeof options.lang !== 'undefined') {\r\n      data.Lang = new String(options.lang);\r\n    }\r\n    if (typeof options.alt !== 'undefined') {\r\n      data.Alt = new String(options.alt);\r\n    }\r\n    if (typeof options.expanded !== 'undefined') {\r\n      data.E = new String(options.expanded);\r\n    }\r\n    if (typeof options.actual !== 'undefined') {\r\n      data.ActualText = new String(options.actual);\r\n    }\r\n\r\n    this._children = [];\r\n\r\n    if (children) {\r\n      if (!Array.isArray(children)) {\r\n        children = [children];\r\n      }\r\n      children.forEach((child) => this.add(child));\r\n      this.end();\r\n    }\r\n  }\r\n\r\n  add(child) {\r\n    if (this._ended) {\r\n      throw new Error(`Cannot add child to already-ended structure element`);\r\n    }\r\n\r\n    if (!this._isValidChild(child)) {\r\n      throw new Error(`Invalid structure element child`);\r\n    }\r\n\r\n    if (child instanceof PDFStructureElement) {\r\n      child.setParent(this.dictionary);\r\n      if (this._attached) {\r\n        child.setAttached();\r\n      }\r\n    }\r\n\r\n    if (child instanceof PDFStructureContent) {\r\n      this._addContentToParentTree(child);\r\n    }\r\n\r\n    if (typeof child === 'function' && this._attached) {\r\n      // _contentForClosure() adds the content to the parent tree\r\n      child = this._contentForClosure(child);\r\n    }\r\n\r\n    this._children.push(child);\r\n\r\n    return this;\r\n  }\r\n\r\n  _addContentToParentTree(content) {\r\n    content.refs.forEach(({ pageRef, mcid }) => {\r\n      const pageStructParents = this.document.getStructParentTree()\r\n        .get(pageRef.data.StructParents);\r\n      pageStructParents[mcid] = this.dictionary;\r\n    });\r\n  }\r\n\r\n  setParent(parentRef) {\r\n    if (this.dictionary.data.P) {\r\n      throw new Error(`Structure element added to more than one parent`);\r\n    }\r\n\r\n    this.dictionary.data.P = parentRef;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  setAttached() {\r\n    if (this._attached) {\r\n      return;\r\n    }\r\n\r\n    this._children.forEach((child, index) => {\r\n      if (child instanceof PDFStructureElement) {\r\n        child.setAttached();\r\n      }\r\n      if (typeof child === 'function') {\r\n        this._children[index] = this._contentForClosure(child);\r\n      }\r\n    });\r\n\r\n    this._attached = true;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  end() {\r\n    if (this._ended) {\r\n      return;\r\n    }\r\n\r\n    this._children\r\n      .filter((child) => child instanceof PDFStructureElement)\r\n      .forEach((child) => child.end());\r\n\r\n    this._ended = true;\r\n\r\n    this._flush();\r\n  }\r\n\r\n  _isValidChild(child) {\r\n    return child instanceof PDFStructureElement ||\r\n        child instanceof PDFStructureContent ||\r\n        typeof child === 'function';\r\n  }\r\n\r\n  _contentForClosure(closure) {\r\n    const content = this.document.markStructureContent(this.dictionary.data.S);\r\n    closure();\r\n    this.document.endMarkedContent();\r\n\r\n    this._addContentToParentTree(content);\r\n\r\n    return content;\r\n  }\r\n\r\n  _isFlushable() {\r\n    if (!this.dictionary.data.P || !this._ended) {\r\n      return false;\r\n    }\r\n\r\n    return this._children.every((child) => {\r\n      if (typeof child === 'function') {\r\n        return false;\r\n      }\r\n      if (child instanceof PDFStructureElement) {\r\n        return child._isFlushable();\r\n      }\r\n      return true;\r\n    });\r\n  }\r\n\r\n  _flush() {\r\n    if (this._flushed || !this._isFlushable()) {\r\n      return;\r\n    }\r\n\r\n    this.dictionary.data.K = [];\r\n\r\n    this._children.forEach((child) => this._flushChild(child));\r\n\r\n    this.dictionary.end();\r\n\r\n    // free memory used by children; the dictionary itself may still be\r\n    // referenced by a parent structure element or root, but we can\r\n    // at least trim the tree here\r\n    this._children = [];\r\n    this.dictionary.data.K = null;\r\n\r\n    this._flushed = true;\r\n  }\r\n\r\n  _flushChild(child) {\r\n    if (child instanceof PDFStructureElement) {\r\n      this.dictionary.data.K.push(child.dictionary);\r\n    }\r\n\r\n    if (child instanceof PDFStructureContent) {\r\n      child.refs.forEach(({ pageRef, mcid }) => {\r\n        if (!this.dictionary.data.Pg) {\r\n          this.dictionary.data.Pg = pageRef;\r\n        }\r\n\r\n        if (this.dictionary.data.Pg === pageRef) {\r\n          this.dictionary.data.K.push(mcid);\r\n        } else {\r\n          this.dictionary.data.K.push({\r\n            Type: \"MCR\",\r\n            Pg: pageRef,\r\n            MCID: mcid\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n\r\nexport default PDFStructureElement;\r\n", "/*\r\nPDFNumberTree - represents a number tree object\r\n*/\r\n\r\nimport PDFTree from \"./tree\";\r\n\r\nclass PDFNumberTree extends PDFTree {\r\n  _compareKeys(a, b) {\r\n    return parseInt(a) - parseInt(b);\r\n  }\r\n\r\n  _keysName() {\r\n    return \"Nums\";\r\n  }\r\n\r\n  _dataForKey(k) {\r\n    return parseInt(k);\r\n  }\r\n}\r\n\r\nexport default PDFNumberTree;\r\n", "/*\r\nMarkings mixin - support marked content sequences in content streams\r\nBy <PERSON>\r\n*/\r\n\r\nimport PDFStructureElement from \"../structure_element\";\r\nimport PDFStructureContent from \"../structure_content\";\r\nimport PDFNumberTree from \"../number_tree\";\r\nimport PDFObject from \"../object\";\r\n\r\nexport default {\r\n\r\n  initMarkings(options) {\r\n    this.structChildren = [];\r\n\r\n    if (options.tagged) {\r\n      this.getMarkInfoDictionary().data.Marked = true;\r\n      this.getStructTreeRoot();\r\n    }\r\n  },\r\n\r\n  markContent(tag, options = null) {\r\n    if (tag === 'Artifact' || (options && options.mcid)) {\r\n      let toClose = 0;\r\n      this.page.markings.forEach((marking) => {\r\n        if (toClose || marking.structContent || marking.tag === 'Artifact') {\r\n          toClose++;\r\n        }\r\n      });\r\n      while (toClose--) {\r\n        this.endMarkedContent();\r\n      }\r\n    }\r\n\r\n    if (!options) {\r\n      this.page.markings.push({ tag });\r\n      this.addContent(`/${tag} BMC`);\r\n      return this;\r\n    }\r\n\r\n    this.page.markings.push({ tag, options });\r\n\r\n    const dictionary = {};\r\n\r\n    if (typeof options.mcid !== 'undefined') {\r\n      dictionary.MCID = options.mcid;\r\n    }\r\n    if (tag === 'Artifact') {\r\n      if (typeof options.type === 'string') {\r\n        dictionary.Type = options.type;\r\n      }\r\n      if (Array.isArray(options.bbox)) {\r\n        dictionary.BBox = [options.bbox[0], this.page.height - options.bbox[3],\r\n          options.bbox[2], this.page.height - options.bbox[1]];\r\n      }\r\n      if (Array.isArray(options.attached) &&\r\n        options.attached.every(val => typeof val === 'string')) {\r\n        dictionary.Attached = options.attached;\r\n      }\r\n    }\r\n    if (tag === 'Span') {\r\n      if (options.lang) {\r\n        dictionary.Lang = new String(options.lang);\r\n      }\r\n      if (options.alt) {\r\n        dictionary.Alt = new String(options.alt);\r\n      }\r\n      if (options.expanded) {\r\n        dictionary.E = new String(options.expanded);\r\n      }\r\n      if (options.actual) {\r\n        dictionary.ActualText = new String(options.actual);\r\n      }\r\n    }\r\n\r\n    this.addContent(`/${tag} ${PDFObject.convert(dictionary)} BDC`);\r\n    return this;\r\n  },\r\n\r\n  markStructureContent(tag, options = {}) {\r\n    const pageStructParents = this.getStructParentTree().get(this.page.structParentTreeKey);\r\n    const mcid = pageStructParents.length;\r\n    pageStructParents.push(null);\r\n\r\n    this.markContent(tag, { ...options, mcid });\r\n\r\n    const structContent = new PDFStructureContent(this.page.dictionary, mcid);\r\n    this.page.markings.slice(-1)[0].structContent = structContent;\r\n    return structContent;\r\n  },\r\n\r\n  endMarkedContent() {\r\n    this.page.markings.pop();\r\n    this.addContent('EMC');\r\n    return this;\r\n  },\r\n\r\n  struct(type, options = {}, children = null) {\r\n    return new PDFStructureElement(this, type, options, children);\r\n  },\r\n\r\n  addStructure(structElem) {\r\n    const structTreeRoot = this.getStructTreeRoot();\r\n    structElem.setParent(structTreeRoot);\r\n    structElem.setAttached();\r\n    this.structChildren.push(structElem);\r\n    if (!structTreeRoot.data.K) {\r\n      structTreeRoot.data.K = [];\r\n    }\r\n    structTreeRoot.data.K.push(structElem.dictionary);\r\n    return this;\r\n  },\r\n\r\n  initPageMarkings(pageMarkings) {\r\n    pageMarkings.forEach((marking) => {\r\n      if (marking.structContent) {\r\n        const structContent = marking.structContent;\r\n        const newStructContent = this.markStructureContent(marking.tag, marking.options);\r\n        structContent.push(newStructContent);\r\n        this.page.markings.slice(-1)[0].structContent = structContent;\r\n      } else {\r\n        this.markContent(marking.tag, marking.options);\r\n      }\r\n    });\r\n  },\r\n\r\n  endPageMarkings(page) {\r\n    const pageMarkings = page.markings;\r\n    pageMarkings.forEach(() => page.write('EMC'));\r\n    page.markings = [];\r\n    return pageMarkings;\r\n  },\r\n\r\n  getMarkInfoDictionary() {\r\n    if (!this._root.data.MarkInfo) {\r\n      this._root.data.MarkInfo = this.ref({});\r\n    }\r\n    return this._root.data.MarkInfo;\r\n  },\r\n\r\n  getStructTreeRoot() {\r\n    if (!this._root.data.StructTreeRoot) {\r\n      this._root.data.StructTreeRoot = this.ref({\r\n        Type: 'StructTreeRoot',\r\n        ParentTree: new PDFNumberTree(),\r\n        ParentTreeNextKey: 0\r\n      });\r\n    }\r\n    return this._root.data.StructTreeRoot;\r\n  },\r\n\r\n  getStructParentTree() {\r\n    return this.getStructTreeRoot().data.ParentTree;\r\n  },\r\n\r\n  createStructParentTreeNextKey() {\r\n    // initialise the MarkInfo dictionary\r\n    this.getMarkInfoDictionary();\r\n\r\n    const structTreeRoot = this.getStructTreeRoot();\r\n    const key = structTreeRoot.data.ParentTreeNextKey++;\r\n    structTreeRoot.data.ParentTree.add(key, []);\r\n    return key;\r\n  },\r\n\r\n  endMarkings() {\r\n    const structTreeRoot = this._root.data.StructTreeRoot;\r\n    if (structTreeRoot) {\r\n      structTreeRoot.end();\r\n      this.structChildren.forEach((structElem) => structElem.end());\r\n    }\r\n    if (this._root.data.MarkInfo) {\r\n      this._root.data.MarkInfo.end();\r\n    }\r\n  }\r\n\r\n};\r\n", "const FIELD_FLAGS = {\r\n  readOnly: 1,\r\n  required: 2,\r\n  noExport: 4,\r\n  multiline: 0x1000,\r\n  password: 0x2000,\r\n  toggleToOffButton: 0x4000,\r\n  radioButton: 0x8000,\r\n  pushButton: 0x10000,\r\n  combo: 0x20000,\r\n  edit: 0x40000,\r\n  sort: 0x80000,\r\n  multiSelect: 0x200000,\r\n  noSpell: 0x400000\r\n};\r\nconst FIELD_JUSTIFY = {\r\n  left: 0,\r\n  center: 1,\r\n  right: 2\r\n};\r\nconst VALUE_MAP = { value: 'V', defaultValue: 'DV' };\r\nconst FORMAT_SPECIAL = {\r\n  zip: '0',\r\n  zipPlus4: '1',\r\n  zip4: '1',\r\n  phone: '2',\r\n  ssn: '3'\r\n};\r\nconst FORMAT_DEFAULT = {\r\n  number: {\r\n    nDec: 0,\r\n    sepComma: false,\r\n    negStyle: 'MinusBlack',\r\n    currency: '',\r\n    currencyPrepend: true\r\n  },\r\n  percent: {\r\n    nDec: 0,\r\n    sepComma: false\r\n  }\r\n};\r\n\r\nexport default {\r\n  /**\r\n   * Must call if adding AcroForms to a document. Must also call font() before\r\n   * this method to set the default font.\r\n   */\r\n  initForm() {\r\n    if (!this._font) {\r\n      throw new Error('Must set a font before calling initForm method');\r\n    }\r\n    this._acroform = {\r\n      fonts: {},\r\n      defaultFont: this._font.name\r\n    };\r\n    this._acroform.fonts[this._font.id] = this._font.ref();\r\n\r\n    let data = {\r\n      Fields: [],\r\n      NeedAppearances: true,\r\n      DA: new String(`/${this._font.id} 0 Tf 0 g`),\r\n      DR: {\r\n        Font: {}\r\n      }\r\n    };\r\n    data.DR.Font[this._font.id] = this._font.ref();\r\n    const AcroForm = this.ref(data);\r\n    this._root.data.AcroForm = AcroForm;\r\n    return this;\r\n  },\r\n\r\n  /**\r\n   * Called automatically by document.js\r\n   */\r\n  endAcroForm() {\r\n    if (this._root.data.AcroForm) {\r\n      if (\r\n        !Object.keys(this._acroform.fonts).length &&\r\n        !this._acroform.defaultFont\r\n      ) {\r\n        throw new Error('No fonts specified for PDF form');\r\n      }\r\n      let fontDict = this._root.data.AcroForm.data.DR.Font;\r\n      Object.keys(this._acroform.fonts).forEach(name => {\r\n        fontDict[name] = this._acroform.fonts[name];\r\n      });\r\n      this._root.data.AcroForm.data.Fields.forEach(fieldRef => {\r\n        this._endChild(fieldRef);\r\n      });\r\n      this._root.data.AcroForm.end();\r\n    }\r\n    return this;\r\n  },\r\n\r\n  _endChild(ref) {\r\n    if (Array.isArray(ref.data.Kids)) {\r\n      ref.data.Kids.forEach(childRef => {\r\n        this._endChild(childRef);\r\n      });\r\n      ref.end();\r\n    }\r\n    return this;\r\n  },\r\n\r\n  /**\r\n   * Creates and adds a form field to the document. Form fields are intermediate\r\n   * nodes in a PDF form that are used to specify form name heirarchy and form\r\n   * value defaults.\r\n   * @param {string} name - field name (T attribute in field dictionary)\r\n   * @param {object} options  - other attributes to include in field dictionary\r\n   */\r\n  formField(name, options = {}) {\r\n    let fieldDict = this._fieldDict(name, null, options);\r\n    let fieldRef = this.ref(fieldDict);\r\n    this._addToParent(fieldRef);\r\n    return fieldRef;\r\n  },\r\n\r\n  /**\r\n   * Creates and adds a Form Annotation to the document. Form annotations are\r\n   * called Widget annotations internally within a PDF file.\r\n   * @param {string} name - form field name (T attribute of widget annotation\r\n   * dictionary)\r\n   * @param {number} x\r\n   * @param {number} y\r\n   * @param {number} w\r\n   * @param {number} h\r\n   * @param {object} options\r\n   */\r\n  formAnnotation(name, type, x, y, w, h, options = {}) {\r\n    let fieldDict = this._fieldDict(name, type, options);\r\n    fieldDict.Subtype = 'Widget';\r\n    if (fieldDict.F === undefined) {\r\n      fieldDict.F = 4; // print the annotation\r\n    }\r\n\r\n    // Add Field annot to page, and get it's ref\r\n    this.annotate(x, y, w, h, fieldDict);\r\n    let annotRef = this.page.annotations[this.page.annotations.length - 1];\r\n\r\n    return this._addToParent(annotRef);\r\n  },\r\n\r\n  formText(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'text', x, y, w, h, options);\r\n  },\r\n\r\n  formPushButton(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'pushButton', x, y, w, h, options);\r\n  },\r\n\r\n  formCombo(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'combo', x, y, w, h, options);\r\n  },\r\n\r\n  formList(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'list', x, y, w, h, options);\r\n  },\r\n\r\n  formRadioButton(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'radioButton', x, y, w, h, options);\r\n  },\r\n\r\n  formCheckbox(name, x, y, w, h, options = {}) {\r\n    return this.formAnnotation(name, 'checkbox', x, y, w, h, options);\r\n  },\r\n\r\n  _addToParent(fieldRef) {\r\n    let parent = fieldRef.data.Parent;\r\n    if (parent) {\r\n      if (!parent.data.Kids) {\r\n        parent.data.Kids = [];\r\n      }\r\n      parent.data.Kids.push(fieldRef);\r\n    } else {\r\n      this._root.data.AcroForm.data.Fields.push(fieldRef);\r\n    }\r\n    return this;\r\n  },\r\n\r\n  _fieldDict(name, type, options = {}) {\r\n    if (!this._acroform) {\r\n      throw new Error(\r\n        'Call document.initForms() method before adding form elements to document'\r\n      );\r\n    }\r\n    let opts = Object.assign({}, options);\r\n    if (type !== null) {\r\n      opts = this._resolveType(type, options);\r\n    }\r\n    opts = this._resolveFlags(opts);\r\n    opts = this._resolveJustify(opts);\r\n    opts = this._resolveFont(opts);\r\n    opts = this._resolveStrings(opts);\r\n    opts = this._resolveColors(opts);\r\n    opts = this._resolveFormat(opts);\r\n    opts.T = new String(name);\r\n    if (opts.parent) {\r\n      opts.Parent = opts.parent;\r\n      delete opts.parent;\r\n    }\r\n    return opts;\r\n  },\r\n\r\n  _resolveType(type, opts) {\r\n    if (type === 'text') {\r\n      opts.FT = 'Tx';\r\n    } else if (type === 'pushButton') {\r\n      opts.FT = 'Btn';\r\n      opts.pushButton = true;\r\n    } else if (type === 'radioButton') {\r\n      opts.FT = 'Btn';\r\n      opts.radioButton = true;\r\n    } else if (type === 'checkbox') {\r\n      opts.FT = 'Btn';\r\n    } else if (type === 'combo') {\r\n      opts.FT = 'Ch';\r\n      opts.combo = true;\r\n    } else if (type === 'list') {\r\n      opts.FT = 'Ch';\r\n    } else {\r\n      throw new Error(`Invalid form annotation type '${type}'`);\r\n    }\r\n    return opts;\r\n  },\r\n\r\n  _resolveFormat(opts) {\r\n    const f = opts.format;\r\n    if (f && f.type) {\r\n      let fnKeystroke;\r\n      let fnFormat;\r\n      let params = '';\r\n      if (FORMAT_SPECIAL[f.type] !== undefined) {\r\n        fnKeystroke = `AFSpecial_Keystroke`;\r\n        fnFormat = `AFSpecial_Format`;\r\n        params = FORMAT_SPECIAL[f.type];\r\n      } else {\r\n        let format = f.type.charAt(0).toUpperCase() + f.type.slice(1);\r\n        fnKeystroke = `AF${format}_Keystroke`;\r\n        fnFormat = `AF${format}_Format`;\r\n\r\n        if (f.type === 'date') {\r\n          fnKeystroke += 'Ex';\r\n          params = String(f.param);\r\n        } else if (f.type === 'time') {\r\n          params = String(f.param);\r\n        } else if (f.type === 'number') {\r\n          let p = Object.assign({}, FORMAT_DEFAULT.number, f);\r\n          params = String(\r\n            [\r\n              String(p.nDec),\r\n              p.sepComma ? '0' : '1',\r\n              '\"' + p.negStyle + '\"',\r\n              'null',\r\n              '\"' + p.currency + '\"',\r\n              String(p.currencyPrepend)\r\n            ].join(',')\r\n          );\r\n        } else if (f.type === 'percent') {\r\n          let p = Object.assign({}, FORMAT_DEFAULT.percent, f);\r\n          params = String([String(p.nDec), p.sepComma ? '0' : '1'].join(','));\r\n        }\r\n      }\r\n      opts.AA = opts.AA ? opts.AA : {};\r\n      opts.AA.K = {\r\n        S: 'JavaScript',\r\n        JS: new String(`${fnKeystroke}(${params});`)\r\n      };\r\n      opts.AA.F = {\r\n        S: 'JavaScript',\r\n        JS: new String(`${fnFormat}(${params});`)\r\n      };\r\n    }\r\n    delete opts.format;\r\n    return opts;\r\n  },\r\n\r\n  _resolveColors(opts) {\r\n    let color = this._normalizeColor(opts.backgroundColor);\r\n    if (color) {\r\n      if (!opts.MK) {\r\n        opts.MK = {};\r\n      }\r\n      opts.MK.BG = color;\r\n    }\r\n    color = this._normalizeColor(opts.borderColor);\r\n    if (color) {\r\n      if (!opts.MK) {\r\n        opts.MK = {};\r\n      }\r\n      opts.MK.BC = color;\r\n    }\r\n    delete opts.backgroundColor;\r\n    delete opts.borderColor;\r\n    return opts;\r\n  },\r\n\r\n  _resolveFlags(options) {\r\n    let result = 0;\r\n    Object.keys(options).forEach(key => {\r\n      if (FIELD_FLAGS[key]) {\r\n        result |= FIELD_FLAGS[key];\r\n        delete options[key];\r\n      }\r\n    });\r\n    if (result !== 0) {\r\n      options.Ff = options.Ff ? options.Ff : 0;\r\n      options.Ff |= result;\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveJustify(options) {\r\n    let result = 0;\r\n    if (options.align !== undefined) {\r\n      if (typeof FIELD_JUSTIFY[options.align] === 'number') {\r\n        result = FIELD_JUSTIFY[options.align];\r\n      }\r\n      delete options.align;\r\n    }\r\n    if (result !== 0) {\r\n      options.Q = result; // default\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveFont(options) {\r\n    // add current font to document-level AcroForm dict if necessary\r\n    if (this._acroform.fonts[this._font.id] === null) {\r\n      this._acroform.fonts[this._font.id] = this._font.ref();\r\n    }\r\n\r\n    // add current font to field's resource dict (RD) if not the default acroform font\r\n    if (this._acroform.defaultFont !== this._font.name) {\r\n      options.DR = { Font: {} };\r\n\r\n      // Get the fontSize option. If not set use auto sizing\r\n      const fontSize = options.fontSize || 0;\r\n\r\n      options.DR.Font[this._font.id] = this._font.ref();\r\n      options.DA = new String(`/${this._font.id} ${fontSize} Tf 0 g`);\r\n    }\r\n    return options;\r\n  },\r\n\r\n  _resolveStrings(options) {\r\n    let select = [];\r\n    function appendChoices(a) {\r\n      if (Array.isArray(a)) {\r\n        for (let idx = 0; idx < a.length; idx++) {\r\n          if (typeof a[idx] === 'string') {\r\n            select.push(new String(a[idx]));\r\n          } else {\r\n            select.push(a[idx]);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    appendChoices(options.Opt);\r\n    if (options.select) {\r\n      appendChoices(options.select);\r\n      delete options.select;\r\n    }\r\n    if (select.length) {\r\n      options.Opt = select;\r\n    }\r\n\r\n    Object.keys(VALUE_MAP).forEach(key => {\r\n      if (options[key] !== undefined) {\r\n        options[VALUE_MAP[key]] = options[key];\r\n        delete options[key];\r\n      }\r\n    });\r\n    ['V', 'DV'].forEach(key => {\r\n      if (typeof options[key] === 'string') {\r\n        options[key] = new String(options[key]);\r\n      }\r\n    });\r\n\r\n    if (options.MK && options.MK.CA) {\r\n      options.MK.CA = new String(options.MK.CA);\r\n    }\r\n    if (options.label) {\r\n      options.MK = options.MK ? options.MK : {};\r\n      options.MK.CA = new String(options.label);\r\n      delete options.label;\r\n    }\r\n    return options;\r\n  }\r\n};\r\n", "import fs from 'fs';\r\nimport CryptoJS from 'crypto-js';\r\n\r\nexport default {\r\n  /**\r\n   * Embed contents of `src` in PDF\r\n   * @param {Buffer | ArrayBuffer | string} src input Buffer, ArrayBuffer, base64 encoded string or path to file\r\n   * @param {object} options\r\n   *  * options.name: filename to be shown in PDF, will use `src` if none set\r\n   *  * options.type: filetype to be shown in PDF\r\n   *  * options.description: description to be shown in PDF\r\n   *  * options.hidden: if true, do not add attachment to EmbeddedFiles dictionary. Useful for file attachment annotations\r\n   *  * options.creationDate: override creation date\r\n   *  * options.modifiedDate: override modified date\r\n   * @returns filespec reference\r\n   */\r\n  file(src, options = {}) {\r\n    options.name = options.name || src;\r\n\r\n    const refBody = {\r\n      Type: 'EmbeddedFile',\r\n      Params: {}\r\n    };\r\n    let data;\r\n\r\n    if (!src) {\r\n      throw new Error('No src specified');\r\n    }\r\n    if (Buffer.isBuffer(src)) {\r\n      data = src;\r\n    } else if (src instanceof ArrayBuffer) {\r\n      data = Buffer.from(new Uint8Array(src));\r\n    } else {\r\n      let match;\r\n      if ((match = /^data:(.*);base64,(.*)$/.exec(src))) {\r\n        if (match[1]) {\r\n          refBody.Subtype = match[1].replace('/', '#2F');\r\n        }\r\n        data = Buffer.from(match[2], 'base64');\r\n      } else {\r\n        data = fs.readFileSync(src);\r\n        if (!data) {\r\n          throw new Error(`Could not read contents of file at filepath ${src}`);\r\n        }\r\n\r\n        // update CreationDate and ModDate\r\n        const { birthtime, ctime } = fs.statSync(src);\r\n        refBody.Params.CreationDate = birthtime;\r\n        refBody.Params.ModDate = ctime;\r\n      }\r\n    }\r\n\r\n    // override creation date and modified date\r\n    if (options.creationDate instanceof Date) {\r\n      refBody.Params.CreationDate = options.creationDate;\r\n    }\r\n    if (options.modifiedDate instanceof Date) {\r\n      refBody.Params.ModDate = options.modifiedDate;\r\n    }\r\n    // add optional subtype\r\n    if (options.type) {\r\n      refBody.Subtype = options.type.replace('/', '#2F');\r\n    }\r\n\r\n    // add checksum and size information\r\n    const checksum = CryptoJS.MD5(\r\n      CryptoJS.lib.WordArray.create(new Uint8Array(data))\r\n    );\r\n    refBody.Params.CheckSum = new String(checksum);\r\n    refBody.Params.Size = data.byteLength;\r\n\r\n    // save some space when embedding the same file again\r\n    // if a file with the same name and metadata exists, reuse its reference\r\n    let ref;\r\n    if (!this._fileRegistry) this._fileRegistry = {};\r\n    let file = this._fileRegistry[options.name];\r\n    if (file && isEqual(refBody, file)) {\r\n      ref = file.ref;\r\n    } else {\r\n      ref = this.ref(refBody);\r\n      ref.end(data);\r\n\r\n      this._fileRegistry[options.name] = { ...refBody, ref };\r\n    }\r\n    // add filespec for embedded file\r\n    const fileSpecBody = {\r\n      Type: 'Filespec',\r\n      F: new String(options.name),\r\n      EF: { F: ref },\r\n      UF: new String(options.name)\r\n    };\r\n    if (options.description) {\r\n      fileSpecBody.Desc = new String(options.description);\r\n    }\r\n    const filespec = this.ref(fileSpecBody);\r\n    filespec.end();\r\n\r\n    if (!options.hidden) {\r\n      this.addNamedEmbeddedFile(options.name, filespec);\r\n    }\r\n\r\n    return filespec;\r\n  }\r\n};\r\n\r\n/** check two embedded file metadata objects for equality */\r\nfunction isEqual(a, b) {\r\n  return (\r\n    a.Subtype === b.Subtype &&\r\n    a.Params.CheckSum.toString() === b.Params.CheckSum.toString() &&\r\n    a.Params.Size === b.Params.Size &&\r\n    a.Params.CreationDate === b.Params.CreationDate &&\r\n    a.Params.ModDate === b.Params.ModDate\r\n  );\r\n}\r\n", "/*\r\nPDFDocument - represents an entire PDF document\r\nBy <PERSON>\r\n*/\r\n\r\nimport stream from 'stream';\r\nimport fs from 'fs';\r\nimport PDFObject from './object';\r\nimport PDFReference from './reference';\r\nimport PDFPage from './page';\r\nimport PDFNameTree from './name_tree';\r\nimport PDFSecurity from './security';\r\nimport ColorMixin from './mixins/color';\r\nimport VectorMixin from './mixins/vector';\r\nimport FontsMixin from './mixins/fonts';\r\nimport TextMixin from './mixins/text';\r\nimport ImagesMixin from './mixins/images';\r\nimport AnnotationsMixin from './mixins/annotations';\r\nimport OutlineMixin from './mixins/outline';\r\nimport MarkingsMixin from './mixins/markings';\r\nimport AcroFormMixin from './mixins/acroform';\r\nimport AttachmentsMixin from './mixins/attachments';\r\nimport LineWrapper from './line_wrapper';\r\n\r\nclass PDFDocument extends stream.Readable {\r\n  constructor(options = {}) {\r\n    super(options);\r\n    this.options = options;\r\n\r\n    // PDF version\r\n    switch (options.pdfVersion) {\r\n      case '1.4':\r\n        this.version = 1.4;\r\n        break;\r\n      case '1.5':\r\n        this.version = 1.5;\r\n        break;\r\n      case '1.6':\r\n        this.version = 1.6;\r\n        break;\r\n      case '1.7':\r\n      case '1.7ext3':\r\n        this.version = 1.7;\r\n        break;\r\n      default:\r\n        this.version = 1.3;\r\n        break;\r\n    }\r\n\r\n    // Whether streams should be compressed\r\n    this.compress =\r\n      this.options.compress != null ? this.options.compress : true;\r\n\r\n    this._pageBuffer = [];\r\n    this._pageBufferStart = 0;\r\n\r\n    // The PDF object store\r\n    this._offsets = [];\r\n    this._waiting = 0;\r\n    this._ended = false;\r\n    this._offset = 0;\r\n    const Pages = this.ref({\r\n      Type: 'Pages',\r\n      Count: 0,\r\n      Kids: []\r\n    });\r\n\r\n    const Names = this.ref({\r\n      Dests: new PDFNameTree()\r\n    });\r\n\r\n    this._root = this.ref({\r\n      Type: 'Catalog',\r\n      Pages,\r\n      Names\r\n    });\r\n\r\n    if (this.options.lang) {\r\n      this._root.data.Lang = new String(this.options.lang);\r\n    }\r\n\r\n    // The current page\r\n    this.page = null;\r\n\r\n    // Initialize mixins\r\n    this.initColor();\r\n    this.initVector();\r\n    this.initFonts(options.font);\r\n    this.initText();\r\n    this.initImages();\r\n    this.initOutline();\r\n    this.initMarkings(options);\r\n\r\n    // Initialize the metadata\r\n    this.info = {\r\n      Producer: 'PDFKit',\r\n      Creator: 'PDFKit',\r\n      CreationDate: new Date()\r\n    };\r\n\r\n    if (this.options.info) {\r\n      for (let key in this.options.info) {\r\n        const val = this.options.info[key];\r\n        this.info[key] = val;\r\n      }\r\n    }\r\n\r\n    if (this.options.displayTitle) {\r\n      this._root.data.ViewerPreferences = this.ref({\r\n        DisplayDocTitle: true\r\n      });\r\n    }\r\n\r\n    // Generate file ID\r\n    this._id = PDFSecurity.generateFileID(this.info);\r\n\r\n    // Initialize security settings\r\n    this._security = PDFSecurity.create(this, options);\r\n\r\n    // Write the header\r\n    // PDF version\r\n    this._write(`%PDF-${this.version}`);\r\n\r\n    // 4 binary chars, as recommended by the spec\r\n    this._write('%\\xFF\\xFF\\xFF\\xFF');\r\n\r\n    // Add the first page\r\n    if (this.options.autoFirstPage !== false) {\r\n      this.addPage();\r\n    }\r\n  }\r\n\r\n  addPage(options) {\r\n    if (options == null) {\r\n      ({ options } = this);\r\n    }\r\n\r\n    // end the current page if needed\r\n    if (!this.options.bufferPages) {\r\n      this.flushPages();\r\n    }\r\n\r\n    // create a page object\r\n    this.page = new PDFPage(this, options);\r\n    this._pageBuffer.push(this.page);\r\n\r\n    // add the page to the object store\r\n    const pages = this._root.data.Pages.data;\r\n    pages.Kids.push(this.page.dictionary);\r\n    pages.Count++;\r\n\r\n    // reset x and y coordinates\r\n    this.x = this.page.margins.left;\r\n    this.y = this.page.margins.top;\r\n\r\n    // flip PDF coordinate system so that the origin is in\r\n    // the top left rather than the bottom left\r\n    this._ctm = [1, 0, 0, 1, 0, 0];\r\n    this.transform(1, 0, 0, -1, 0, this.page.height);\r\n\r\n    this.emit('pageAdded');\r\n\r\n    return this;\r\n  }\r\n\r\n  continueOnNewPage(options) {\r\n    const pageMarkings = this.endPageMarkings(this.page);\r\n\r\n    this.addPage(options);\r\n\r\n    this.initPageMarkings(pageMarkings);\r\n\r\n    return this;\r\n  }\r\n\r\n  bufferedPageRange() {\r\n    return { start: this._pageBufferStart, count: this._pageBuffer.length };\r\n  }\r\n\r\n  switchToPage(n) {\r\n    let page;\r\n    if (!(page = this._pageBuffer[n - this._pageBufferStart])) {\r\n      throw new Error(\r\n        `switchToPage(${n}) out of bounds, current buffer covers pages ${\r\n          this._pageBufferStart\r\n        } to ${this._pageBufferStart + this._pageBuffer.length - 1}`\r\n      );\r\n    }\r\n\r\n    return (this.page = page);\r\n  }\r\n\r\n  flushPages() {\r\n    // this local variable exists so we're future-proof against\r\n    // reentrant calls to flushPages.\r\n    const pages = this._pageBuffer;\r\n    this._pageBuffer = [];\r\n    this._pageBufferStart += pages.length;\r\n    for (let page of pages) {\r\n      this.endPageMarkings(page);\r\n      page.end();\r\n    }\r\n  }\r\n\r\n  addNamedDestination(name, ...args) {\r\n    if (args.length === 0) {\r\n      args = ['XYZ', null, null, null];\r\n    }\r\n    if (args[0] === 'XYZ' && args[2] !== null) {\r\n      args[2] = this.page.height - args[2];\r\n    }\r\n    args.unshift(this.page.dictionary);\r\n    this._root.data.Names.data.Dests.add(name, args);\r\n  }\r\n\r\n  addNamedEmbeddedFile(name, ref) {\r\n    if (!this._root.data.Names.data.EmbeddedFiles) {\r\n      // disabling /Limits for this tree fixes attachments not showing in Adobe Reader\r\n      this._root.data.Names.data.EmbeddedFiles = new PDFNameTree({ limits: false });\r\n    }\r\n\r\n    // add filespec to EmbeddedFiles\r\n    this._root.data.Names.data.EmbeddedFiles.add(name, ref);\r\n  }\r\n\r\n  addNamedJavaScript(name, js) {\r\n    if (!this._root.data.Names.data.JavaScript) {\r\n      this._root.data.Names.data.JavaScript = new PDFNameTree();\r\n    }\r\n    let data = {\r\n      JS: new String(js),\r\n      S: 'JavaScript'\r\n    };\r\n    this._root.data.Names.data.JavaScript.add(name, data);\r\n  }\r\n\r\n  ref(data) {\r\n    const ref = new PDFReference(this, this._offsets.length + 1, data);\r\n    this._offsets.push(null); // placeholder for this object's offset once it is finalized\r\n    this._waiting++;\r\n    return ref;\r\n  }\r\n\r\n  _read() {}\r\n  // do nothing, but this method is required by node\r\n\r\n  _write(data) {\r\n    if (!Buffer.isBuffer(data)) {\r\n      data = Buffer.from(data + '\\n', 'binary');\r\n    }\r\n\r\n    this.push(data);\r\n    return (this._offset += data.length);\r\n  }\r\n\r\n  addContent(data) {\r\n    this.page.write(data);\r\n    return this;\r\n  }\r\n\r\n  _refEnd(ref) {\r\n    this._offsets[ref.id - 1] = ref.offset;\r\n    if (--this._waiting === 0 && this._ended) {\r\n      this._finalize();\r\n      return (this._ended = false);\r\n    }\r\n  }\r\n\r\n  write(filename, fn) {\r\n    // print a deprecation warning with a stacktrace\r\n    const err = new Error(`\\\r\nPDFDocument#write is deprecated, and will be removed in a future version of PDFKit. \\\r\nPlease pipe the document into a Node stream.\\\r\n`);\r\n\r\n    console.warn(err.stack);\r\n\r\n    this.pipe(fs.createWriteStream(filename));\r\n    this.end();\r\n    return this.once('end', fn);\r\n  }\r\n\r\n  end() {\r\n    this.flushPages();\r\n    this._info = this.ref();\r\n    for (let key in this.info) {\r\n      let val = this.info[key];\r\n      if (typeof val === 'string') {\r\n        val = new String(val);\r\n      }\r\n\r\n      let entry = this.ref(val);\r\n      entry.end();\r\n\r\n      this._info.data[key] = entry;\r\n    }\r\n\r\n    this._info.end();\r\n\r\n    for (let name in this._fontFamilies) {\r\n      const font = this._fontFamilies[name];\r\n      font.finalize();\r\n    }\r\n\r\n    this.endOutline();\r\n    this.endMarkings();\r\n\r\n    this._root.end();\r\n    this._root.data.Pages.end();\r\n    this._root.data.Names.end();\r\n    this.endAcroForm();\r\n\r\n    if (this._root.data.ViewerPreferences) {\r\n      this._root.data.ViewerPreferences.end();\r\n    }\r\n\r\n    if (this._security) {\r\n      this._security.end();\r\n    }\r\n\r\n    if (this._waiting === 0) {\r\n      return this._finalize();\r\n    } else {\r\n      return (this._ended = true);\r\n    }\r\n  }\r\n\r\n  _finalize() {\r\n    // generate xref\r\n    const xRefOffset = this._offset;\r\n    this._write('xref');\r\n    this._write(`0 ${this._offsets.length + 1}`);\r\n    this._write('0000000000 65535 f ');\r\n\r\n    for (let offset of this._offsets) {\r\n      offset = `0000000000${offset}`.slice(-10);\r\n      this._write(offset + ' 00000 n ');\r\n    }\r\n\r\n    // trailer\r\n    const trailer = {\r\n      Size: this._offsets.length + 1,\r\n      Root: this._root,\r\n      Info: this._info,\r\n      ID: [this._id, this._id]\r\n    };\r\n    if (this._security) {\r\n      trailer.Encrypt = this._security.dictionary;\r\n    }\r\n\r\n    this._write('trailer');\r\n    this._write(PDFObject.convert(trailer));\r\n\r\n    this._write('startxref');\r\n    this._write(`${xRefOffset}`);\r\n    this._write('%%EOF');\r\n\r\n    // end the stream\r\n    return this.push(null);\r\n  }\r\n\r\n  toString() {\r\n    return '[object PDFDocument]';\r\n  }\r\n}\r\n\r\nconst mixin = methods => {\r\n  Object.assign(PDFDocument.prototype, methods);\r\n};\r\n\r\nmixin(ColorMixin);\r\nmixin(VectorMixin);\r\nmixin(FontsMixin);\r\nmixin(TextMixin);\r\nmixin(ImagesMixin);\r\nmixin(AnnotationsMixin);\r\nmixin(OutlineMixin);\r\nmixin(MarkingsMixin);\r\nmixin(AcroFormMixin);\r\nmixin(AttachmentsMixin);\r\n\r\nPDFDocument.LineWrapper = LineWrapper;\r\n\r\nexport default PDFDocument;\r\n"], "names": ["PDFAbstractReference", "toString", "Error", "PDFTree", "constructor", "options", "_items", "limits", "add", "key", "val", "get", "sortedKeys", "Object", "keys", "sort", "a", "b", "_compareKeys", "out", "length", "first", "last", "push", "PDFObject", "convert", "_dataForKey", "_keysName", "join", "pad", "str", "Array", "slice", "escapableRe", "escapable", "swapBytes", "buff", "l", "i", "end", "object", "encryptFn", "String", "string", "isUnicode", "charCodeAt", "stringBuffer", "<PERSON><PERSON><PERSON>", "from", "valueOf", "replace", "c", "<PERSON><PERSON><PERSON><PERSON>", "Date", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "isArray", "items", "map", "e", "call", "number", "n", "Math", "round", "PDFReference", "document", "id", "data", "gen", "compress", "Filter", "uncompressedLength", "buffer", "write", "chunk", "Length", "finalize", "offset", "_offset", "_security", "getEncryptFn", "concat", "zlib", "deflateSync", "_write", "_refEnd", "DEFAULT_MARGINS", "top", "left", "bottom", "right", "SIZES", "A0", "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "A10", "B0", "B1", "B2", "B3", "B4", "B5", "B6", "B7", "B8", "B9", "B10", "C0", "C1", "C2", "C3", "C4", "C5", "C6", "C7", "C8", "C9", "C10", "RA0", "RA1", "RA2", "RA3", "RA4", "SRA0", "SRA1", "SRA2", "SRA3", "SRA4", "EXECUTIVE", "FOLIO", "LEGAL", "LETTER", "TABLOID", "PDFPage", "size", "layout", "margin", "margins", "dimensions", "toUpperCase", "width", "height", "content", "ref", "resources", "ProcSet", "dictionary", "Type", "Parent", "_root", "Pages", "MediaBox", "Contents", "Resources", "markings", "fonts", "Font", "xobjects", "XObject", "ext_gstates", "ExtGState", "patterns", "Pattern", "annotations", "Ann<PERSON>", "structParentTreeKey", "StructParents", "createStructParentTreeNextKey", "maxY", "PDFNameTree", "localeCompare", "k", "inRange", "value", "rangeGroup", "startRange", "endRange", "<PERSON><PERSON><PERSON><PERSON>", "floor", "arrayIndex", "unassigned_code_points", "isUnassignedCodePoint", "character", "commonly_mapped_to_nothing", "isCommonlyMappedToNothing", "non_ASCII_space_characters", "isNonASCIISpaceCharacter", "non_ASCII_controls_characters", "non_character_codepoints", "prohibited_characters", "isProhibitedCharacter", "bidirectional_r_al", "isBidirectionalRAL", "bidirectional_l", "isBidirectionalL", "mapping2space", "mapping2nothing", "getCodePoint", "codePointAt", "x", "toCodePoints", "input", "codepoints", "before", "next", "saslprep", "opts", "TypeError", "mapped_input", "filter", "normalized_input", "fromCodePoint", "apply", "normalize", "normalized_map", "hasProhibited", "some", "allowUnassigned", "hasUnassigned", "hasBidiRAL", "hasBidiL", "isFirstBidiRAL", "isLastBidiRAL", "PDFSecurity", "generateFileID", "info", "infoStr", "CreationDate", "getTime", "hasOwnProperty", "wordArrayToBuffer", "CryptoJS", "MD5", "generateRandomWordArray", "bytes", "lib", "WordArray", "random", "create", "ownerPassword", "userPassword", "_setupEncryption", "pdfVersion", "version", "encDict", "_setupEncryptionV1V2V4", "_setupEncryptionV5", "v", "r", "permissions", "keyBits", "getPermissionsR2", "getPermissionsR3", "paddedUserPassword", "processPasswordR2R3R4", "paddedOwnerPassword", "ownerPasswordEntry", "getOwnerPasswordR2R3R4", "<PERSON><PERSON><PERSON>", "getEncryptionKeyR2R3R4", "_id", "userPasswordEntry", "getUserPasswordR2", "getUserPasswordR3R4", "V", "CF", "StdCF", "AuthEvent", "CFM", "StmF", "StrF", "R", "O", "U", "P", "processedUserPassword", "processPasswordR5", "processedOwnerPassword", "getEncryptionKeyR5", "getUserPasswordR5", "userKeySalt", "words", "userEncryptionKeyEntry", "getUserEncryptionKeyR5", "getOwnerPasswordR5", "ownerKeySalt", "ownerEncryptionKeyEntry", "getOwnerEncryptionKeyR5", "permsEntry", "getEncryptedPermissionsR5", "OE", "UE", "Perms", "obj", "digest", "clone", "sigBytes", "min", "RC4", "encrypt", "ciphertext", "iv", "mode", "CBC", "padding", "Pkcs7", "AES", "permissionObject", "printing", "modifying", "copying", "annotating", "fillingForms", "contentAccessibility", "documentAssembly", "documentId", "cipher", "xorRound", "ceil", "j", "lsbFirstWord", "validationSalt", "keySalt", "SHA256", "NoPadding", "ECB", "password", "alloc", "index", "code", "PASSWORD_PADDING", "unescape", "encodeURIComponent", "wordArray", "byteArray", "PDFGradient", "doc", "stops", "embedded", "transform", "stop", "pos", "color", "opacity", "_normalizeColor", "_colorSpace", "max", "setTransform", "m11", "m12", "m21", "m22", "dx", "dy", "embed", "m", "fn", "<PERSON><PERSON><PERSON><PERSON>", "matrix", "bounds", "encode", "FunctionType", "Domain", "N", "Functions", "Bounds", "Encode", "_gradCount", "shader", "pattern", "PatternType", "Shading", "Matrix", "grad", "opacityGradient", "pageBBox", "page", "form", "Subtype", "FormType", "BBox", "Group", "S", "CS", "Sh1", "gstate", "SMask", "G", "opacityPattern", "PaintType", "TilingType", "XStep", "YStep", "Gs1", "op", "m0", "m1", "m2", "m3", "m4", "m5", "_ctm", "addContent", "PDFLinearGradient", "x1", "y1", "x2", "y2", "ShadingType", "ColorSpace", "<PERSON><PERSON><PERSON>", "Function", "Extend", "PDFRadialGradient", "r1", "r2", "Gradient", "initColor", "_opacityRegistry", "_opacityCount", "char<PERSON>t", "hex", "parseInt", "namedColors", "part", "_setColor", "stroke", "_setColorSpace", "space", "fillColor", "set", "fillOpacity", "_fillColor", "strokeColor", "strokeOpacity", "_doOpacity", "name", "ca", "CA", "linearGradient", "radialGradient", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "grey", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "cx", "cy", "px", "py", "sx", "sy", "parameters", "A", "C", "H", "h", "L", "M", "Q", "q", "s", "T", "t", "Z", "z", "parse", "path", "cmd", "ret", "args", "curArg", "foundDecimal", "params", "includes", "commands", "runners", "moveTo", "bezierCurveTo", "quadraticCurveTo", "solveArc", "lineTo", "closePath", "y", "coords", "rx", "ry", "rot", "large", "sweep", "ex", "ey", "segs", "arcToSegments", "seg", "bez", "segmentToBezier", "rotateX", "ox", "oy", "th", "PI", "sin_th", "sin", "cos_th", "cos", "abs", "pl", "sqrt", "a00", "a01", "a10", "a11", "x0", "y0", "d", "sfactor_sq", "sfactor", "xc", "yc", "th0", "atan2", "th1", "th_arc", "segments", "result", "th2", "th3", "th_half", "x3", "y3", "SVGPath", "KAPPA", "initVector", "_ctmStack", "save", "restore", "pop", "lineWidth", "w", "_CAP_STYLES", "BUTT", "ROUND", "SQUARE", "lineCap", "_JOIN_STYLES", "MITER", "BEVEL", "lineJoin", "miterLimit", "dash", "original<PERSON>ength", "valid", "every", "Number", "isFinite", "JSON", "stringify", "phase", "undash", "cp1x", "cp1y", "cp2x", "cp2y", "cpx", "cpy", "rect", "roundedRect", "ellipse", "xe", "ye", "xm", "ym", "circle", "radius", "arc", "startAngle", "endAngle", "anticlockwise", "TWO_PI", "HALF_PI", "deltaAng", "dir", "numSegs", "segAng", "handleLen", "curAng", "deltaCx", "deltaCy", "ax", "ay", "segIdx", "polygon", "points", "shift", "point", "_windingRule", "rule", "test", "fill", "fillAndStroke", "isFillRule", "clip", "values", "translate", "rotate", "angle", "rad", "origin", "scale", "xFactor", "yFactor", "WIN_ANSI_MAP", "characters", "split", "AFMFont", "open", "filename", "fs", "readFileSync", "contents", "attributes", "glyphWidths", "boundingBoxes", "kernPairs", "char<PERSON><PERSON><PERSON>", "char", "bbox", "ascender", "descender", "xHeight", "capHeight", "lineGap", "section", "line", "match", "encodeText", "text", "res", "len", "glyphsForString", "glyphs", "charCode", "characterToGlyph", "widthOfGlyph", "glyph", "getKernPair", "advancesForGlyphs", "advances", "PDFFont", "widthOfString", "lineHeight", "includeGap", "gap", "STANDARD_FONTS", "Courier", "__dirname", "Helvetica", "Symbol", "ZapfDingbats", "StandardFont", "font", "BaseFont", "Encoding", "encoded", "positions", "xAdvance", "yAdvance", "xOffset", "yOffset", "advanceWidth", "advance", "isStandardFont", "toHex", "num", "EmbeddedFont", "subset", "createSubset", "unicode", "widths", "getGlyph", "postscriptName", "unitsPerEm", "ascent", "descent", "fontLayoutCache", "layoutCache", "layoutRun", "features", "run", "position", "layoutCached", "cached", "only<PERSON><PERSON><PERSON>", "needle", "gid", "includeGlyph", "codePoints", "isCFF", "cff", "fontFile", "encodeStream", "on", "familyClass", "sFamilyClass", "undefined", "flags", "post", "isFixedPitch", "head", "macStyle", "italic", "tag", "fromCharCode", "descriptor", "FontName", "Flags", "FontBBox", "minX", "minY", "maxX", "ItalicAngle", "italicAngle", "Ascent", "Descent", "CapHeight", "XHeight", "StemV", "FontFile3", "FontFile2", "descendantFontData", "CIDSystemInfo", "Registry", "Ordering", "Supplement", "FontDescriptor", "W", "CIDToGIDMap", "descendantFont", "DescendantFonts", "ToUnicode", "toUnicodeCmap", "cmap", "entries", "PDFFontFactory", "src", "family", "fontkit", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initFonts", "defaultFont", "_fontFamilies", "_fontCount", "_fontSize", "_font", "_registeredFonts", "cache<PERSON>ey", "fontSize", "currentLineHeight", "registerFont", "LineWrapper", "EventEmitter", "indent", "characterSpacing", "wordSpacing", "columns", "columnGap", "spaceLeft", "startX", "startY", "column", "ellipsis", "continuedX", "once", "continued", "align", "lastLine", "paragraphGap", "wordWidth", "word", "eachWord", "bk", "breaker", "LineBreaker", "wordWidths", "nextBreak", "shouldC<PERSON><PERSON>ue", "lbk", "fbk", "<PERSON><PERSON>row", "mustShrink", "required", "wrap", "nextY", "nextSection", "textWidth", "wc", "lc", "emitLine", "wordCount", "emit", "lh", "continueOnNewPage", "initText", "_line", "bind", "_lineGap", "moveDown", "lines", "moveUp", "_text", "lineCallback", "_initOptions", "addStructure", "structParent", "struct", "structType", "mark<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "wrapper", "_wrapper", "_textOptions", "heightOfString", "Infinity", "list", "listType", "unit", "midLine", "bulletRadius", "textIndent", "itemIndent", "bulletIndent", "level", "levels", "numbers", "flatten", "item", "label", "letter", "times", "itemType", "labelType", "bodyType", "structTypes", "diff", "_fragment", "assign", "lineBreak", "trim", "spaceWidth", "baseline", "rendered<PERSON><PERSON><PERSON>", "link", "goTo", "destination", "addNamedDestination", "underline", "lineY", "strike", "oblique", "skew", "encodedWord", "positionsWord", "hadOffset", "addSegment", "cur", "flush", "MARKERS", "COLOR_SPACE_MAP", "JPEG", "marker", "readUInt16BE", "bits", "channels", "colorSpace", "BitsPerComponent", "<PERSON><PERSON><PERSON>", "Height", "PNGImage", "image", "PNG", "imgData", "dataDecoded", "hasAlphaChannel", "isInterlaced", "interlace<PERSON>ethod", "Predictor", "Colors", "colors", "Columns", "palette", "transparency", "grayscale", "rgb", "mask", "indexed", "loadIndexedAlphaChannel", "splitAlphaChannel", "decodeData", "alphaChannel", "sMask", "Decode", "decodePixels", "pixels", "p", "colorCount", "pixelCount", "skipByteCount", "colorIndex", "PDFImage", "exec", "initImages", "_imageRegistry", "_imageCount", "bh", "bp", "bw", "ip", "left1", "openImage", "wp", "hp", "fit", "cover", "valign", "annotate", "Rect", "_convertRect", "Border", "F", "Dest", "note", "Name", "D", "url", "pages", "Kids", "URI", "_markup", "QuadPoints", "highlight", "lineAnnotation", "rectAnnotation", "ellipseAnnotation", "textAnnotation", "DA", "fileAnnotation", "file", "filespec", "hidden", "FS", "Desc", "PDFOutline", "parent", "title", "dest", "expanded", "outlineData", "children", "addItem", "endOutline", "Count", "First", "Last", "child", "Prev", "Next", "initOutline", "outline", "Outlines", "PageMode", "PDFStructureContent", "pageRef", "mcid", "refs", "structContent", "for<PERSON>ach", "PDFStructureElement", "type", "_attached", "_ended", "_flushed", "_is<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "lang", "<PERSON>", "alt", "Alt", "E", "actual", "ActualText", "_children", "setParent", "setAttached", "_addContentToParentTree", "_contentForClosure", "pageStructParents", "getStructParentTree", "parentRef", "_flush", "closure", "endMarkedContent", "_is<PERSON><PERSON>hable", "K", "_<PERSON><PERSON><PERSON>d", "Pg", "MCID", "PDFNumberTree", "initMarkings", "struct<PERSON><PERSON><PERSON><PERSON>", "tagged", "getMarkInfoDictionary", "Marked", "getStructTreeRoot", "<PERSON><PERSON><PERSON><PERSON>", "toClose", "marking", "attached", "Attached", "structElem", "structTreeRoot", "initPageMarkings", "pageMarkings", "newStructContent", "endPageMarkings", "MarkInfo", "StructTreeRoot", "<PERSON><PERSON><PERSON><PERSON>", "ParentTreeNextKey", "endMarkings", "FIELD_FLAGS", "readOnly", "noExport", "multiline", "toggleToOffButton", "radioButton", "pushButton", "combo", "edit", "multiSelect", "noSpell", "FIELD_JUSTIFY", "center", "VALUE_MAP", "defaultValue", "FORMAT_SPECIAL", "zip", "zipPlus4", "zip4", "phone", "ssn", "FORMAT_DEFAULT", "nDec", "sepComma", "negStyle", "currency", "currencyPrepend", "percent", "initForm", "_acroform", "Fields", "NeedAppearances", "DR", "AcroForm", "endAcroForm", "fontDict", "fieldRef", "_endChild", "childRef", "formField", "fieldDict", "_fieldDict", "_addToParent", "formAnnotation", "annotRef", "formText", "formPushButton", "formCombo", "formList", "formRadioButton", "formCheckbox", "_resolveType", "_resolveFlags", "_resolveJustify", "_resolveFont", "_resolveStrings", "_resolveColors", "_resolveFormat", "FT", "f", "format", "fnKeystroke", "fnFormat", "param", "AA", "JS", "backgroundColor", "MK", "BG", "borderColor", "BC", "Ff", "select", "appendChoices", "idx", "<PERSON><PERSON>", "refBody", "Params", "birthtime", "ctime", "statSync", "ModDate", "creationDate", "modifiedDate", "checksum", "CheckSum", "Size", "byteLength", "_fileRegistry", "isEqual", "fileSpecBody", "EF", "UF", "description", "addNamedEmbeddedFile", "PDFDocument", "stream", "Readable", "_pageBuffer", "_pageBufferStart", "_offsets", "_waiting", "Names", "Des<PERSON>", "Producer", "Creator", "displayTitle", "ViewerPreferences", "DisplayDocTitle", "autoFirstPage", "addPage", "bufferPages", "flushPages", "bufferedPageRange", "start", "count", "switchToPage", "unshift", "EmbeddedFiles", "addNamedJavaScript", "js", "JavaScript", "_read", "_finalize", "err", "console", "warn", "stack", "pipe", "createWriteStream", "_info", "entry", "xRefOffset", "trailer", "Root", "Info", "ID", "Encrypt", "mixin", "methods", "prototype", "ColorMixin", "VectorMixin", "FontsMixin", "TextMixin", "ImagesMixin", "AnnotationsMixin", "OutlineMixin", "MarkingsMixin", "AcroFormMixin", "AttachmentsMixin"], "mappings": ";;;;;;;;;;;AAAA;;;AAIA,MAAMA,oBAAN,CAA2B;EACzBC,QAAQ,GAAG;UACH,IAAIC,KAAJ,CAAU,mCAAV,CAAN;;;;;ACNJ;;;AAIA;AAEA,MAAMC,OAAN,CAAc;EACZC,WAAW,CAACC,OAAO,GAAG,EAAX,EAAe;SACnBC,MAAL,GAAc,EAAd,CADwB;;SAGnBC,MAAL,GACE,OAAOF,OAAO,CAACE,MAAf,KAA0B,SAA1B,GAAsCF,OAAO,CAACE,MAA9C,GAAuD,IADzD;;;EAIFC,GAAG,CAACC,GAAD,EAAMC,GAAN,EAAW;WACJ,KAAKJ,MAAL,CAAYG,GAAZ,IAAmBC,GAA3B;;;EAGFC,GAAG,CAACF,GAAD,EAAM;WACA,KAAKH,MAAL,CAAYG,GAAZ,CAAP;;;EAGFR,QAAQ,GAAG;;UAEHW,UAAU,GAAGC,MAAM,CAACC,IAAP,CAAY,KAAKR,MAAjB,EAAyBS,IAAzB,CAA8B,CAACC,CAAD,EAAIC,CAAJ,KAC/C,KAAKC,YAAL,CAAkBF,CAAlB,EAAqBC,CAArB,CADiB,CAAnB;UAIME,GAAG,GAAG,CAAC,IAAD,CAAZ;;QACI,KAAKZ,MAAL,IAAeK,UAAU,CAACQ,MAAX,GAAoB,CAAvC,EAA0C;YAClCC,KAAK,GAAGT,UAAU,CAAC,CAAD,CAAxB;YACEU,IAAI,GAAGV,UAAU,CAACA,UAAU,CAACQ,MAAX,GAAoB,CAArB,CADnB;MAEAD,GAAG,CAACI,IAAJ,CACG,aAAYC,SAAS,CAACC,OAAV,CAAkB,CAAC,KAAKC,WAAL,CAAiBL,KAAjB,CAAD,EAA0B,KAAKK,WAAL,CAAiBJ,IAAjB,CAA1B,CAAlB,CAAqE,EADpF;;;IAIFH,GAAG,CAACI,IAAJ,CAAU,MAAK,KAAKI,SAAL,EAAiB,IAAhC;;SACK,IAAIlB,GAAT,IAAgBG,UAAhB,EAA4B;MAC1BO,GAAG,CAACI,IAAJ,CACG,OAAMC,SAAS,CAACC,OAAV,CAAkB,KAAKC,WAAL,CAAiBjB,GAAjB,CAAlB,CAAyC,IAAGe,SAAS,CAACC,OAAV,CACjD,KAAKnB,MAAL,CAAYG,GAAZ,CADiD,CAEjD,EAHJ;;;IAMFU,GAAG,CAACI,IAAJ,CAAS,GAAT;IACAJ,GAAG,CAACI,IAAJ,CAAS,IAAT;WACOJ,GAAG,CAACS,IAAJ,CAAS,IAAT,CAAP;;;EAGFV,YAAY;;;UACJ,IAAIhB,KAAJ,CAAU,mCAAV,CAAN;;;EAGFyB,SAAS,GAAG;UACJ,IAAIzB,KAAJ,CAAU,mCAAV,CAAN;;;EAGFwB,WAAW;;;UACH,IAAIxB,KAAJ,CAAU,mCAAV,CAAN;;;;;AC1DJ;;;;AAKA;AAGA,MAAM2B,GAAG,GAAG,CAACC,GAAD,EAAMV,MAAN,KAAiB,CAACW,KAAK,CAACX,MAAM,GAAG,CAAV,CAAL,CAAkBQ,IAAlB,CAAuB,GAAvB,IAA8BE,GAA/B,EAAoCE,KAApC,CAA0C,CAACZ,MAA3C,CAA7B;;AAEA,MAAMa,WAAW,GAAG,mBAApB;AACA,MAAMC,SAAS,GAAG;QACV,KADU;QAEV,KAFU;QAGV,KAHU;QAIV,KAJU;QAKV,KALU;QAMV,MANU;OAOX,KAPW;OAQX;CARP;;AAYA,MAAMC,SAAS,GAAG,UAASC,IAAT,EAAe;QACzBC,CAAC,GAAGD,IAAI,CAAChB,MAAf;;MACIiB,CAAC,GAAG,IAAR,EAAc;UACN,IAAInC,KAAJ,CAAU,4BAAV,CAAN;GADF,MAEO;SACA,IAAIoC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,CAAC,GAAG,CAA1B,EAA6BC,CAAC,GAAGC,GAAjC,EAAsCD,CAAC,IAAI,CAA3C,EAA8C;YACtCtB,CAAC,GAAGoB,IAAI,CAACE,CAAD,CAAd;MACAF,IAAI,CAACE,CAAD,CAAJ,GAAUF,IAAI,CAACE,CAAC,GAAG,CAAL,CAAd;MACAF,IAAI,CAACE,CAAC,GAAG,CAAL,CAAJ,GAActB,CAAd;;;;SAIGoB,IAAP;CAZF;;AAeA,MAAMZ,SAAN,CAAgB;SACPC,OAAP,CAAee,MAAf,EAAuBC,SAAS,GAAG,IAAnC,EAAyC;;QAEnC,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;aACtB,IAAGA,MAAO,EAAlB,CAD8B;KAAhC,MAIO,IAAIA,MAAM,YAAYE,MAAtB,EAA8B;UAC/BC,MAAM,GAAGH,MAAb,CADmC;;UAG/BI,SAAS,GAAG,KAAhB;;WACK,IAAIN,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGI,MAAM,CAACvB,MAA7B,EAAqCkB,CAAC,GAAGC,GAAzC,EAA8CD,CAAC,EAA/C,EAAmD;YAC7CK,MAAM,CAACE,UAAP,CAAkBP,CAAlB,IAAuB,IAA3B,EAAiC;UAC/BM,SAAS,GAAG,IAAZ;;;OAN+B;;;UAY/BE,YAAJ;;UACIF,SAAJ,EAAe;QACbE,YAAY,GAAGX,SAAS,CAACY,MAAM,CAACC,IAAP,CAAa,SAAQL,MAAO,EAA5B,EAA+B,SAA/B,CAAD,CAAxB;OADF,MAEO;QACLG,YAAY,GAAGC,MAAM,CAACC,IAAP,CAAYL,MAAM,CAACM,OAAP,EAAZ,EAA8B,OAA9B,CAAf;OAhBiC;;;UAoB/BR,SAAJ,EAAe;QACbE,MAAM,GAAGF,SAAS,CAACK,YAAD,CAAT,CAAwB7C,QAAxB,CAAiC,QAAjC,CAAT;OADF,MAEO;QACL0C,MAAM,GAAGG,YAAY,CAAC7C,QAAb,CAAsB,QAAtB,CAAT;OAvBiC;;;MA2BnC0C,MAAM,GAAGA,MAAM,CAACO,OAAP,CAAejB,WAAf,EAA4BkB,CAAC,IAAIjB,SAAS,CAACiB,CAAD,CAA1C,CAAT;aAEQ,IAAGR,MAAO,GAAlB,CA7BmC;KAA9B,MAgCA,IAAII,MAAM,CAACK,QAAP,CAAgBZ,MAAhB,CAAJ,EAA6B;aAC1B,IAAGA,MAAM,CAACvC,QAAP,CAAgB,KAAhB,CAAuB,GAAlC;KADK,MAEA,IACLuC,MAAM,YAAYxC,oBAAlB,IACAwC,MAAM,YAAYrC,OAFb,EAGL;aACOqC,MAAM,CAACvC,QAAP,EAAP;KAJK,MAKA,IAAIuC,MAAM,YAAYa,IAAtB,EAA4B;UAC7BV,MAAM,GACP,KAAId,GAAG,CAACW,MAAM,CAACc,cAAP,EAAD,EAA0B,CAA1B,CAA6B,EAArC,GACAzB,GAAG,CAACW,MAAM,CAACe,WAAP,KAAuB,CAAxB,EAA2B,CAA3B,CADH,GAEA1B,GAAG,CAACW,MAAM,CAACgB,UAAP,EAAD,EAAsB,CAAtB,CAFH,GAGA3B,GAAG,CAACW,MAAM,CAACiB,WAAP,EAAD,EAAuB,CAAvB,CAHH,GAIA5B,GAAG,CAACW,MAAM,CAACkB,aAAP,EAAD,EAAyB,CAAzB,CAJH,GAKA7B,GAAG,CAACW,MAAM,CAACmB,aAAP,EAAD,EAAyB,CAAzB,CALH,GAMA,GAPF,CADiC;;UAW7BlB,SAAJ,EAAe;QACbE,MAAM,GAAGF,SAAS,CAACM,MAAM,CAACC,IAAP,CAAYL,MAAZ,EAAoB,OAApB,CAAD,CAAT,CAAwC1C,QAAxC,CAAiD,QAAjD,CAAT,CADa;;QAIb0C,MAAM,GAAGA,MAAM,CAACO,OAAP,CAAejB,WAAf,EAA4BkB,CAAC,IAAIjB,SAAS,CAACiB,CAAD,CAA1C,CAAT;;;aAGM,IAAGR,MAAO,GAAlB;KAlBK,MAmBA,IAAIZ,KAAK,CAAC6B,OAAN,CAAcpB,MAAd,CAAJ,EAA2B;YAC1BqB,KAAK,GAAGrB,MAAM,CAACsB,GAAP,CAAWC,CAAC,IAAIvC,SAAS,CAACC,OAAV,CAAkBsC,CAAlB,EAAqBtB,SAArB,CAAhB,EAAiDb,IAAjD,CAAsD,GAAtD,CAAd;aACQ,IAAGiC,KAAM,GAAjB;KAFK,MAGA,IAAI,GAAG5D,QAAH,CAAY+D,IAAZ,CAAiBxB,MAAjB,MAA6B,iBAAjC,EAAoD;YACnDrB,GAAG,GAAG,CAAC,IAAD,CAAZ;;WACK,IAAIV,GAAT,IAAgB+B,MAAhB,EAAwB;cAChB9B,GAAG,GAAG8B,MAAM,CAAC/B,GAAD,CAAlB;QACAU,GAAG,CAACI,IAAJ,CAAU,IAAGd,GAAI,IAAGe,SAAS,CAACC,OAAV,CAAkBf,GAAlB,EAAuB+B,SAAvB,CAAkC,EAAtD;;;MAGFtB,GAAG,CAACI,IAAJ,CAAS,IAAT;aACOJ,GAAG,CAACS,IAAJ,CAAS,IAAT,CAAP;KARK,MASA,IAAI,OAAOY,MAAP,KAAkB,QAAtB,EAAgC;aAC9BhB,SAAS,CAACyC,MAAV,CAAiBzB,MAAjB,CAAP;KADK,MAEA;aACG,GAAEA,MAAO,EAAjB;;;;SAIGyB,MAAP,CAAcC,CAAd,EAAiB;QACXA,CAAC,GAAG,CAAC,IAAL,IAAaA,CAAC,GAAG,IAArB,EAA2B;aAClBC,IAAI,CAACC,KAAL,CAAWF,CAAC,GAAG,GAAf,IAAsB,GAA7B;;;UAGI,IAAIhE,KAAJ,CAAW,uBAAsBgE,CAAE,EAAnC,CAAN;;;;;AC/HJ;;;;AAKA;AAIA,MAAMG,YAAN,SAA2BrE,oBAA3B,CAAgD;EAC9CI,WAAW,CAACkE,QAAD,EAAWC,EAAX,EAAeC,IAAI,GAAG,EAAtB,EAA0B;;SAE9BF,QAAL,GAAgBA,QAAhB;SACKC,EAAL,GAAUA,EAAV;SACKC,IAAL,GAAYA,IAAZ;SACKC,GAAL,GAAW,CAAX;SACKC,QAAL,GAAgB,KAAKJ,QAAL,CAAcI,QAAd,IAA0B,CAAC,KAAKF,IAAL,CAAUG,MAArD;SACKC,kBAAL,GAA0B,CAA1B;SACKC,MAAL,GAAc,EAAd;;;EAGFC,KAAK,CAACC,KAAD,EAAQ;QACP,CAAChC,MAAM,CAACK,QAAP,CAAgB2B,KAAhB,CAAL,EAA6B;MAC3BA,KAAK,GAAGhC,MAAM,CAACC,IAAP,CAAY+B,KAAK,GAAG,IAApB,EAA0B,QAA1B,CAAR;;;SAGGH,kBAAL,IAA2BG,KAAK,CAAC3D,MAAjC;;QACI,KAAKoD,IAAL,CAAUQ,MAAV,IAAoB,IAAxB,EAA8B;WACvBR,IAAL,CAAUQ,MAAV,GAAmB,CAAnB;;;SAEGH,MAAL,CAAYtD,IAAZ,CAAiBwD,KAAjB;SACKP,IAAL,CAAUQ,MAAV,IAAoBD,KAAK,CAAC3D,MAA1B;;QACI,KAAKsD,QAAT,EAAmB;aACT,KAAKF,IAAL,CAAUG,MAAV,GAAmB,aAA3B;;;;EAIJpC,GAAG,CAACwC,KAAD,EAAQ;QACLA,KAAJ,EAAW;WACJD,KAAL,CAAWC,KAAX;;;WAEK,KAAKE,QAAL,EAAP;;;EAGFA,QAAQ,GAAG;SACJC,MAAL,GAAc,KAAKZ,QAAL,CAAca,OAA5B;UAEM1C,SAAS,GAAG,KAAK6B,QAAL,CAAcc,SAAd,GACd,KAAKd,QAAL,CAAcc,SAAd,CAAwBC,YAAxB,CAAqC,KAAKd,EAA1C,EAA8C,KAAKE,GAAnD,CADc,GAEd,IAFJ;;QAII,KAAKI,MAAL,CAAYzD,MAAhB,EAAwB;WACjByD,MAAL,GAAc9B,MAAM,CAACuC,MAAP,CAAc,KAAKT,MAAnB,CAAd;;UACI,KAAKH,QAAT,EAAmB;aACZG,MAAL,GAAcU,IAAI,CAACC,WAAL,CAAiB,KAAKX,MAAtB,CAAd;;;UAGEpC,SAAJ,EAAe;aACRoC,MAAL,GAAcpC,SAAS,CAAC,KAAKoC,MAAN,CAAvB;;;WAGGL,IAAL,CAAUQ,MAAV,GAAmB,KAAKH,MAAL,CAAYzD,MAA/B;;;SAGGkD,QAAL,CAAcmB,MAAd,CAAsB,GAAE,KAAKlB,EAAG,IAAG,KAAKE,GAAI,MAA5C;;SACKH,QAAL,CAAcmB,MAAd,CAAqBjE,SAAS,CAACC,OAAV,CAAkB,KAAK+C,IAAvB,EAA6B/B,SAA7B,CAArB;;QAEI,KAAKoC,MAAL,CAAYzD,MAAhB,EAAwB;WACjBkD,QAAL,CAAcmB,MAAd,CAAqB,QAArB;;WACKnB,QAAL,CAAcmB,MAAd,CAAqB,KAAKZ,MAA1B;;WAEKA,MAAL,GAAc,EAAd,CAJsB;;WAKjBP,QAAL,CAAcmB,MAAd,CAAqB,aAArB;;;SAGGnB,QAAL,CAAcmB,MAAd,CAAqB,QAArB;;SACKnB,QAAL,CAAcoB,OAAd,CAAsB,IAAtB;;;EAEFzF,QAAQ,GAAG;WACD,GAAE,KAAKsE,EAAG,IAAG,KAAKE,GAAI,IAA9B;;;;;AC/EJ;;;;AAKA,MAAMkB,eAAe,GAAG;EACtBC,GAAG,EAAE,EADiB;EAEtBC,IAAI,EAAE,EAFgB;EAGtBC,MAAM,EAAE,EAHc;EAItBC,KAAK,EAAE;CAJT;AAOA,MAAMC,KAAK,GAAG;SACL,CAAC,OAAD,EAAU,OAAV,CADK;SAEL,CAAC,OAAD,EAAU,OAAV,CAFK;EAGZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAHQ;EAIZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAJQ;EAKZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CALQ;EAMZC,EAAE,EAAE,CAAC,MAAD,EAAS,OAAT,CANQ;EAOZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAPQ;EAQZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CARQ;EASZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CATQ;EAUZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAVQ;EAWZC,EAAE,EAAE,CAAC,KAAD,EAAQ,MAAR,CAXQ;EAYZC,EAAE,EAAE,CAAC,MAAD,EAAS,KAAT,CAZQ;EAaZC,GAAG,EAAE,CAAC,IAAD,EAAO,MAAP,CAbO;EAcZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAdQ;EAeZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAfQ;EAgBZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAhBQ;EAiBZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAjBQ;EAkBZC,EAAE,EAAE,CAAC,MAAD,EAAS,OAAT,CAlBQ;EAmBZC,EAAE,EAAE,CAAC,KAAD,EAAQ,MAAR,CAnBQ;EAoBZC,EAAE,EAAE,CAAC,MAAD,EAAS,KAAT,CApBQ;EAqBZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CArBQ;EAsBZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAtBQ;EAuBZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAvBQ;EAwBZC,GAAG,EAAE,CAAC,KAAD,EAAQ,MAAR,CAxBO;EAyBZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CAzBQ;EA0BZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CA1BQ;EA2BZC,EAAE,EAAE,CAAC,OAAD,EAAU,OAAV,CA3BQ;EA4BZC,EAAE,EAAE,CAAC,MAAD,EAAS,OAAT,CA5BQ;EA6BZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CA7BQ;EA8BZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CA9BQ;EA+BZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CA/BQ;EAgCZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAhCQ;EAiCZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAjCQ;EAkCZC,EAAE,EAAE,CAAC,MAAD,EAAS,MAAT,CAlCQ;EAmCZC,GAAG,EAAE,CAAC,KAAD,EAAQ,MAAR,CAnCO;EAoCZC,GAAG,EAAE,CAAC,MAAD,EAAS,OAAT,CApCO;EAqCZC,GAAG,EAAE,CAAC,OAAD,EAAU,MAAV,CArCO;EAsCZC,GAAG,EAAE,CAAC,MAAD,EAAS,OAAT,CAtCO;EAuCZC,GAAG,EAAE,CAAC,MAAD,EAAS,MAAT,CAvCO;EAwCZC,GAAG,EAAE,CAAC,MAAD,EAAS,MAAT,CAxCO;EAyCZC,IAAI,EAAE,CAAC,OAAD,EAAU,OAAV,CAzCM;EA0CZC,IAAI,EAAE,CAAC,OAAD,EAAU,OAAV,CA1CM;EA2CZC,IAAI,EAAE,CAAC,OAAD,EAAU,OAAV,CA3CM;EA4CZC,IAAI,EAAE,CAAC,MAAD,EAAS,OAAT,CA5CM;EA6CZC,IAAI,EAAE,CAAC,KAAD,EAAQ,MAAR,CA7CM;EA8CZC,SAAS,EAAE,CAAC,MAAD,EAAS,KAAT,CA9CC;EA+CZC,KAAK,EAAE,CAAC,KAAD,EAAQ,KAAR,CA/CK;EAgDZC,KAAK,EAAE,CAAC,KAAD,EAAQ,MAAR,CAhDK;EAiDZC,MAAM,EAAE,CAAC,KAAD,EAAQ,KAAR,CAjDI;EAkDZC,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR;CAlDX;;AAqDA,MAAMC,OAAN,CAAc;EACZ7I,WAAW,CAACkE,QAAD,EAAWjE,OAAO,GAAG,EAArB,EAAyB;SAC7BiE,QAAL,GAAgBA,QAAhB;SACK4E,IAAL,GAAY7I,OAAO,CAAC6I,IAAR,IAAgB,QAA5B;SACKC,MAAL,GAAc9I,OAAO,CAAC8I,MAAR,IAAkB,UAAhC,CAHkC;;QAM9B,OAAO9I,OAAO,CAAC+I,MAAf,KAA0B,QAA9B,EAAwC;WACjCC,OAAL,GAAe;QACbzD,GAAG,EAAEvF,OAAO,CAAC+I,MADA;QAEbvD,IAAI,EAAExF,OAAO,CAAC+I,MAFD;QAGbtD,MAAM,EAAEzF,OAAO,CAAC+I,MAHH;QAIbrD,KAAK,EAAE1F,OAAO,CAAC+I;OAJjB,CADsC;KAAxC,MASO;WACAC,OAAL,GAAehJ,OAAO,CAACgJ,OAAR,IAAmB1D,eAAlC;KAhBgC;;;UAoB5B2D,UAAU,GAAGvH,KAAK,CAAC6B,OAAN,CAAc,KAAKsF,IAAnB,IACf,KAAKA,IADU,GAEflD,KAAK,CAAC,KAAKkD,IAAL,CAAUK,WAAV,EAAD,CAFT;SAGKC,KAAL,GAAaF,UAAU,CAAC,KAAKH,MAAL,KAAgB,UAAhB,GAA6B,CAA7B,GAAiC,CAAlC,CAAvB;SACKM,MAAL,GAAcH,UAAU,CAAC,KAAKH,MAAL,KAAgB,UAAhB,GAA6B,CAA7B,GAAiC,CAAlC,CAAxB;SAEKO,OAAL,GAAe,KAAKpF,QAAL,CAAcqF,GAAd,EAAf,CA1BkC;;SA6B7BC,SAAL,GAAiB,KAAKtF,QAAL,CAAcqF,GAAd,CAAkB;MACjCE,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,QAAhB,EAA0B,QAA1B,EAAoC,QAApC;KADM,CAAjB,CA7BkC;;SAkC7BC,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,CAAkB;MAClCI,IAAI,EAAE,MAD4B;MAElCC,MAAM,EAAE,KAAK1F,QAAL,CAAc2F,KAAd,CAAoBzF,IAApB,CAAyB0F,KAFC;MAGlCC,QAAQ,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,KAAKX,KAAZ,EAAmB,KAAKC,MAAxB,CAHwB;MAIlCW,QAAQ,EAAE,KAAKV,OAJmB;MAKlCW,SAAS,EAAE,KAAKT;KALA,CAAlB;SAQKU,QAAL,GAAgB,EAAhB;GA3CU;;;MA+CRC,KAAJ,GAAY;UACJ/F,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;WACOA,IAAI,CAACgG,IAAL,IAAa,IAAb,GAAoBhG,IAAI,CAACgG,IAAzB,GAAiChG,IAAI,CAACgG,IAAL,GAAY,EAApD;;;MAGEC,QAAJ,GAAe;UACPjG,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;WACOA,IAAI,CAACkG,OAAL,IAAgB,IAAhB,GAAuBlG,IAAI,CAACkG,OAA5B,GAAuClG,IAAI,CAACkG,OAAL,GAAe,EAA7D;;;MAGEC,WAAJ,GAAkB;UACVnG,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;WACOA,IAAI,CAACoG,SAAL,IAAkB,IAAlB,GAAyBpG,IAAI,CAACoG,SAA9B,GAA2CpG,IAAI,CAACoG,SAAL,GAAiB,EAAnE;;;MAGEC,QAAJ,GAAe;UACPrG,IAAI,GAAG,KAAKoF,SAAL,CAAepF,IAA5B;WACOA,IAAI,CAACsG,OAAL,IAAgB,IAAhB,GAAuBtG,IAAI,CAACsG,OAA5B,GAAuCtG,IAAI,CAACsG,OAAL,GAAe,EAA7D;;;MAGEC,WAAJ,GAAkB;UACVvG,IAAI,GAAG,KAAKsF,UAAL,CAAgBtF,IAA7B;WACOA,IAAI,CAACwG,MAAL,IAAe,IAAf,GAAsBxG,IAAI,CAACwG,MAA3B,GAAqCxG,IAAI,CAACwG,MAAL,GAAc,EAA1D;;;MAGEC,mBAAJ,GAA0B;UAClBzG,IAAI,GAAG,KAAKsF,UAAL,CAAgBtF,IAA7B;WACOA,IAAI,CAAC0G,aAAL,IAAsB,IAAtB,GACL1G,IAAI,CAAC0G,aADA,GAEJ1G,IAAI,CAAC0G,aAAL,GAAqB,KAAK5G,QAAL,CAAc6G,6BAAd,EAFxB;;;EAKFC,IAAI,GAAG;WACE,KAAK3B,MAAL,GAAc,KAAKJ,OAAL,CAAavD,MAAlC;;;EAGFhB,KAAK,CAACC,KAAD,EAAQ;WACJ,KAAK2E,OAAL,CAAa5E,KAAb,CAAmBC,KAAnB,CAAP;;;EAGFxC,GAAG,GAAG;SACCuH,UAAL,CAAgBvH,GAAhB;SACKqH,SAAL,CAAerH,GAAf;WACO,KAAKmH,OAAL,CAAanH,GAAb,EAAP;;;;;AC3JJ;;;AAIA;AAEA,MAAM8I,WAAN,SAA0BlL,OAA1B,CAAkC;EAChCe,YAAY,CAACF,CAAD,EAAIC,CAAJ,EAAO;WACVD,CAAC,CAACsK,aAAF,CAAgBrK,CAAhB,CAAP;;;EAGFU,SAAS,GAAG;WACH,OAAP;;;EAGFD,WAAW,CAAC6J,CAAD,EAAI;WACN,IAAI7I,MAAJ,CAAW6I,CAAX,CAAP;;;;;AChBJ;;;;;;AAMA,SAASC,OAAT,CAAiBC,KAAjB,EAAwBC,UAAxB,EAAoC;MAC9BD,KAAK,GAAGC,UAAU,CAAC,CAAD,CAAtB,EAA2B,OAAO,KAAP;MACvBC,UAAU,GAAG,CAAjB;MACIC,QAAQ,GAAGF,UAAU,CAACtK,MAAX,GAAoB,CAAnC;;SACOuK,UAAU,IAAIC,QAArB,EAA+B;UACvBC,WAAW,GAAG1H,IAAI,CAAC2H,KAAL,CAAW,CAACH,UAAU,GAAGC,QAAd,IAA0B,CAArC,CAApB,CAD6B;;UAIvBG,UAAU,GAAGF,WAAW,GAAG,CAAjC,CAJ6B;;QAQ3BJ,KAAK,IAAIC,UAAU,CAACK,UAAD,CAAnB,IACAN,KAAK,IAAIC,UAAU,CAACK,UAAU,GAAG,CAAd,CAFrB,EAGE;aACO,IAAP;;;QAGEN,KAAK,GAAGC,UAAU,CAACK,UAAU,GAAG,CAAd,CAAtB,EAAwC;;MAEtCJ,UAAU,GAAGE,WAAW,GAAG,CAA3B;KAFF,MAGO;;MAELD,QAAQ,GAAGC,WAAW,GAAG,CAAzB;;;;SAGG,KAAP;;;AC7BF;;;;;AAIA,MAAMG,sBAAsB,GAAG,CAC7B,MAD6B,EAE7B,MAF6B,EAG7B,MAH6B,EAI7B,MAJ6B,EAK7B,MAL6B,EAM7B,MAN6B,EAO7B,MAP6B,EAQ7B,MAR6B,EAS7B,MAT6B,EAU7B,MAV6B,EAW7B,MAX6B,EAY7B,MAZ6B,EAa7B,MAb6B,EAc7B,MAd6B,EAe7B,MAf6B,EAgB7B,MAhB6B,EAiB7B,MAjB6B,EAkB7B,MAlB6B,EAmB7B,MAnB6B,EAoB7B,MApB6B,EAqB7B,MArB6B,EAsB7B,MAtB6B,EAuB7B,MAvB6B,EAwB7B,MAxB6B,EAyB7B,MAzB6B,EA0B7B,MA1B6B,EA2B7B,MA3B6B,EA4B7B,MA5B6B,EA6B7B,MA7B6B,EA8B7B,MA9B6B,EA+B7B,MA/B6B,EAgC7B,MAhC6B,EAiC7B,MAjC6B,EAkC7B,MAlC6B,EAmC7B,MAnC6B,EAoC7B,MApC6B,EAqC7B,MArC6B,EAsC7B,MAtC6B,EAuC7B,MAvC6B,EAwC7B,MAxC6B,EAyC7B,MAzC6B,EA0C7B,MA1C6B,EA2C7B,MA3C6B,EA4C7B,MA5C6B,EA6C7B,MA7C6B,EA8C7B,MA9C6B,EA+C7B,MA/C6B,EAgD7B,MAhD6B,EAiD7B,MAjD6B,EAkD7B,MAlD6B,EAmD7B,MAnD6B,EAoD7B,MApD6B,EAqD7B,MArD6B,EAsD7B,MAtD6B,EAuD7B,MAvD6B,EAwD7B,MAxD6B,EAyD7B,MAzD6B,EA0D7B,MA1D6B,EA2D7B,MA3D6B,EA4D7B,MA5D6B,EA6D7B,MA7D6B,EA8D7B,MA9D6B,EA+D7B,MA/D6B,EAgE7B,MAhE6B,EAiE7B,MAjE6B,EAkE7B,MAlE6B,EAmE7B,MAnE6B,EAoE7B,MApE6B,EAqE7B,MArE6B,EAsE7B,MAtE6B,EAuE7B,MAvE6B,EAwE7B,MAxE6B,EAyE7B,MAzE6B,EA0E7B,MA1E6B,EA2E7B,MA3E6B,EA4E7B,MA5E6B,EA6E7B,MA7E6B,EA8E7B,MA9E6B,EA+E7B,MA/E6B,EAgF7B,MAhF6B,EAiF7B,MAjF6B,EAkF7B,MAlF6B,EAmF7B,MAnF6B,EAoF7B,MApF6B,EAqF7B,MArF6B,EAsF7B,MAtF6B,EAuF7B,MAvF6B,EAwF7B,MAxF6B,EAyF7B,MAzF6B,EA0F7B,MA1F6B,EA2F7B,MA3F6B,EA4F7B,MA5F6B,EA6F7B,MA7F6B,EA8F7B,MA9F6B,EA+F7B,MA/F6B,EAgG7B,MAhG6B,EAiG7B,MAjG6B,EAkG7B,MAlG6B,EAmG7B,MAnG6B,EAoG7B,MApG6B,EAqG7B,MArG6B,EAsG7B,MAtG6B,EAuG7B,MAvG6B,EAwG7B,MAxG6B,EAyG7B,MAzG6B,EA0G7B,MA1G6B,EA2G7B,MA3G6B,EA4G7B,MA5G6B,EA6G7B,MA7G6B,EA8G7B,MA9G6B,EA+G7B,MA/G6B,EAgH7B,MAhH6B,EAiH7B,MAjH6B,EAkH7B,MAlH6B,EAmH7B,MAnH6B,EAoH7B,MApH6B,EAqH7B,MArH6B,EAsH7B,MAtH6B,EAuH7B,MAvH6B,EAwH7B,MAxH6B,EAyH7B,MAzH6B,EA0H7B,MA1H6B,EA2H7B,MA3H6B,EA4H7B,MA5H6B,EA6H7B,MA7H6B,EA8H7B,MA9H6B,EA+H7B,MA/H6B,EAgI7B,MAhI6B,EAiI7B,MAjI6B,EAkI7B,MAlI6B,EAmI7B,MAnI6B,EAoI7B,MApI6B,EAqI7B,MArI6B,EAsI7B,MAtI6B,EAuI7B,MAvI6B,EAwI7B,MAxI6B,EAyI7B,MAzI6B,EA0I7B,MA1I6B,EA2I7B,MA3I6B,EA4I7B,MA5I6B,EA6I7B,MA7I6B,EA8I7B,MA9I6B,EA+I7B,MA/I6B,EAgJ7B,MAhJ6B,EAiJ7B,MAjJ6B,EAkJ7B,MAlJ6B,EAmJ7B,MAnJ6B,EAoJ7B,MApJ6B,EAqJ7B,MArJ6B,EAsJ7B,MAtJ6B,EAuJ7B,MAvJ6B,EAwJ7B,MAxJ6B,EAyJ7B,MAzJ6B,EA0J7B,MA1J6B,EA2J7B,MA3J6B,EA4J7B,MA5J6B,EA6J7B,MA7J6B,EA8J7B,MA9J6B,EA+J7B,MA/J6B,EAgK7B,MAhK6B,EAiK7B,MAjK6B,EAkK7B,MAlK6B,EAmK7B,MAnK6B,EAoK7B,MApK6B,EAqK7B,MArK6B,EAsK7B,MAtK6B,EAuK7B,MAvK6B,EAwK7B,MAxK6B,EAyK7B,MAzK6B,EA0K7B,MA1K6B,EA2K7B,MA3K6B,EA4K7B,MA5K6B,EA6K7B,MA7K6B,EA8K7B,MA9K6B,EA+K7B,MA/K6B,EAgL7B,MAhL6B,EAiL7B,MAjL6B,EAkL7B,MAlL6B,EAmL7B,MAnL6B,EAoL7B,MApL6B,EAqL7B,MArL6B,EAsL7B,MAtL6B,EAuL7B,MAvL6B,EAwL7B,MAxL6B,EAyL7B,MAzL6B,EA0L7B,MA1L6B,EA2L7B,MA3L6B,EA4L7B,MA5L6B,EA6L7B,MA7L6B,EA8L7B,MA9L6B,EA+L7B,MA/L6B,EAgM7B,MAhM6B,EAiM7B,MAjM6B,EAkM7B,MAlM6B,EAmM7B,MAnM6B,EAoM7B,MApM6B,EAqM7B,MArM6B,EAsM7B,MAtM6B,EAuM7B,MAvM6B,EAwM7B,MAxM6B,EAyM7B,MAzM6B,EA0M7B,MA1M6B,EA2M7B,MA3M6B,EA4M7B,MA5M6B,EA6M7B,MA7M6B,EA8M7B,MA9M6B,EA+M7B,MA/M6B,EAgN7B,MAhN6B,EAiN7B,MAjN6B,EAkN7B,MAlN6B,EAmN7B,MAnN6B,EAoN7B,MApN6B,EAqN7B,MArN6B,EAsN7B,MAtN6B,EAuN7B,MAvN6B,EAwN7B,MAxN6B,EAyN7B,MAzN6B,EA0N7B,MA1N6B,EA2N7B,MA3N6B,EA4N7B,MA5N6B,EA6N7B,MA7N6B,EA8N7B,MA9N6B,EA+N7B,MA/N6B,EAgO7B,MAhO6B,EAiO7B,MAjO6B,EAkO7B,MAlO6B,EAmO7B,MAnO6B,EAoO7B,MApO6B,EAqO7B,MArO6B,EAsO7B,MAtO6B,EAuO7B,MAvO6B,EAwO7B,MAxO6B,EAyO7B,MAzO6B,EA0O7B,MA1O6B,EA2O7B,MA3O6B,EA4O7B,MA5O6B,EA6O7B,MA7O6B,EA8O7B,MA9O6B,EA+O7B,MA/O6B,EAgP7B,MAhP6B,EAiP7B,MAjP6B,EAkP7B,MAlP6B,EAmP7B,MAnP6B,EAoP7B,MApP6B,EAqP7B,MArP6B,EAsP7B,MAtP6B,EAuP7B,MAvP6B,EAwP7B,MAxP6B,EAyP7B,MAzP6B,EA0P7B,MA1P6B,EA2P7B,MA3P6B,EA4P7B,MA5P6B,EA6P7B,MA7P6B,EA8P7B,MA9P6B,EA+P7B,MA/P6B,EAgQ7B,MAhQ6B,EAiQ7B,MAjQ6B,EAkQ7B,MAlQ6B,EAmQ7B,MAnQ6B,EAoQ7B,MApQ6B,EAqQ7B,MArQ6B,EAsQ7B,MAtQ6B,EAuQ7B,MAvQ6B,EAwQ7B,MAxQ6B,EAyQ7B,MAzQ6B,EA0Q7B,MA1Q6B,EA2Q7B,MA3Q6B,EA4Q7B,MA5Q6B,EA6Q7B,MA7Q6B,EA8Q7B,MA9Q6B,EA+Q7B,MA/Q6B,EAgR7B,MAhR6B,EAiR7B,MAjR6B,EAkR7B,MAlR6B,EAmR7B,MAnR6B,EAoR7B,MApR6B,EAqR7B,MArR6B,EAsR7B,MAtR6B,EAuR7B,MAvR6B,EAwR7B,MAxR6B,EAyR7B,MAzR6B,EA0R7B,MA1R6B,EA2R7B,MA3R6B,EA4R7B,MA5R6B,EA6R7B,MA7R6B,EA8R7B,MA9R6B,EA+R7B,MA/R6B,EAgS7B,MAhS6B,EAiS7B,MAjS6B,EAkS7B,MAlS6B,EAmS7B,MAnS6B,EAoS7B,MApS6B,EAqS7B,MArS6B,EAsS7B,MAtS6B,EAuS7B,MAvS6B,EAwS7B,MAxS6B,EAyS7B,MAzS6B,EA0S7B,MA1S6B,EA2S7B,MA3S6B,EA4S7B,MA5S6B,EA6S7B,MA7S6B,EA8S7B,MA9S6B,EA+S7B,MA/S6B,EAgT7B,MAhT6B,EAiT7B,MAjT6B,EAkT7B,MAlT6B,EAmT7B,MAnT6B,EAoT7B,MApT6B,EAqT7B,MArT6B,EAsT7B,MAtT6B,EAuT7B,MAvT6B,EAwT7B,MAxT6B,EAyT7B,MAzT6B,EA0T7B,MA1T6B,EA2T7B,MA3T6B,EA4T7B,MA5T6B,EA6T7B,MA7T6B,EA8T7B,MA9T6B,EA+T7B,MA/T6B,EAgU7B,MAhU6B,EAiU7B,MAjU6B,EAkU7B,MAlU6B,EAmU7B,MAnU6B,EAoU7B,MApU6B,EAqU7B,MArU6B,EAsU7B,MAtU6B,EAuU7B,MAvU6B,EAwU7B,MAxU6B,EAyU7B,MAzU6B,EA0U7B,MA1U6B,EA2U7B,MA3U6B,EA4U7B,MA5U6B,EA6U7B,MA7U6B,EA8U7B,MA9U6B,EA+U7B,MA/U6B,EAgV7B,MAhV6B,EAiV7B,MAjV6B,EAkV7B,MAlV6B,EAmV7B,MAnV6B,EAoV7B,MApV6B,EAqV7B,MArV6B,EAsV7B,MAtV6B,EAuV7B,MAvV6B,EAwV7B,MAxV6B,EAyV7B,MAzV6B,EA0V7B,MA1V6B,EA2V7B,MA3V6B,EA4V7B,MA5V6B,EA6V7B,MA7V6B,EA8V7B,MA9V6B,EA+V7B,MA/V6B,EAgW7B,MAhW6B,EAiW7B,MAjW6B,EAkW7B,MAlW6B,EAmW7B,MAnW6B,EAoW7B,MApW6B,EAqW7B,MArW6B,EAsW7B,MAtW6B,EAuW7B,MAvW6B,EAwW7B,MAxW6B,EAyW7B,MAzW6B,EA0W7B,MA1W6B,EA2W7B,MA3W6B,EA4W7B,MA5W6B,EA6W7B,MA7W6B,EA8W7B,MA9W6B,EA+W7B,MA/W6B,EAgX7B,MAhX6B,EAiX7B,MAjX6B,EAkX7B,MAlX6B,EAmX7B,MAnX6B,EAoX7B,MApX6B,EAqX7B,MArX6B,EAsX7B,MAtX6B,EAuX7B,MAvX6B,EAwX7B,MAxX6B,EAyX7B,MAzX6B,EA0X7B,MA1X6B,EA2X7B,MA3X6B,EA4X7B,MA5X6B,EA6X7B,MA7X6B,EA8X7B,MA9X6B,EA+X7B,MA/X6B,EAgY7B,MAhY6B,EAiY7B,MAjY6B,EAkY7B,MAlY6B,EAmY7B,MAnY6B,EAoY7B,MApY6B,EAqY7B,MArY6B,EAsY7B,MAtY6B,EAuY7B,MAvY6B,EAwY7B,MAxY6B,EAyY7B,MAzY6B,EA0Y7B,MA1Y6B,EA2Y7B,MA3Y6B,EA4Y7B,MA5Y6B,EA6Y7B,MA7Y6B,EA8Y7B,MA9Y6B,EA+Y7B,MA/Y6B,EAgZ7B,MAhZ6B,EAiZ7B,MAjZ6B,EAkZ7B,MAlZ6B,EAmZ7B,MAnZ6B,EAoZ7B,MApZ6B,EAqZ7B,MArZ6B,EAsZ7B,MAtZ6B,EAuZ7B,MAvZ6B,EAwZ7B,MAxZ6B,EAyZ7B,MAzZ6B,EA0Z7B,MA1Z6B,EA2Z7B,MA3Z6B,EA4Z7B,MA5Z6B,EA6Z7B,MA7Z6B,EA8Z7B,MA9Z6B,EA+Z7B,MA/Z6B,EAga7B,MAha6B,EAia7B,MAja6B,EAka7B,MAla6B,EAma7B,MAna6B,EAoa7B,MApa6B,EAqa7B,MAra6B,EAsa7B,MAta6B,EAua7B,MAva6B,EAwa7B,MAxa6B,EAya7B,MAza6B,EA0a7B,MA1a6B,EA2a7B,MA3a6B,EA4a7B,MA5a6B,EA6a7B,MA7a6B,EA8a7B,MA9a6B,EA+a7B,MA/a6B,EAgb7B,MAhb6B,EAib7B,MAjb6B,EAkb7B,MAlb6B,EAmb7B,MAnb6B,EAob7B,MApb6B,EAqb7B,MArb6B,EAsb7B,MAtb6B,EAub7B,MAvb6B,EAwb7B,MAxb6B,EAyb7B,MAzb6B,EA0b7B,MA1b6B,EA2b7B,MA3b6B,EA4b7B,MA5b6B,EA6b7B,MA7b6B,EA8b7B,MA9b6B,EA+b7B,MA/b6B,EAgc7B,MAhc6B,EAic7B,MAjc6B,EAkc7B,MAlc6B,EAmc7B,MAnc6B,EAoc7B,MApc6B,EAqc7B,MArc6B,EAsc7B,MAtc6B,EAuc7B,MAvc6B,EAwc7B,MAxc6B,EAyc7B,MAzc6B,EA0c7B,MA1c6B,EA2c7B,MA3c6B,EA4c7B,MA5c6B,EA6c7B,MA7c6B,EA8c7B,MA9c6B,EA+c7B,MA/c6B,EAgd7B,MAhd6B,EAid7B,MAjd6B,EAkd7B,MAld6B,EAmd7B,MAnd6B,EAod7B,MApd6B,EAqd7B,MArd6B,EAsd7B,MAtd6B,EAud7B,MAvd6B,EAwd7B,MAxd6B,EAyd7B,MAzd6B,EA0d7B,MA1d6B,EA2d7B,MA3d6B,EA4d7B,MA5d6B,EA6d7B,MA7d6B,EA8d7B,MA9d6B,EA+d7B,MA/d6B,EAge7B,MAhe6B,EAie7B,MAje6B,EAke7B,MAle6B,EAme7B,MAne6B,EAoe7B,MApe6B,EAqe7B,MAre6B,EAse7B,MAte6B,EAue7B,MAve6B,EAwe7B,MAxe6B,EAye7B,MAze6B,EA0e7B,MA1e6B,EA2e7B,MA3e6B,EA4e7B,MA5e6B,EA6e7B,MA7e6B,EA8e7B,MA9e6B,EA+e7B,MA/e6B,EAgf7B,MAhf6B,EAif7B,MAjf6B,EAkf7B,MAlf6B,EAmf7B,MAnf6B,EAof7B,MApf6B,EAqf7B,MArf6B,EAsf7B,MAtf6B,EAuf7B,MAvf6B,EAwf7B,MAxf6B,EAyf7B,MAzf6B,EA0f7B,MA1f6B,EA2f7B,MA3f6B,EA4f7B,MA5f6B,EA6f7B,MA7f6B,EA8f7B,MA9f6B,EA+f7B,MA/f6B,EAggB7B,MAhgB6B,EAigB7B,MAjgB6B,EAkgB7B,MAlgB6B,EAmgB7B,MAngB6B,EAogB7B,MApgB6B,EAqgB7B,MArgB6B,EAsgB7B,MAtgB6B,EAugB7B,MAvgB6B,EAwgB7B,MAxgB6B,EAygB7B,MAzgB6B,EA0gB7B,MA1gB6B,EA2gB7B,MA3gB6B,EA4gB7B,MA5gB6B,EA6gB7B,MA7gB6B,EA8gB7B,MA9gB6B,EA+gB7B,MA/gB6B,EAghB7B,MAhhB6B,EAihB7B,MAjhB6B,EAkhB7B,MAlhB6B,EAmhB7B,MAnhB6B,EAohB7B,MAphB6B,EAqhB7B,MArhB6B,EAshB7B,MAthB6B,EAuhB7B,MAvhB6B,EAwhB7B,MAxhB6B,EAyhB7B,MAzhB6B,EA0hB7B,MA1hB6B,EA2hB7B,MA3hB6B,EA4hB7B,MA5hB6B,EA6hB7B,MA7hB6B,EA8hB7B,MA9hB6B,EA+hB7B,MA/hB6B,EAgiB7B,MAhiB6B,EAiiB7B,MAjiB6B,EAkiB7B,MAliB6B,EAmiB7B,MAniB6B,EAoiB7B,MApiB6B,EAqiB7B,MAriB6B,EAsiB7B,MAtiB6B,EAuiB7B,MAviB6B,EAwiB7B,MAxiB6B,EAyiB7B,MAziB6B,EA0iB7B,MA1iB6B,EA2iB7B,MA3iB6B,EA4iB7B,MA5iB6B,EA6iB7B,MA7iB6B,EA8iB7B,MA9iB6B,EA+iB7B,MA/iB6B,EAgjB7B,MAhjB6B,EAijB7B,MAjjB6B,EAkjB7B,MAljB6B,EAmjB7B,MAnjB6B,EAojB7B,MApjB6B,EAqjB7B,MArjB6B,EAsjB7B,MAtjB6B,EAujB7B,MAvjB6B,EAwjB7B,MAxjB6B,EAyjB7B,MAzjB6B,EA0jB7B,MA1jB6B,EA2jB7B,MA3jB6B,EA4jB7B,MA5jB6B,EA6jB7B,MA7jB6B,EA8jB7B,MA9jB6B,EA+jB7B,MA/jB6B,EAgkB7B,MAhkB6B,EAikB7B,MAjkB6B,EAkkB7B,MAlkB6B,EAmkB7B,MAnkB6B,EAokB7B,MApkB6B,EAqkB7B,MArkB6B,EAskB7B,MAtkB6B,EAukB7B,MAvkB6B,EAwkB7B,MAxkB6B,EAykB7B,MAzkB6B,EA0kB7B,MA1kB6B,EA2kB7B,MA3kB6B,EA4kB7B,MA5kB6B,EA6kB7B,MA7kB6B,EA8kB7B,MA9kB6B,EA+kB7B,MA/kB6B,EAglB7B,MAhlB6B,EAilB7B,MAjlB6B,EAklB7B,MAllB6B,EAmlB7B,MAnlB6B,EAolB7B,MAplB6B,EAqlB7B,MArlB6B,EAslB7B,MAtlB6B,EAulB7B,MAvlB6B,EAwlB7B,MAxlB6B,EAylB7B,MAzlB6B,EA0lB7B,MA1lB6B,EA2lB7B,MA3lB6B,EA4lB7B,MA5lB6B,EA6lB7B,MA7lB6B,EA8lB7B,MA9lB6B,EA+lB7B,MA/lB6B,EAgmB7B,MAhmB6B,EAimB7B,MAjmB6B,EAkmB7B,MAlmB6B,EAmmB7B,MAnmB6B,EAomB7B,MApmB6B,EAqmB7B,MArmB6B,EAsmB7B,MAtmB6B,EAumB7B,MAvmB6B,EAwmB7B,MAxmB6B,EAymB7B,MAzmB6B,EA0mB7B,MA1mB6B,EA2mB7B,MA3mB6B,EA4mB7B,MA5mB6B,EA6mB7B,MA7mB6B,EA8mB7B,MA9mB6B,EA+mB7B,MA/mB6B,EAgnB7B,MAhnB6B,EAinB7B,MAjnB6B,EAknB7B,MAlnB6B,EAmnB7B,MAnnB6B,EAonB7B,MApnB6B,EAqnB7B,MArnB6B,EAsnB7B,MAtnB6B,EAunB7B,MAvnB6B,EAwnB7B,MAxnB6B,EAynB7B,MAznB6B,EA0nB7B,MA1nB6B,EA2nB7B,MA3nB6B,EA4nB7B,MA5nB6B,EA6nB7B,MA7nB6B,EA8nB7B,MA9nB6B,EA+nB7B,MA/nB6B,EAgoB7B,MAhoB6B,EAioB7B,MAjoB6B,EAkoB7B,MAloB6B,EAmoB7B,MAnoB6B,EAooB7B,MApoB6B,EAqoB7B,MAroB6B,EAsoB7B,MAtoB6B,EAuoB7B,MAvoB6B,EAwoB7B,MAxoB6B,EAyoB7B,MAzoB6B,EA0oB7B,MA1oB6B,EA2oB7B,MA3oB6B,EA4oB7B,MA5oB6B,EA6oB7B,MA7oB6B,EA8oB7B,MA9oB6B,EA+oB7B,MA/oB6B,EAgpB7B,MAhpB6B,EAipB7B,MAjpB6B,EAkpB7B,MAlpB6B,EAmpB7B,MAnpB6B,EAopB7B,MAppB6B,EAqpB7B,MArpB6B,EAspB7B,MAtpB6B,EAupB7B,MAvpB6B,EAwpB7B,MAxpB6B,EAypB7B,MAzpB6B,EA0pB7B,MA1pB6B,EA2pB7B,MA3pB6B,EA4pB7B,MA5pB6B,EA6pB7B,MA7pB6B,EA8pB7B,MA9pB6B,EA+pB7B,MA/pB6B,EAgqB7B,MAhqB6B,EAiqB7B,MAjqB6B,EAkqB7B,MAlqB6B,EAmqB7B,MAnqB6B,EAoqB7B,MApqB6B,EAqqB7B,MArqB6B,EAsqB7B,MAtqB6B,EAuqB7B,MAvqB6B,EAwqB7B,MAxqB6B,EAyqB7B,MAzqB6B,EA0qB7B,MA1qB6B,EA2qB7B,MA3qB6B,EA4qB7B,MA5qB6B,EA6qB7B,MA7qB6B,EA8qB7B,MA9qB6B,EA+qB7B,MA/qB6B,EAgrB7B,MAhrB6B,EAirB7B,MAjrB6B,EAkrB7B,MAlrB6B,EAmrB7B,MAnrB6B,EAorB7B,MAprB6B,EAqrB7B,MArrB6B,EAsrB7B,MAtrB6B,EAurB7B,MAvrB6B,EAwrB7B,MAxrB6B,EAyrB7B,MAzrB6B,EA0rB7B,MA1rB6B,EA2rB7B,OA3rB6B,EA4rB7B,OA5rB6B,EA6rB7B,OA7rB6B,EA8rB7B,OA9rB6B,EA+rB7B,OA/rB6B,EAgsB7B,OAhsB6B,EAisB7B,OAjsB6B,EAksB7B,OAlsB6B,EAmsB7B,OAnsB6B,EAosB7B,OApsB6B,EAqsB7B,OArsB6B,EAssB7B,OAtsB6B,EAusB7B,OAvsB6B,EAwsB7B,OAxsB6B,EAysB7B,OAzsB6B,EA0sB7B,OA1sB6B,EA2sB7B,OA3sB6B,EA4sB7B,OA5sB6B,EA6sB7B,OA7sB6B,EA8sB7B,OA9sB6B,EA+sB7B,OA/sB6B,EAgtB7B,OAhtB6B,EAitB7B,OAjtB6B,EAktB7B,OAltB6B,EAmtB7B,OAntB6B,EAotB7B,OAptB6B,EAqtB7B,OArtB6B,EAstB7B,OAttB6B,EAutB7B,OAvtB6B,EAwtB7B,OAxtB6B,EAytB7B,OAztB6B,EA0tB7B,OA1tB6B,EA2tB7B,OA3tB6B,EA4tB7B,OA5tB6B,EA6tB7B,OA7tB6B,EA8tB7B,OA9tB6B,EA+tB7B,OA/tB6B,EAguB7B,OAhuB6B,EAiuB7B,OAjuB6B,EAkuB7B,OAluB6B,EAmuB7B,OAnuB6B,EAouB7B,OApuB6B,EAquB7B,OAruB6B,EAsuB7B,OAtuB6B,EAuuB7B,OAvuB6B,EAwuB7B,OAxuB6B,EAyuB7B,OAzuB6B,EA0uB7B,OA1uB6B,EA2uB7B,OA3uB6B,EA4uB7B,OA5uB6B,EA6uB7B,OA7uB6B,EA8uB7B,OA9uB6B,EA+uB7B,OA/uB6B,EAgvB7B,OAhvB6B,EAivB7B,OAjvB6B,EAkvB7B,OAlvB6B,EAmvB7B,OAnvB6B,EAovB7B,OApvB6B,EAqvB7B,OArvB6B,EAsvB7B,OAtvB6B,EAuvB7B,OAvvB6B,EAwvB7B,OAxvB6B,EAyvB7B,OAzvB6B,EA0vB7B,OA1vB6B,EA2vB7B,OA3vB6B,EA4vB7B,OA5vB6B,EA6vB7B,OA7vB6B,EA8vB7B,OA9vB6B,EA+vB7B,OA/vB6B,EAgwB7B,OAhwB6B,EAiwB7B,OAjwB6B,EAkwB7B,OAlwB6B,EAmwB7B,OAnwB6B,EAowB7B,OApwB6B,EAqwB7B,OArwB6B,EAswB7B,OAtwB6B,EAuwB7B,OAvwB6B,EAwwB7B,OAxwB6B,EAywB7B,OAzwB6B,EA0wB7B,OA1wB6B,EA2wB7B,OA3wB6B,EA4wB7B,OA5wB6B,EA6wB7B,OA7wB6B,EA8wB7B,OA9wB6B,EA+wB7B,OA/wB6B,EAgxB7B,OAhxB6B,EAixB7B,OAjxB6B,EAkxB7B,OAlxB6B,EAmxB7B,OAnxB6B,EAoxB7B,OApxB6B,EAqxB7B,OArxB6B,EAsxB7B,OAtxB6B,EAuxB7B,OAvxB6B,EAwxB7B,OAxxB6B,CAA/B;;AA4xBA,MAAMC,qBAAqB,GAAGC,SAAS,IACrCV,OAAO,CAACU,SAAD,EAAYF,sBAAZ,CADT;;;;;;;;AAQA,MAAMG,0BAA0B,GAAG,CACjC,MADiC,EAEjC,MAFiC,EAGjC,MAHiC,EAIjC,MAJiC,EAKjC,MALiC,EAMjC,MANiC,EAOjC,MAPiC,EAQjC,MARiC,EASjC,MATiC,EAUjC,MAViC,EAWjC,MAXiC,EAYjC,MAZiC,EAajC,MAbiC,EAcjC,MAdiC,EAejC,MAfiC,EAgBjC,MAhBiC,EAiBjC,MAjBiC,EAkBjC,MAlBiC,EAmBjC,MAnBiC,EAoBjC,MApBiC,EAqBjC,MArBiC,EAsBjC,MAtBiC,EAuBjC,MAvBiC,EAwBjC,MAxBiC,EAyBjC,MAzBiC,EA0BjC,MA1BiC,EA2BjC,MA3BiC,EA4BjC,MA5BiC,EA6BjC,MA7BiC,EA8BjC,MA9BiC,EA+BjC,MA/BiC,EAgCjC,MAhCiC,EAiCjC,MAjCiC,EAkCjC,MAlCiC,EAmCjC,MAnCiC,EAoCjC,MApCiC,EAqCjC,MArCiC,EAsCjC,MAtCiC,EAuCjC,MAvCiC,EAwCjC,MAxCiC,EAyCjC,MAzCiC,EA0CjC,MA1CiC,EA2CjC,MA3CiC,EA4CjC,MA5CiC,EA6CjC,MA7CiC,EA8CjC,MA9CiC,EA+CjC,MA/CiC,EAgDjC,MAhDiC,EAiDjC,MAjDiC,EAkDjC,MAlDiC,EAmDjC,MAnDiC,EAoDjC,MApDiC,EAqDjC,MArDiC,EAsDjC,MAtDiC,CAAnC;;AA0DA,MAAMC,yBAAyB,GAAGF,SAAS,IACzCV,OAAO,CAACU,SAAD,EAAYC,0BAAZ,CADT;;;;;;;;AAQA,MAAME,0BAA0B,GAAG,CACjC,MADiC,EAEjC;;EACA,MAHiC,EAIjC;;EACA,MALiC,EAMjC;;EACA,MAPiC,EAQjC;;EACA,MATiC,EAUjC;;EACA,MAXiC,EAYjC;;EACA,MAbiC,EAcjC;;EACA,MAfiC,EAgBjC;;EACA,MAjBiC,EAkBjC;;EACA,MAnBiC,EAoBjC;;EACA,MArBiC,EAsBjC;;EACA,MAvBiC,EAwBjC;;EACA,MAzBiC,EA0BjC;;EACA,MA3BiC,EA4BjC;;EACA,MA7BiC,EA8BjC;;EACA,MA/BiC,EAgCjC;;EACA,MAjCiC,EAkCjC;;CAlCF;;AAsCA,MAAMC,wBAAwB,GAAGJ,SAAS,IACxCV,OAAO,CAACU,SAAD,EAAYG,0BAAZ,CADT;;;AAIA,MAAME,6BAA6B,GAAG;;;;;AAKpC,MALoC,EAMpC;;EACA,MAPoC,EAQpC;;EACA,MAToC,EAUpC;;EACA,MAXoC,EAYpC;;EACA,MAboC,EAcpC;;EACA,MAfoC,EAgBpC;;EACA,MAjBoC,EAkBpC;;EACA,MAnBoC,EAoBpC;;EACA,MArBoC,EAsBpC;;EACA,MAvBoC,EAwBpC;;EACA,MAzBoC,EA0BpC;;EACA,MA3BoC,EA4BpC;;EACA,MA7BoC,EA8BpC;;EACA,MA/BoC,EAgCpC;;EACA,MAjCoC,EAkCpC;;EACA,OAnCoC,EAoCpC;;CApCF;AAuCA,MAAMC,wBAAwB,GAAG;;;;;AAK/B,MAL+B,EAM/B;;EACA,MAP+B,EAQ/B;;EACA,OAT+B,EAU/B;;EACA,OAX+B,EAY/B;;EACA,OAb+B,EAc/B;;EACA,OAf+B,EAgB/B;;EACA,OAjB+B,EAkB/B;;EACA,OAnB+B,EAoB/B;;EACA,OArB+B,EAsB/B;;EACA,OAvB+B,EAwB/B;;EACA,OAzB+B,EA0B/B;;EACA,OA3B+B,EA4B/B;;EACA,OA7B+B,EA8B/B;;EACA,OA/B+B,EAgC/B;;EACA,OAjC+B,EAkC/B;;EACA,OAnC+B,EAoC/B;;EACA,QArC+B,EAsC/B;;CAtCF;;;;;AA4CA,MAAMC,qBAAqB,GAAG;;;;;AAK5B,CAL4B,EAM5B;;EACA,MAP4B,EAQ5B;;;;;;;AAMA,MAd4B,EAe5B;;EACA,MAhB4B,EAiB5B;;EACA,MAlB4B,EAmB5B;;EACA,MApB4B,EAqB5B;;EACA,MAtB4B,EAuB5B;;EACA,MAxB4B,EAyB5B;;EACA,MA1B4B,EA2B5B;;EACA,MA5B4B,EA6B5B;;EACA,MA9B4B,EA+B5B;;EACA,MAhC4B,EAiC5B;;EACA,MAlC4B,EAmC5B;;EACA,MApC4B,EAqC5B;;EACA,MAtC4B,EAuC5B;;EACA,MAxC4B,EAyC5B;;EACA,MA1C4B,EA2C5B;;;;;;;AAMA,MAjD4B,EAkD5B;;;;;;;AAMA,MAxD4B,EAyD5B,MAzD4B;;;;;AA+D5B,MA/D4B,EAgE5B;;;;;;;AAMA,MAtE4B,EAuE5B;;EACA,MAxE4B,EAyE5B;;EACA,MA1E4B,EA2E5B;;EACA,MA5E4B,EA6E5B;;EACA,MA9E4B,EA+E5B;;;;;;;AAMA,OArF4B,EAsF5B;;EACA,OAvF4B,EAwF5B;;;;;;;AAOA,OA/F4B,EAgG5B;;EACA,QAjG4B,EAkG5B;;CAlGF;;AAsGA,MAAMC,qBAAqB,GAAGR,SAAS,IACrCV,OAAO,CAACU,SAAD,EAAYG,0BAAZ,CAAP,IACAb,OAAO,CAACU,SAAD,EAAYO,qBAAZ,CADP,IAEAjB,OAAO,CAACU,SAAD,EAAYK,6BAAZ,CAFP,IAGAf,OAAO,CAACU,SAAD,EAAYM,wBAAZ,CAJT;;;;;;;;AAWA,MAAMG,kBAAkB,GAAG,CACzB,MADyB,EAEzB,MAFyB,EAGzB,MAHyB,EAIzB,MAJyB,EAKzB,MALyB,EAMzB,MANyB,EAOzB,MAPyB,EAQzB,MARyB,EASzB,MATyB,EAUzB,MAVyB,EAWzB,MAXyB,EAYzB,MAZyB,EAazB,MAbyB,EAczB,MAdyB,EAezB,MAfyB,EAgBzB,MAhByB,EAiBzB,MAjByB,EAkBzB,MAlByB,EAmBzB,MAnByB,EAoBzB,MApByB,EAqBzB,MArByB,EAsBzB,MAtByB,EAuBzB,MAvByB,EAwBzB,MAxByB,EAyBzB,MAzByB,EA0BzB,MA1ByB,EA2BzB,MA3ByB,EA4BzB,MA5ByB,EA6BzB,MA7ByB,EA8BzB,MA9ByB,EA+BzB,MA/ByB,EAgCzB,MAhCyB,EAiCzB,MAjCyB,EAkCzB,MAlCyB,EAmCzB,MAnCyB,EAoCzB,MApCyB,EAqCzB,MArCyB,EAsCzB,MAtCyB,EAuCzB,MAvCyB,EAwCzB,MAxCyB,EAyCzB,MAzCyB,EA0CzB,MA1CyB,EA2CzB,MA3CyB,EA4CzB,MA5CyB,EA6CzB,MA7CyB,EA8CzB,MA9CyB,EA+CzB,MA/CyB,EAgDzB,MAhDyB,EAiDzB,MAjDyB,EAkDzB,MAlDyB,EAmDzB,MAnDyB,EAoDzB,MApDyB,EAqDzB,MArDyB,EAsDzB,MAtDyB,EAuDzB,MAvDyB,EAwDzB,MAxDyB,EAyDzB,MAzDyB,EA0DzB,MA1DyB,EA2DzB,MA3DyB,EA4DzB,MA5DyB,EA6DzB,MA7DyB,EA8DzB,MA9DyB,EA+DzB,MA/DyB,EAgEzB,MAhEyB,EAiEzB,MAjEyB,EAkEzB,MAlEyB,EAmEzB,MAnEyB,EAoEzB,MApEyB,CAA3B;;AAwEA,MAAMC,kBAAkB,GAAGV,SAAS,IAAIV,OAAO,CAACU,SAAD,EAAYS,kBAAZ,CAA/C;;;;;;;;AAOA,MAAME,eAAe,GAAG,CACtB,MADsB,EAEtB,MAFsB,EAGtB,MAHsB,EAItB,MAJsB,EAKtB,MALsB,EAMtB,MANsB,EAOtB,MAPsB,EAQtB,MARsB,EAStB,MATsB,EAUtB,MAVsB,EAWtB,MAXsB,EAYtB,MAZsB,EAatB,MAbsB,EActB,MAdsB,EAetB,MAfsB,EAgBtB,MAhBsB,EAiBtB,MAjBsB,EAkBtB,MAlBsB,EAmBtB,MAnBsB,EAoBtB,MApBsB,EAqBtB,MArBsB,EAsBtB,MAtBsB,EAuBtB,MAvBsB,EAwBtB,MAxBsB,EAyBtB,MAzBsB,EA0BtB,MA1BsB,EA2BtB,MA3BsB,EA4BtB,MA5BsB,EA6BtB,MA7BsB,EA8BtB,MA9BsB,EA+BtB,MA/BsB,EAgCtB,MAhCsB,EAiCtB,MAjCsB,EAkCtB,MAlCsB,EAmCtB,MAnCsB,EAoCtB,MApCsB,EAqCtB,MArCsB,EAsCtB,MAtCsB,EAuCtB,MAvCsB,EAwCtB,MAxCsB,EAyCtB,MAzCsB,EA0CtB,MA1CsB,EA2CtB,MA3CsB,EA4CtB,MA5CsB,EA6CtB,MA7CsB,EA8CtB,MA9CsB,EA+CtB,MA/CsB,EAgDtB,MAhDsB,EAiDtB,MAjDsB,EAkDtB,MAlDsB,EAmDtB,MAnDsB,EAoDtB,MApDsB,EAqDtB,MArDsB,EAsDtB,MAtDsB,EAuDtB,MAvDsB,EAwDtB,MAxDsB,EAyDtB,MAzDsB,EA0DtB,MA1DsB,EA2DtB,MA3DsB,EA4DtB,MA5DsB,EA6DtB,MA7DsB,EA8DtB,MA9DsB,EA+DtB,MA/DsB,EAgEtB,MAhEsB,EAiEtB,MAjEsB,EAkEtB,MAlEsB,EAmEtB,MAnEsB,EAoEtB,MApEsB,EAqEtB,MArEsB,EAsEtB,MAtEsB,EAuEtB,MAvEsB,EAwEtB,MAxEsB,EAyEtB,MAzEsB,EA0EtB,MA1EsB,EA2EtB,MA3EsB,EA4EtB,MA5EsB,EA6EtB,MA7EsB,EA8EtB,MA9EsB,EA+EtB,MA/EsB,EAgFtB,MAhFsB,EAiFtB,MAjFsB,EAkFtB,MAlFsB,EAmFtB,MAnFsB,EAoFtB,MApFsB,EAqFtB,MArFsB,EAsFtB,MAtFsB,EAuFtB,MAvFsB,EAwFtB,MAxFsB,EAyFtB,MAzFsB,EA0FtB,MA1FsB,EA2FtB,MA3FsB,EA4FtB,MA5FsB,EA6FtB,MA7FsB,EA8FtB,MA9FsB,EA+FtB,MA/FsB,EAgGtB,MAhGsB,EAiGtB,MAjGsB,EAkGtB,MAlGsB,EAmGtB,MAnGsB,EAoGtB,MApGsB,EAqGtB,MArGsB,EAsGtB,MAtGsB,EAuGtB,MAvGsB,EAwGtB,MAxGsB,EAyGtB,MAzGsB,EA0GtB,MA1GsB,EA2GtB,MA3GsB,EA4GtB,MA5GsB,EA6GtB,MA7GsB,EA8GtB,MA9GsB,EA+GtB,MA/GsB,EAgHtB,MAhHsB,EAiHtB,MAjHsB,EAkHtB,MAlHsB,EAmHtB,MAnHsB,EAoHtB,MApHsB,EAqHtB,MArHsB,EAsHtB,MAtHsB,EAuHtB,MAvHsB,EAwHtB,MAxHsB,EAyHtB,MAzHsB,EA0HtB,MA1HsB,EA2HtB,MA3HsB,EA4HtB,MA5HsB,EA6HtB,MA7HsB,EA8HtB,MA9HsB,EA+HtB,MA/HsB,EAgItB,MAhIsB,EAiItB,MAjIsB,EAkItB,MAlIsB,EAmItB,MAnIsB,EAoItB,MApIsB,EAqItB,MArIsB,EAsItB,MAtIsB,EAuItB,MAvIsB,EAwItB,MAxIsB,EAyItB,MAzIsB,EA0ItB,MA1IsB,EA2ItB,MA3IsB,EA4ItB,MA5IsB,EA6ItB,MA7IsB,EA8ItB,MA9IsB,EA+ItB,MA/IsB,EAgJtB,MAhJsB,EAiJtB,MAjJsB,EAkJtB,MAlJsB,EAmJtB,MAnJsB,EAoJtB,MApJsB,EAqJtB,MArJsB,EAsJtB,MAtJsB,EAuJtB,MAvJsB,EAwJtB,MAxJsB,EAyJtB,MAzJsB,EA0JtB,MA1JsB,EA2JtB,MA3JsB,EA4JtB,MA5JsB,EA6JtB,MA7JsB,EA8JtB,MA9JsB,EA+JtB,MA/JsB,EAgKtB,MAhKsB,EAiKtB,MAjKsB,EAkKtB,MAlKsB,EAmKtB,MAnKsB,EAoKtB,MApKsB,EAqKtB,MArKsB,EAsKtB,MAtKsB,EAuKtB,MAvKsB,EAwKtB,MAxKsB,EAyKtB,MAzKsB,EA0KtB,MA1KsB,EA2KtB,MA3KsB,EA4KtB,MA5KsB,EA6KtB,MA7KsB,EA8KtB,MA9KsB,EA+KtB,MA/KsB,EAgLtB,MAhLsB,EAiLtB,MAjLsB,EAkLtB,MAlLsB,EAmLtB,MAnLsB,EAoLtB,MApLsB,EAqLtB,MArLsB,EAsLtB,MAtLsB,EAuLtB,MAvLsB,EAwLtB,MAxLsB,EAyLtB,MAzLsB,EA0LtB,MA1LsB,EA2LtB,MA3LsB,EA4LtB,MA5LsB,EA6LtB,MA7LsB,EA8LtB,MA9LsB,EA+LtB,MA/LsB,EAgMtB,MAhMsB,EAiMtB,MAjMsB,EAkMtB,MAlMsB,EAmMtB,MAnMsB,EAoMtB,MApMsB,EAqMtB,MArMsB,EAsMtB,MAtMsB,EAuMtB,MAvMsB,EAwMtB,MAxMsB,EAyMtB,MAzMsB,EA0MtB,MA1MsB,EA2MtB,MA3MsB,EA4MtB,MA5MsB,EA6MtB,MA7MsB,EA8MtB,MA9MsB,EA+MtB,MA/MsB,EAgNtB,MAhNsB,EAiNtB,MAjNsB,EAkNtB,MAlNsB,EAmNtB,MAnNsB,EAoNtB,MApNsB,EAqNtB,MArNsB,EAsNtB,MAtNsB,EAuNtB,MAvNsB,EAwNtB,MAxNsB,EAyNtB,MAzNsB,EA0NtB,MA1NsB,EA2NtB,MA3NsB,EA4NtB,MA5NsB,EA6NtB,MA7NsB,EA8NtB,MA9NsB,EA+NtB,MA/NsB,EAgOtB,MAhOsB,EAiOtB,MAjOsB,EAkOtB,MAlOsB,EAmOtB,MAnOsB,EAoOtB,MApOsB,EAqOtB,MArOsB,EAsOtB,MAtOsB,EAuOtB,MAvOsB,EAwOtB,MAxOsB,EAyOtB,MAzOsB,EA0OtB,MA1OsB,EA2OtB,MA3OsB,EA4OtB,MA5OsB,EA6OtB,MA7OsB,EA8OtB,MA9OsB,EA+OtB,MA/OsB,EAgPtB,MAhPsB,EAiPtB,MAjPsB,EAkPtB,MAlPsB,EAmPtB,MAnPsB,EAoPtB,MApPsB,EAqPtB,MArPsB,EAsPtB,MAtPsB,EAuPtB,MAvPsB,EAwPtB,MAxPsB,EAyPtB,MAzPsB,EA0PtB,MA1PsB,EA2PtB,MA3PsB,EA4PtB,MA5PsB,EA6PtB,MA7PsB,EA8PtB,MA9PsB,EA+PtB,MA/PsB,EAgQtB,MAhQsB,EAiQtB,MAjQsB,EAkQtB,MAlQsB,EAmQtB,MAnQsB,EAoQtB,MApQsB,EAqQtB,MArQsB,EAsQtB,MAtQsB,EAuQtB,MAvQsB,EAwQtB,MAxQsB,EAyQtB,MAzQsB,EA0QtB,MA1QsB,EA2QtB,MA3QsB,EA4QtB,MA5QsB,EA6QtB,MA7QsB,EA8QtB,MA9QsB,EA+QtB,MA/QsB,EAgRtB,MAhRsB,EAiRtB,MAjRsB,EAkRtB,MAlRsB,EAmRtB,MAnRsB,EAoRtB,MApRsB,EAqRtB,MArRsB,EAsRtB,MAtRsB,EAuRtB,MAvRsB,EAwRtB,MAxRsB,EAyRtB,MAzRsB,EA0RtB,MA1RsB,EA2RtB,MA3RsB,EA4RtB,MA5RsB,EA6RtB,MA7RsB,EA8RtB,MA9RsB,EA+RtB,MA/RsB,EAgStB,MAhSsB,EAiStB,MAjSsB,EAkStB,MAlSsB,EAmStB,MAnSsB,EAoStB,MApSsB,EAqStB,MArSsB,EAsStB,MAtSsB,EAuStB,MAvSsB,EAwStB,MAxSsB,EAyStB,MAzSsB,EA0StB,MA1SsB,EA2StB,MA3SsB,EA4StB,MA5SsB,EA6StB,MA7SsB,EA8StB,MA9SsB,EA+StB,MA/SsB,EAgTtB,MAhTsB,EAiTtB,MAjTsB,EAkTtB,MAlTsB,EAmTtB,MAnTsB,EAoTtB,MApTsB,EAqTtB,MArTsB,EAsTtB,MAtTsB,EAuTtB,MAvTsB,EAwTtB,MAxTsB,EAyTtB,MAzTsB,EA0TtB,MA1TsB,EA2TtB,MA3TsB,EA4TtB,MA5TsB,EA6TtB,MA7TsB,EA8TtB,MA9TsB,EA+TtB,MA/TsB,EAgUtB,MAhUsB,EAiUtB,MAjUsB,EAkUtB,MAlUsB,EAmUtB,MAnUsB,EAoUtB,MApUsB,EAqUtB,MArUsB,EAsUtB,MAtUsB,EAuUtB,MAvUsB,EAwUtB,MAxUsB,EAyUtB,MAzUsB,EA0UtB,MA1UsB,EA2UtB,MA3UsB,EA4UtB,MA5UsB,EA6UtB,MA7UsB,EA8UtB,MA9UsB,EA+UtB,MA/UsB,EAgVtB,MAhVsB,EAiVtB,MAjVsB,EAkVtB,MAlVsB,EAmVtB,MAnVsB,EAoVtB,MApVsB,EAqVtB,MArVsB,EAsVtB,MAtVsB,EAuVtB,MAvVsB,EAwVtB,MAxVsB,EAyVtB,MAzVsB,EA0VtB,MA1VsB,EA2VtB,MA3VsB,EA4VtB,MA5VsB,EA6VtB,MA7VsB,EA8VtB,MA9VsB,EA+VtB,MA/VsB,EAgWtB,MAhWsB,EAiWtB,MAjWsB,EAkWtB,MAlWsB,EAmWtB,MAnWsB,EAoWtB,MApWsB,EAqWtB,MArWsB,EAsWtB,MAtWsB,EAuWtB,MAvWsB,EAwWtB,MAxWsB,EAyWtB,MAzWsB,EA0WtB,MA1WsB,EA2WtB,MA3WsB,EA4WtB,MA5WsB,EA6WtB,MA7WsB,EA8WtB,MA9WsB,EA+WtB,MA/WsB,EAgXtB,MAhXsB,EAiXtB,MAjXsB,EAkXtB,MAlXsB,EAmXtB,MAnXsB,EAoXtB,MApXsB,EAqXtB,MArXsB,EAsXtB,MAtXsB,EAuXtB,MAvXsB,EAwXtB,MAxXsB,EAyXtB,MAzXsB,EA0XtB,MA1XsB,EA2XtB,MA3XsB,EA4XtB,MA5XsB,EA6XtB,MA7XsB,EA8XtB,MA9XsB,EA+XtB,MA/XsB,EAgYtB,MAhYsB,EAiYtB,MAjYsB,EAkYtB,MAlYsB,EAmYtB,MAnYsB,EAoYtB,MApYsB,EAqYtB,MArYsB,EAsYtB,MAtYsB,EAuYtB,MAvYsB,EAwYtB,MAxYsB,EAyYtB,MAzYsB,EA0YtB,MA1YsB,EA2YtB,MA3YsB,EA4YtB,MA5YsB,EA6YtB,MA7YsB,EA8YtB,MA9YsB,EA+YtB,MA/YsB,EAgZtB,MAhZsB,EAiZtB,MAjZsB,EAkZtB,MAlZsB,EAmZtB,MAnZsB,EAoZtB,MApZsB,EAqZtB,MArZsB,EAsZtB,MAtZsB,EAuZtB,MAvZsB,EAwZtB,MAxZsB,EAyZtB,MAzZsB,EA0ZtB,MA1ZsB,EA2ZtB,MA3ZsB,EA4ZtB,MA5ZsB,EA6ZtB,MA7ZsB,EA8ZtB,MA9ZsB,EA+ZtB,MA/ZsB,EAgatB,MAhasB,EAiatB,MAjasB,EAkatB,MAlasB,EAmatB,MAnasB,EAoatB,MApasB,EAqatB,MArasB,EAsatB,MAtasB,EAuatB,MAvasB,EAwatB,MAxasB,EAyatB,MAzasB,EA0atB,MA1asB,EA2atB,MA3asB,EA4atB,MA5asB,EA6atB,MA7asB,EA8atB,MA9asB,EA+atB,MA/asB,EAgbtB,MAhbsB,EAibtB,MAjbsB,EAkbtB,MAlbsB,EAmbtB,MAnbsB,EAobtB,MApbsB,EAqbtB,MArbsB,EAsbtB,MAtbsB,EAubtB,MAvbsB,EAwbtB,MAxbsB,EAybtB,MAzbsB,EA0btB,MA1bsB,EA2btB,MA3bsB,EA4btB,MA5bsB,EA6btB,MA7bsB,EA8btB,MA9bsB,EA+btB,MA/bsB,EAgctB,MAhcsB,EAictB,MAjcsB,EAkctB,MAlcsB,EAmctB,MAncsB,EAoctB,MApcsB,EAqctB,MArcsB,EAsctB,MAtcsB,EAuctB,MAvcsB,EAwctB,MAxcsB,EAyctB,MAzcsB,EA0ctB,MA1csB,EA2ctB,MA3csB,EA4ctB,MA5csB,EA6ctB,MA7csB,EA8ctB,MA9csB,EA+ctB,MA/csB,EAgdtB,MAhdsB,EAidtB,MAjdsB,EAkdtB,MAldsB,EAmdtB,MAndsB,EAodtB,MApdsB,EAqdtB,MArdsB,EAsdtB,MAtdsB,EAudtB,MAvdsB,EAwdtB,MAxdsB,EAydtB,MAzdsB,EA0dtB,MA1dsB,EA2dtB,MA3dsB,EA4dtB,MA5dsB,EA6dtB,MA7dsB,EA8dtB,MA9dsB,EA+dtB,MA/dsB,EAgetB,MAhesB,EAietB,MAjesB,EAketB,MAlesB,EAmetB,MAnesB,EAoetB,MApesB,EAqetB,MAresB,EAsetB,MAtesB,EAuetB,MAvesB,EAwetB,MAxesB,EAyetB,MAzesB,EA0etB,MA1esB,EA2etB,MA3esB,EA4etB,MA5esB,EA6etB,MA7esB,EA8etB,MA9esB,EA+etB,MA/esB,EAgftB,MAhfsB,EAiftB,MAjfsB,EAkftB,MAlfsB,EAmftB,MAnfsB,EAoftB,MApfsB,EAqftB,MArfsB,EAsftB,MAtfsB,EAuftB,MAvfsB,EAwftB,MAxfsB,EAyftB,MAzfsB,EA0ftB,MA1fsB,EA2ftB,MA3fsB,EA4ftB,MA5fsB,EA6ftB,MA7fsB,EA8ftB,MA9fsB,EA+ftB,MA/fsB,EAggBtB,MAhgBsB,EAigBtB,MAjgBsB,EAkgBtB,MAlgBsB,EAmgBtB,MAngBsB,EAogBtB,MApgBsB,EAqgBtB,MArgBsB,EAsgBtB,MAtgBsB,EAugBtB,MAvgBsB,EAwgBtB,MAxgBsB,EAygBtB,MAzgBsB,EA0gBtB,MA1gBsB,EA2gBtB,MA3gBsB,EA4gBtB,MA5gBsB,EA6gBtB,MA7gBsB,EA8gBtB,MA9gBsB,EA+gBtB,MA/gBsB,EAghBtB,MAhhBsB,EAihBtB,MAjhBsB,EAkhBtB,MAlhBsB,EAmhBtB,MAnhBsB,EAohBtB,MAphBsB,EAqhBtB,MArhBsB,EAshBtB,MAthBsB,EAuhBtB,MAvhBsB,EAwhBtB,MAxhBsB,EAyhBtB,MAzhBsB,EA0hBtB,MA1hBsB,EA2hBtB,MA3hBsB,EA4hBtB,MA5hBsB,EA6hBtB,MA7hBsB,EA8hBtB,MA9hBsB,EA+hBtB,MA/hBsB,EAgiBtB,MAhiBsB,EAiiBtB,MAjiBsB,EAkiBtB,MAliBsB,EAmiBtB,MAniBsB,EAoiBtB,MApiBsB,EAqiBtB,MAriBsB,EAsiBtB,MAtiBsB,EAuiBtB,MAviBsB,EAwiBtB,MAxiBsB,EAyiBtB,MAziBsB,EA0iBtB,MA1iBsB,EA2iBtB,MA3iBsB,EA4iBtB,MA5iBsB,EA6iBtB,MA7iBsB,EA8iBtB,MA9iBsB,EA+iBtB,MA/iBsB,EAgjBtB,MAhjBsB,EAijBtB,MAjjBsB,EAkjBtB,MAljBsB,EAmjBtB,MAnjBsB,EAojBtB,MApjBsB,EAqjBtB,MArjBsB,EAsjBtB,MAtjBsB,EAujBtB,MAvjBsB,EAwjBtB,MAxjBsB,EAyjBtB,MAzjBsB,EA0jBtB,MA1jBsB,EA2jBtB,MA3jBsB,EA4jBtB,MA5jBsB,EA6jBtB,MA7jBsB,EA8jBtB,MA9jBsB,EA+jBtB,MA/jBsB,EAgkBtB,MAhkBsB,EAikBtB,MAjkBsB,EAkkBtB,MAlkBsB,EAmkBtB,MAnkBsB,EAokBtB,MApkBsB,EAqkBtB,MArkBsB,EAskBtB,MAtkBsB,EAukBtB,MAvkBsB,EAwkBtB,MAxkBsB,EAykBtB,MAzkBsB,EA0kBtB,MA1kBsB,EA2kBtB,MA3kBsB,EA4kBtB,MA5kBsB,EA6kBtB,MA7kBsB,EA8kBtB,MA9kBsB,EA+kBtB,MA/kBsB,EAglBtB,MAhlBsB,EAilBtB,MAjlBsB,EAklBtB,MAllBsB,EAmlBtB,MAnlBsB,EAolBtB,MAplBsB,EAqlBtB,MArlBsB,EAslBtB,MAtlBsB,EAulBtB,MAvlBsB,EAwlBtB,MAxlBsB,EAylBtB,MAzlBsB,EA0lBtB,MA1lBsB,EA2lBtB,MA3lBsB,EA4lBtB,MA5lBsB,EA6lBtB,MA7lBsB,EA8lBtB,MA9lBsB,EA+lBtB,MA/lBsB,EAgmBtB,MAhmBsB,EAimBtB,MAjmBsB,EAkmBtB,MAlmBsB,EAmmBtB,MAnmBsB,EAomBtB,MApmBsB,EAqmBtB,MArmBsB,EAsmBtB,MAtmBsB,EAumBtB,MAvmBsB,EAwmBtB,MAxmBsB,EAymBtB,MAzmBsB,EA0mBtB,MA1mBsB,EA2mBtB,MA3mBsB,EA4mBtB,MA5mBsB,EA6mBtB,MA7mBsB,EA8mBtB,MA9mBsB,EA+mBtB,MA/mBsB,EAgnBtB,MAhnBsB,EAinBtB,MAjnBsB,EAknBtB,MAlnBsB,EAmnBtB,MAnnBsB,EAonBtB,MApnBsB,EAqnBtB,MArnBsB,EAsnBtB,MAtnBsB,EAunBtB,MAvnBsB,EAwnBtB,MAxnBsB,EAynBtB,MAznBsB,EA0nBtB,MA1nBsB,EA2nBtB,MA3nBsB,EA4nBtB,MA5nBsB,EA6nBtB,MA7nBsB,EA8nBtB,MA9nBsB,EA+nBtB,MA/nBsB,EAgoBtB,MAhoBsB,EAioBtB,MAjoBsB,EAkoBtB,MAloBsB,EAmoBtB,MAnoBsB,EAooBtB,MApoBsB,EAqoBtB,MAroBsB,EAsoBtB,MAtoBsB,EAuoBtB,OAvoBsB,EAwoBtB,OAxoBsB,EAyoBtB,OAzoBsB,EA0oBtB,OA1oBsB,EA2oBtB,OA3oBsB,EA4oBtB,OA5oBsB,EA6oBtB,OA7oBsB,EA8oBtB,OA9oBsB,EA+oBtB,OA/oBsB,EAgpBtB,OAhpBsB,EAipBtB,OAjpBsB,EAkpBtB,OAlpBsB,EAmpBtB,OAnpBsB,EAopBtB,OAppBsB,EAqpBtB,OArpBsB,EAspBtB,OAtpBsB,EAupBtB,OAvpBsB,EAwpBtB,OAxpBsB,EAypBtB,OAzpBsB,EA0pBtB,OA1pBsB,EA2pBtB,OA3pBsB,EA4pBtB,OA5pBsB,EA6pBtB,OA7pBsB,EA8pBtB,OA9pBsB,EA+pBtB,OA/pBsB,EAgqBtB,OAhqBsB,EAiqBtB,OAjqBsB,EAkqBtB,OAlqBsB,EAmqBtB,OAnqBsB,EAoqBtB,OApqBsB,EAqqBtB,OArqBsB,EAsqBtB,OAtqBsB,EAuqBtB,OAvqBsB,EAwqBtB,OAxqBsB,EAyqBtB,OAzqBsB,EA0qBtB,OA1qBsB,EA2qBtB,OA3qBsB,EA4qBtB,OA5qBsB,EA6qBtB,OA7qBsB,EA8qBtB,OA9qBsB,EA+qBtB,OA/qBsB,EAgrBtB,OAhrBsB,EAirBtB,OAjrBsB,EAkrBtB,OAlrBsB,EAmrBtB,OAnrBsB,EAorBtB,OAprBsB,EAqrBtB,OArrBsB,EAsrBtB,OAtrBsB,EAurBtB,OAvrBsB,EAwrBtB,OAxrBsB,EAyrBtB,OAzrBsB,EA0rBtB,OA1rBsB,EA2rBtB,OA3rBsB,EA4rBtB,OA5rBsB,EA6rBtB,OA7rBsB,EA8rBtB,OA9rBsB,EA+rBtB,OA/rBsB,EAgsBtB,OAhsBsB,EAisBtB,OAjsBsB,EAksBtB,OAlsBsB,EAmsBtB,OAnsBsB,EAosBtB,OApsBsB,EAqsBtB,OArsBsB,EAssBtB,OAtsBsB,EAusBtB,OAvsBsB,EAwsBtB,OAxsBsB,EAysBtB,OAzsBsB,EA0sBtB,OA1sBsB,EA2sBtB,OA3sBsB,EA4sBtB,OA5sBsB,EA6sBtB,OA7sBsB,EA8sBtB,OA9sBsB,EA+sBtB,QA/sBsB,EAgtBtB,QAhtBsB,CAAxB;;AAotBA,MAAMC,gBAAgB,GAAGZ,SAAS,IAAIV,OAAO,CAACU,SAAD,EAAYW,eAAZ,CAA7C;;ACn3DA;;;;;AAIA,MAAME,aAAa,GAAGT,wBAAtB;;;;;;AAMA,MAAMU,eAAe,GAAGZ,yBAAxB;;AAGA,MAAMa,YAAY,GAAGf,SAAS,IAAIA,SAAS,CAACgB,WAAV,CAAsB,CAAtB,CAAlC;;AACA,MAAM7L,KAAK,GAAG8L,CAAC,IAAIA,CAAC,CAAC,CAAD,CAApB;;AACA,MAAM7L,IAAI,GAAG6L,CAAC,IAAIA,CAAC,CAACA,CAAC,CAAC/L,MAAF,GAAW,CAAZ,CAAnB;;;;;;;;;;AASA,SAASgM,YAAT,CAAsBC,KAAtB,EAA6B;QACrBC,UAAU,GAAG,EAAnB;QACMpE,IAAI,GAAGmE,KAAK,CAACjM,MAAnB;;OAEK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4G,IAApB,EAA0B5G,CAAC,IAAI,CAA/B,EAAkC;UAC1BiL,MAAM,GAAGF,KAAK,CAACxK,UAAN,CAAiBP,CAAjB,CAAf;;QAEIiL,MAAM,IAAI,MAAV,IAAoBA,MAAM,IAAI,MAA9B,IAAwCrE,IAAI,GAAG5G,CAAC,GAAG,CAAvD,EAA0D;YAClDkL,IAAI,GAAGH,KAAK,CAACxK,UAAN,CAAiBP,CAAC,GAAG,CAArB,CAAb;;UAEIkL,IAAI,IAAI,MAAR,IAAkBA,IAAI,IAAI,MAA9B,EAAsC;QACpCF,UAAU,CAAC/L,IAAX,CAAgB,CAACgM,MAAM,GAAG,MAAV,IAAoB,KAApB,GAA4BC,IAA5B,GAAmC,MAAnC,GAA4C,OAA5D;QACAlL,CAAC,IAAI,CAAL;;;;;IAKJgL,UAAU,CAAC/L,IAAX,CAAgBgM,MAAhB;;;SAGKD,UAAP;;;;;;;;;;;AAUF,SAASG,QAAT,CAAkBJ,KAAlB,EAAyBK,IAAI,GAAG,EAAhC,EAAoC;MAC9B,OAAOL,KAAP,KAAiB,QAArB,EAA+B;UACvB,IAAIM,SAAJ,CAAc,kBAAd,CAAN;;;MAGEN,KAAK,CAACjM,MAAN,KAAiB,CAArB,EAAwB;WACf,EAAP;GANgC;;;QAU5BwM,YAAY,GAAGR,YAAY,CAACC,KAAD,CAAZ;GAElBvJ,GAFkB,CAEdoI,SAAS,IAAKa,aAAa,CAACb,SAAD,CAAb,GAA2B,IAA3B,GAAkCA,SAFlC;GAIlB2B,MAJkB,CAIX3B,SAAS,IAAI,CAACc,eAAe,CAACd,SAAD,CAJlB,CAArB,CAVkC;;QAiB5B4B,gBAAgB,GAAGpL,MAAM,CAACqL,aAAP,CACtBC,KADsB,CAChB,IADgB,EACVJ,YADU,EAEtBK,SAFsB,CAEZ,MAFY,CAAzB;QAIMC,cAAc,GAAGd,YAAY,CAACU,gBAAD,CAAnC,CArBkC;;QAwB5BK,aAAa,GAAGD,cAAc,CAACE,IAAf,CAAoB1B,qBAApB,CAAtB;;MAEIyB,aAAJ,EAAmB;UACX,IAAIjO,KAAJ,CACJ,2EADI,CAAN;GA3BgC;;;MAiC9BwN,IAAI,CAACW,eAAL,KAAyB,IAA7B,EAAmC;UAC3BC,aAAa,GAAGJ,cAAc,CAACE,IAAf,CAAoBnC,qBAApB,CAAtB;;QAEIqC,aAAJ,EAAmB;YACX,IAAIpO,KAAJ,CACJ,4EADI,CAAN;;GArC8B;;;QA6C5BqO,UAAU,GAAGL,cAAc,CAACE,IAAf,CAAoBxB,kBAApB,CAAnB;QAEM4B,QAAQ,GAAGN,cAAc,CAACE,IAAf,CAAoBtB,gBAApB,CAAjB,CA/CkC;;;MAmD9ByB,UAAU,IAAIC,QAAlB,EAA4B;UACpB,IAAItO,KAAJ,CACJ,iEACE,oDAFE,CAAN;;;;;;;;;QAYIuO,cAAc,GAAG7B,kBAAkB,CACvCK,YAAY,CAAC5L,KAAK,CAACyM,gBAAD,CAAN,CAD2B,CAAzC;QAGMY,aAAa,GAAG9B,kBAAkB,CACtCK,YAAY,CAAC3L,IAAI,CAACwM,gBAAD,CAAL,CAD0B,CAAxC;;MAIIS,UAAU,IAAI,EAAEE,cAAc,IAAIC,aAApB,CAAlB,EAAsD;UAC9C,IAAIxO,KAAJ,CACJ,qEACE,6EAFE,CAAN;;;SAMK4N,gBAAP;;;AC/IF;;;;AAKA;AAGA,MAAMa,WAAN,CAAkB;SACTC,cAAP,CAAsBC,IAAI,GAAG,EAA7B,EAAiC;QAC3BC,OAAO,GAAI,GAAED,IAAI,CAACE,YAAL,CAAkBC,OAAlB,EAA4B,IAA7C;;SAEK,IAAIvO,GAAT,IAAgBoO,IAAhB,EAAsB;;UAEhB,CAACA,IAAI,CAACI,cAAL,CAAoBxO,GAApB,CAAL,EAA+B;;;;MAG/BqO,OAAO,IAAK,GAAErO,GAAI,KAAIoO,IAAI,CAACpO,GAAD,CAAJ,CAAUwC,OAAV,EAAoB,IAA1C;;;WAGKiM,iBAAiB,CAACC,QAAQ,CAACC,GAAT,CAAaN,OAAb,CAAD,CAAxB;;;SAGKO,uBAAP,CAA+BC,KAA/B,EAAsC;WAC7BH,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBC,MAAvB,CAA8BH,KAA9B,CAAP;;;SAGKI,MAAP,CAAcpL,QAAd,EAAwBjE,OAAO,GAAG,EAAlC,EAAsC;QAChC,CAACA,OAAO,CAACsP,aAAT,IAA0B,CAACtP,OAAO,CAACuP,YAAvC,EAAqD;aAC5C,IAAP;;;WAEK,IAAIjB,WAAJ,CAAgBrK,QAAhB,EAA0BjE,OAA1B,CAAP;;;EAGFD,WAAW,CAACkE,QAAD,EAAWjE,OAAO,GAAG,EAArB,EAAyB;QAC9B,CAACA,OAAO,CAACsP,aAAT,IAA0B,CAACtP,OAAO,CAACuP,YAAvC,EAAqD;YAC7C,IAAI1P,KAAJ,CAAU,sDAAV,CAAN;;;SAGGoE,QAAL,GAAgBA,QAAhB;;SACKuL,gBAAL,CAAsBxP,OAAtB;;;EAGFwP,gBAAgB,CAACxP,OAAD,EAAU;YAChBA,OAAO,CAACyP,UAAhB;WACO,KAAL;WACK,KAAL;aACOC,OAAL,GAAe,CAAf;;;WAEG,KAAL;WACK,KAAL;aACOA,OAAL,GAAe,CAAf;;;WAEG,SAAL;aACOA,OAAL,GAAe,CAAf;;;;aAGKA,OAAL,GAAe,CAAf;;;;UAIEC,OAAO,GAAG;MACdrL,MAAM,EAAE;KADV;;YAIQ,KAAKoL,OAAb;WACO,CAAL;WACK,CAAL;WACK,CAAL;aACOE,sBAAL,CAA4B,KAAKF,OAAjC,EAA0CC,OAA1C,EAAmD3P,OAAnD;;;;WAEG,CAAL;aACO6P,kBAAL,CAAwBF,OAAxB,EAAiC3P,OAAjC;;;;;SAICyJ,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,CAAkBqG,OAAlB,CAAlB;;;EAGFC,sBAAsB,CAACE,CAAD,EAAIH,OAAJ,EAAa3P,OAAb,EAAsB;QACtC+P,CAAJ,EAAOC,WAAP;;YACQF,CAAR;WACO,CAAL;QACEC,CAAC,GAAG,CAAJ;aACKE,OAAL,GAAe,EAAf;QACAD,WAAW,GAAGE,gBAAgB,CAAClQ,OAAO,CAACgQ,WAAT,CAA9B;;;WAEG,CAAL;QACED,CAAC,GAAG,CAAJ;aACKE,OAAL,GAAe,GAAf;QACAD,WAAW,GAAGG,gBAAgB,CAACnQ,OAAO,CAACgQ,WAAT,CAA9B;;;WAEG,CAAL;QACED,CAAC,GAAG,CAAJ;aACKE,OAAL,GAAe,GAAf;QACAD,WAAW,GAAGG,gBAAgB,CAACnQ,OAAO,CAACgQ,WAAT,CAA9B;;;;UAIEI,kBAAkB,GAAGC,qBAAqB,CAACrQ,OAAO,CAACuP,YAAT,CAAhD;UACMe,mBAAmB,GAAGtQ,OAAO,CAACsP,aAAR,GACxBe,qBAAqB,CAACrQ,OAAO,CAACsP,aAAT,CADG,GAExBc,kBAFJ;UAIMG,kBAAkB,GAAGC,sBAAsB,CAC/CT,CAD+C,EAE/C,KAAKE,OAF0C,EAG/CG,kBAH+C,EAI/CE,mBAJ+C,CAAjD;SAMKG,aAAL,GAAqBC,sBAAsB,CACzCX,CADyC,EAEzC,KAAKE,OAFoC,EAGzC,KAAKhM,QAAL,CAAc0M,GAH2B,EAIzCP,kBAJyC,EAKzCG,kBALyC,EAMzCP,WANyC,CAA3C;QAQIY,iBAAJ;;QACIb,CAAC,KAAK,CAAV,EAAa;MACXa,iBAAiB,GAAGC,iBAAiB,CAAC,KAAKJ,aAAN,CAArC;KADF,MAEO;MACLG,iBAAiB,GAAGE,mBAAmB,CACrC,KAAK7M,QAAL,CAAc0M,GADuB,EAErC,KAAKF,aAFgC,CAAvC;;;IAMFd,OAAO,CAACoB,CAAR,GAAYjB,CAAZ;;QACIA,CAAC,IAAI,CAAT,EAAY;MACVH,OAAO,CAAChL,MAAR,GAAiB,KAAKsL,OAAtB;;;QAEEH,CAAC,KAAK,CAAV,EAAa;MACXH,OAAO,CAACqB,EAAR,GAAa;QACXC,KAAK,EAAE;UACLC,SAAS,EAAE,SADN;UAELC,GAAG,EAAE,OAFA;UAGLxM,MAAM,EAAE,KAAKsL,OAAL,GAAe;;OAJ3B;MAOAN,OAAO,CAACyB,IAAR,GAAe,OAAf;MACAzB,OAAO,CAAC0B,IAAR,GAAe,OAAf;;;IAEF1B,OAAO,CAAC2B,CAAR,GAAYvB,CAAZ;IACAJ,OAAO,CAAC4B,CAAR,GAAY1C,iBAAiB,CAAC0B,kBAAD,CAA7B;IACAZ,OAAO,CAAC6B,CAAR,GAAY3C,iBAAiB,CAAC+B,iBAAD,CAA7B;IACAjB,OAAO,CAAC8B,CAAR,GAAYzB,WAAZ;;;EAGFH,kBAAkB,CAACF,OAAD,EAAU3P,OAAV,EAAmB;SAC9BiQ,OAAL,GAAe,GAAf;UACMD,WAAW,GAAGG,gBAAgB,CAACnQ,OAAO,CAACgQ,WAAT,CAApC;UAEM0B,qBAAqB,GAAGC,iBAAiB,CAAC3R,OAAO,CAACuP,YAAT,CAA/C;UACMqC,sBAAsB,GAAG5R,OAAO,CAACsP,aAAR,GAC3BqC,iBAAiB,CAAC3R,OAAO,CAACsP,aAAT,CADU,GAE3BoC,qBAFJ;SAIKjB,aAAL,GAAqBoB,kBAAkB,CACrCvD,WAAW,CAACU,uBADyB,CAAvC;UAGM4B,iBAAiB,GAAGkB,iBAAiB,CACzCJ,qBADyC,EAEzCpD,WAAW,CAACU,uBAF6B,CAA3C;UAIM+C,WAAW,GAAGjD,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAClBuB,iBAAiB,CAACoB,KAAlB,CAAwBrQ,KAAxB,CAA8B,EAA9B,EAAkC,EAAlC,CADkB,EAElB,CAFkB,CAApB;UAIMsQ,sBAAsB,GAAGC,sBAAsB,CACnDR,qBADmD,EAEnDK,WAFmD,EAGnD,KAAKtB,aAH8C,CAArD;UAKMF,kBAAkB,GAAG4B,kBAAkB,CAC3CP,sBAD2C,EAE3ChB,iBAF2C,EAG3CtC,WAAW,CAACU,uBAH+B,CAA7C;UAKMoD,YAAY,GAAGtD,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CACnBkB,kBAAkB,CAACyB,KAAnB,CAAyBrQ,KAAzB,CAA+B,EAA/B,EAAmC,EAAnC,CADmB,EAEnB,CAFmB,CAArB;UAIM0Q,uBAAuB,GAAGC,uBAAuB,CACrDV,sBADqD,EAErDQ,YAFqD,EAGrDxB,iBAHqD,EAIrD,KAAKH,aAJgD,CAAvD;UAMM8B,UAAU,GAAGC,yBAAyB,CAC1CxC,WAD0C,EAE1C,KAAKS,aAFqC,EAG1CnC,WAAW,CAACU,uBAH8B,CAA5C;IAMAW,OAAO,CAACoB,CAAR,GAAY,CAAZ;IACApB,OAAO,CAAChL,MAAR,GAAiB,KAAKsL,OAAtB;IACAN,OAAO,CAACqB,EAAR,GAAa;MACXC,KAAK,EAAE;QACLC,SAAS,EAAE,SADN;QAELC,GAAG,EAAE,OAFA;QAGLxM,MAAM,EAAE,KAAKsL,OAAL,GAAe;;KAJ3B;IAOAN,OAAO,CAACyB,IAAR,GAAe,OAAf;IACAzB,OAAO,CAAC0B,IAAR,GAAe,OAAf;IACA1B,OAAO,CAAC2B,CAAR,GAAY,CAAZ;IACA3B,OAAO,CAAC4B,CAAR,GAAY1C,iBAAiB,CAAC0B,kBAAD,CAA7B;IACAZ,OAAO,CAAC8C,EAAR,GAAa5D,iBAAiB,CAACwD,uBAAD,CAA9B;IACA1C,OAAO,CAAC6B,CAAR,GAAY3C,iBAAiB,CAAC+B,iBAAD,CAA7B;IACAjB,OAAO,CAAC+C,EAAR,GAAa7D,iBAAiB,CAACoD,sBAAD,CAA9B;IACAtC,OAAO,CAAC8B,CAAR,GAAYzB,WAAZ;IACAL,OAAO,CAACgD,KAAR,GAAgB9D,iBAAiB,CAAC0D,UAAD,CAAjC;;;EAGFvN,YAAY,CAAC4N,GAAD,EAAMxO,GAAN,EAAW;QACjByO,MAAJ;;QACI,KAAKnD,OAAL,GAAe,CAAnB,EAAsB;MACpBmD,MAAM,GAAG,KAAKpC,aAAL,CACNqC,KADM,GAEN7N,MAFM,CAGL6J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CACE,CACG,CAACuD,GAAG,GAAG,IAAP,KAAgB,EAAjB,GACG,CAACA,GAAG,GAAG,MAAP,KAAkB,CADrB,GAEIA,GAAG,IAAI,CAAR,GAAa,MAFhB,GAGGxO,GAAG,GAAG,IAJX,EAKE,CAACA,GAAG,GAAG,MAAP,KAAkB,EALpB,CADF,EAQE,CARF,CAHK,CAAT;;;QAgBE,KAAKsL,OAAL,KAAiB,CAAjB,IAAsB,KAAKA,OAAL,KAAiB,CAA3C,EAA8C;UACxCtP,GAAG,GAAG0O,QAAQ,CAACC,GAAT,CAAa8D,MAAb,CAAV;MACAzS,GAAG,CAAC2S,QAAJ,GAAejP,IAAI,CAACkP,GAAL,CAAS,EAAT,EAAa,KAAK/C,OAAL,GAAe,CAAf,GAAmB,CAAhC,CAAf;aACOzL,MAAM,IACXqK,iBAAiB,CACfC,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqBpE,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B7K,MAA9B,CAArB,EAA4DpE,GAA5D,EACG+S,UAFY,CADnB;;;QAOE/S,GAAJ;;QACI,KAAKsP,OAAL,KAAiB,CAArB,EAAwB;MACtBtP,GAAG,GAAG0O,QAAQ,CAACC,GAAT,CACJ8D,MAAM,CAAC5N,MAAP,CAAc6J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,CAAC,UAAD,CAA9B,EAA4C,CAA5C,CAAd,CADI,CAAN;KADF,MAIO;MACLjP,GAAG,GAAG,KAAKqQ,aAAX;;;UAGI2C,EAAE,GAAG9E,WAAW,CAACU,uBAAZ,CAAoC,EAApC,CAAX;UACMhP,OAAO,GAAG;MACdqT,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcC,GADN;MAEdC,OAAO,EAAEzE,QAAQ,CAACtN,GAAT,CAAagS,KAFR;MAGdJ;KAHF;WAMO5O,MAAM,IACXqK,iBAAiB,CACfuE,EAAE,CACCN,KADH,GAEG7N,MAFH,CAGI6J,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CACEpE,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B7K,MAA9B,CADF,EAEEpE,GAFF,EAGEJ,OAHF,EAIEmT,UAPN,CADe,CADnB;;;EAcFjR,GAAG,GAAG;SACCuH,UAAL,CAAgBvH,GAAhB;;;;;AAIJ,SAASgO,gBAAT,CAA0BwD,gBAAgB,GAAG,EAA7C,EAAiD;MAC3C1D,WAAW,GAAG,cAAc,CAAhC;;MACI0D,gBAAgB,CAACC,QAArB,EAA+B;IAC7B3D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACE,SAArB,EAAgC;IAC9B5D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACG,OAArB,EAA8B;IAC5B7D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACI,UAArB,EAAiC;IAC/B9D,WAAW,IAAI,cAAf;;;SAEKA,WAAP;;;AAGF,SAASG,gBAAT,CAA0BuD,gBAAgB,GAAG,EAA7C,EAAiD;MAC3C1D,WAAW,GAAG,cAAc,CAAhC;;MACI0D,gBAAgB,CAACC,QAAjB,KAA8B,eAAlC,EAAmD;IACjD3D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACC,QAAjB,KAA8B,gBAAlC,EAAoD;IAClD3D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACE,SAArB,EAAgC;IAC9B5D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACG,OAArB,EAA8B;IAC5B7D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACI,UAArB,EAAiC;IAC/B9D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACK,YAArB,EAAmC;IACjC/D,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACM,oBAArB,EAA2C;IACzChE,WAAW,IAAI,cAAf;;;MAEE0D,gBAAgB,CAACO,gBAArB,EAAuC;IACrCjE,WAAW,IAAI,cAAf;;;SAEKA,WAAP;;;AAGF,SAASa,iBAAT,CAA2BJ,aAA3B,EAA0C;SACjC3B,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqB7C,qBAAqB,EAA1C,EAA8CI,aAA9C,EACJ0C,UADH;;;AAIF,SAASrC,mBAAT,CAA6BoD,UAA7B,EAAyCzD,aAAzC,EAAwD;QAChDrQ,GAAG,GAAGqQ,aAAa,CAACqC,KAAd,EAAZ;MACIqB,MAAM,GAAGrF,QAAQ,CAACC,GAAT,CACXsB,qBAAqB,GAAGpL,MAAxB,CAA+B6J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B6E,UAA9B,CAA/B,CADW,CAAb;;OAGK,IAAIjS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;UACrBmS,QAAQ,GAAGtQ,IAAI,CAACuQ,IAAL,CAAUjU,GAAG,CAAC2S,QAAJ,GAAe,CAAzB,CAAjB;;SACK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAApB,EAA8BE,CAAC,EAA/B,EAAmC;MACjClU,GAAG,CAAC4R,KAAJ,CAAUsC,CAAV,IACE7D,aAAa,CAACuB,KAAd,CAAoBsC,CAApB,KAA0BrS,CAAC,GAAIA,CAAC,IAAI,CAAV,GAAgBA,CAAC,IAAI,EAArB,GAA4BA,CAAC,IAAI,EAA3D,CADF;;;IAGFkS,MAAM,GAAGrF,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqBiB,MAArB,EAA6B/T,GAA7B,EAAkC+S,UAA3C;;;SAEKgB,MAAM,CAAClP,MAAP,CAAc6J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,IAA9B,EAAoC,EAApC,CAAd,CAAP;;;AAGF,SAASmB,sBAAT,CACET,CADF,EAEEE,OAFF,EAGEG,kBAHF,EAIEE,mBAJF,EAKE;MACIuC,MAAM,GAAGvC,mBAAb;MACIvM,KAAK,GAAGgM,CAAC,IAAI,CAAL,GAAS,EAAT,GAAc,CAA1B;;OACK,IAAI9N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,KAApB,EAA2B9B,CAAC,EAA5B,EAAgC;IAC9B4Q,MAAM,GAAG/D,QAAQ,CAACC,GAAT,CAAa8D,MAAb,CAAT;;;QAGIzS,GAAG,GAAGyS,MAAM,CAACC,KAAP,EAAZ;EACA1S,GAAG,CAAC2S,QAAJ,GAAe9C,OAAO,GAAG,CAAzB;MACIkE,MAAM,GAAG/D,kBAAb;EACArM,KAAK,GAAGgM,CAAC,IAAI,CAAL,GAAS,EAAT,GAAc,CAAtB;;OACK,IAAI9N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,KAApB,EAA2B9B,CAAC,EAA5B,EAAgC;UACxBmS,QAAQ,GAAGtQ,IAAI,CAACuQ,IAAL,CAAUjU,GAAG,CAAC2S,QAAJ,GAAe,CAAzB,CAAjB;;SACK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAApB,EAA8BE,CAAC,EAA/B,EAAmC;MACjClU,GAAG,CAAC4R,KAAJ,CAAUsC,CAAV,IAAezB,MAAM,CAACb,KAAP,CAAasC,CAAb,KAAmBrS,CAAC,GAAIA,CAAC,IAAI,CAAV,GAAgBA,CAAC,IAAI,EAArB,GAA4BA,CAAC,IAAI,EAApD,CAAf;;;IAEFkS,MAAM,GAAGrF,QAAQ,CAACmE,GAAT,CAAaC,OAAb,CAAqBiB,MAArB,EAA6B/T,GAA7B,EAAkC+S,UAA3C;;;SAEKgB,MAAP;;;AAGF,SAASzD,sBAAT,CACEX,CADF,EAEEE,OAFF,EAGEiE,UAHF,EAIE9D,kBAJF,EAKEG,kBALF,EAMEP,WANF,EAOE;MACI5P,GAAG,GAAGgQ,kBAAkB,CACzB0C,KADO,GAEP7N,MAFO,CAEAsL,kBAFA,EAGPtL,MAHO,CAGA6J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,CAACkF,YAAY,CAACvE,WAAD,CAAb,CAA9B,EAA2D,CAA3D,CAHA,EAIP/K,MAJO,CAIA6J,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B6E,UAA9B,CAJA,CAAV;QAKMnQ,KAAK,GAAGgM,CAAC,IAAI,CAAL,GAAS,EAAT,GAAc,CAA5B;;OACK,IAAI9N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,KAApB,EAA2B9B,CAAC,EAA5B,EAAgC;IAC9B7B,GAAG,GAAG0O,QAAQ,CAACC,GAAT,CAAa3O,GAAb,CAAN;IACAA,GAAG,CAAC2S,QAAJ,GAAe9C,OAAO,GAAG,CAAzB;;;SAEK7P,GAAP;;;AAGF,SAAS0R,iBAAT,CAA2BJ,qBAA3B,EAAkD1C,uBAAlD,EAA2E;QACnEwF,cAAc,GAAGxF,uBAAuB,CAAC,CAAD,CAA9C;QACMyF,OAAO,GAAGzF,uBAAuB,CAAC,CAAD,CAAvC;SACOF,QAAQ,CAAC4F,MAAT,CAAgBhD,qBAAqB,CAACoB,KAAtB,GAA8B7N,MAA9B,CAAqCuP,cAArC,CAAhB,EACJvP,MADI,CACGuP,cADH,EAEJvP,MAFI,CAEGwP,OAFH,CAAP;;;AAKF,SAASvC,sBAAT,CACER,qBADF,EAEEK,WAFF,EAGEtB,aAHF,EAIE;QACMrQ,GAAG,GAAG0O,QAAQ,CAAC4F,MAAT,CACVhD,qBAAqB,CAACoB,KAAtB,GAA8B7N,MAA9B,CAAqC8M,WAArC,CADU,CAAZ;QAGM/R,OAAO,GAAG;IACdqT,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcC,GADN;IAEdC,OAAO,EAAEzE,QAAQ,CAACtN,GAAT,CAAamT,SAFR;IAGdvB,EAAE,EAAEtE,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,IAA9B,EAAoC,EAApC;GAHN;SAKOP,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CAAqBzC,aAArB,EAAoCrQ,GAApC,EAAyCJ,OAAzC,EAAkDmT,UAAzD;;;AAGF,SAAShB,kBAAT,CACEP,sBADF,EAEEhB,iBAFF,EAGE5B,uBAHF,EAIE;QACMwF,cAAc,GAAGxF,uBAAuB,CAAC,CAAD,CAA9C;QACMyF,OAAO,GAAGzF,uBAAuB,CAAC,CAAD,CAAvC;SACOF,QAAQ,CAAC4F,MAAT,CACL9C,sBAAsB,CACnBkB,KADH,GAEG7N,MAFH,CAEUuP,cAFV,EAGGvP,MAHH,CAGU2L,iBAHV,CADK,EAMJ3L,MANI,CAMGuP,cANH,EAOJvP,MAPI,CAOGwP,OAPH,CAAP;;;AAUF,SAASnC,uBAAT,CACEV,sBADF,EAEEQ,YAFF,EAGExB,iBAHF,EAIEH,aAJF,EAKE;QACMrQ,GAAG,GAAG0O,QAAQ,CAAC4F,MAAT,CACV9C,sBAAsB,CACnBkB,KADH,GAEG7N,MAFH,CAEUmN,YAFV,EAGGnN,MAHH,CAGU2L,iBAHV,CADU,CAAZ;QAMM5Q,OAAO,GAAG;IACdqT,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcC,GADN;IAEdC,OAAO,EAAEzE,QAAQ,CAACtN,GAAT,CAAamT,SAFR;IAGdvB,EAAE,EAAEtE,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,IAA9B,EAAoC,EAApC;GAHN;SAKOP,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CAAqBzC,aAArB,EAAoCrQ,GAApC,EAAyCJ,OAAzC,EAAkDmT,UAAzD;;;AAGF,SAAStB,kBAAT,CAA4B7C,uBAA5B,EAAqD;SAC5CA,uBAAuB,CAAC,EAAD,CAA9B;;;AAGF,SAASwD,yBAAT,CACExC,WADF,EAEES,aAFF,EAGEzB,uBAHF,EAIE;QACMmF,MAAM,GAAGrF,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CACb,CAACkF,YAAY,CAACvE,WAAD,CAAb,EAA4B,UAA5B,EAAwC,UAAxC,CADa,EAEb,EAFa,EAGb/K,MAHa,CAGN+J,uBAAuB,CAAC,CAAD,CAHjB,CAAf;QAIMhP,OAAO,GAAG;IACdqT,IAAI,EAAEvE,QAAQ,CAACuE,IAAT,CAAcuB,GADN;IAEdrB,OAAO,EAAEzE,QAAQ,CAACtN,GAAT,CAAamT;GAFxB;SAIO7F,QAAQ,CAAC2E,GAAT,CAAaP,OAAb,CAAqBiB,MAArB,EAA6B1D,aAA7B,EAA4CzQ,OAA5C,EAAqDmT,UAA5D;;;AAGF,SAAS9C,qBAAT,CAA+BwE,QAAQ,GAAG,EAA1C,EAA8C;QACtC/T,GAAG,GAAG4B,MAAM,CAACoS,KAAP,CAAa,EAAb,CAAZ;QACM/T,MAAM,GAAG8T,QAAQ,CAAC9T,MAAxB;MACIgU,KAAK,GAAG,CAAZ;;SACOA,KAAK,GAAGhU,MAAR,IAAkBgU,KAAK,GAAG,EAAjC,EAAqC;UAC7BC,IAAI,GAAGH,QAAQ,CAACrS,UAAT,CAAoBuS,KAApB,CAAb;;QACIC,IAAI,GAAG,IAAX,EAAiB;YACT,IAAInV,KAAJ,CAAU,mDAAV,CAAN;;;IAEFiB,GAAG,CAACiU,KAAD,CAAH,GAAaC,IAAb;IACAD,KAAK;;;SAEAA,KAAK,GAAG,EAAf,EAAmB;IACjBjU,GAAG,CAACiU,KAAD,CAAH,GAAaE,gBAAgB,CAACF,KAAK,GAAGhU,MAAT,CAA7B;IACAgU,KAAK;;;SAEAjG,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8BvO,GAA9B,CAAP;;;AAGF,SAAS6Q,iBAAT,CAA2BkD,QAAQ,GAAG,EAAtC,EAA0C;EACxCA,QAAQ,GAAGK,QAAQ,CAACC,kBAAkB,CAAC/H,QAAQ,CAACyH,QAAD,CAAT,CAAnB,CAAnB;QACM9T,MAAM,GAAG+C,IAAI,CAACkP,GAAL,CAAS,GAAT,EAAc6B,QAAQ,CAAC9T,MAAvB,CAAf;QACMD,GAAG,GAAG4B,MAAM,CAACoS,KAAP,CAAa/T,MAAb,CAAZ;;OAEK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,MAApB,EAA4BkB,CAAC,EAA7B,EAAiC;IAC/BnB,GAAG,CAACmB,CAAD,CAAH,GAAS4S,QAAQ,CAACrS,UAAT,CAAoBP,CAApB,CAAT;;;SAGK6M,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8BvO,GAA9B,CAAP;;;AAGF,SAASyT,YAAT,CAAsBpQ,IAAtB,EAA4B;SAEvB,CAACA,IAAI,GAAG,IAAR,KAAiB,EAAlB,GACC,CAACA,IAAI,GAAG,MAAR,KAAmB,CADpB,GAEEA,IAAI,IAAI,CAAT,GAAc,MAFf,GAGEA,IAAI,IAAI,EAAT,GAAe,IAJlB;;;AAQF,SAAS0K,iBAAT,CAA2BuG,SAA3B,EAAsC;QAC9BC,SAAS,GAAG,EAAlB;;OACK,IAAIpT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmT,SAAS,CAACrC,QAA9B,EAAwC9Q,CAAC,EAAzC,EAA6C;IAC3CoT,SAAS,CAACnU,IAAV,CACGkU,SAAS,CAACpD,KAAV,CAAgBlO,IAAI,CAAC2H,KAAL,CAAWxJ,CAAC,GAAG,CAAf,CAAhB,KAAuC,KAAK,IAAKA,CAAC,GAAG,CAAd,CAAxC,GAA8D,IADhE;;;SAIKS,MAAM,CAACC,IAAP,CAAY0S,SAAZ,CAAP;;;AAGF,MAAMJ,gBAAgB,GAAG,CACvB,IADuB,EAEvB,IAFuB,EAGvB,IAHuB,EAIvB,IAJuB,EAKvB,IALuB,EAMvB,IANuB,EAOvB,IAPuB,EAQvB,IARuB,EASvB,IATuB,EAUvB,IAVuB,EAWvB,IAXuB,EAYvB,IAZuB,EAavB,IAbuB,EAcvB,IAduB,EAevB,IAfuB,EAgBvB,IAhBuB,EAiBvB,IAjBuB,EAkBvB,IAlBuB,EAmBvB,IAnBuB,EAoBvB,IApBuB,EAqBvB,IArBuB,EAsBvB,IAtBuB,EAuBvB,IAvBuB,EAwBvB,IAxBuB,EAyBvB,IAzBuB,EA0BvB,IA1BuB,EA2BvB,IA3BuB,EA4BvB,IA5BuB,EA6BvB,IA7BuB,EA8BvB,IA9BuB,EA+BvB,IA/BuB,EAgCvB,IAhCuB,CAAzB;;AC1gBA,MAAM;EAAErR;IAAWzC,SAAnB;;AAEA,MAAMmU,WAAN,CAAkB;EAChBvV,WAAW,CAACwV,GAAD,EAAM;SACVA,GAAL,GAAWA,GAAX;SACKC,KAAL,GAAa,EAAb;SACKC,QAAL,GAAgB,KAAhB;SACKC,SAAL,GAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAAjB;;;EAGFC,IAAI,CAACC,GAAD,EAAMC,KAAN,EAAaC,OAAb,EAAsB;QACpBA,OAAO,IAAI,IAAf,EAAqB;MACnBA,OAAO,GAAG,CAAV;;;IAEFD,KAAK,GAAG,KAAKN,GAAL,CAASQ,eAAT,CAAyBF,KAAzB,CAAR;;QAEI,KAAKL,KAAL,CAAWzU,MAAX,KAAsB,CAA1B,EAA6B;UACvB8U,KAAK,CAAC9U,MAAN,KAAiB,CAArB,EAAwB;aACjBiV,WAAL,GAAmB,WAAnB;OADF,MAEO,IAAIH,KAAK,CAAC9U,MAAN,KAAiB,CAArB,EAAwB;aACxBiV,WAAL,GAAmB,YAAnB;OADK,MAEA,IAAIH,KAAK,CAAC9U,MAAN,KAAiB,CAArB,EAAwB;aACxBiV,WAAL,GAAmB,YAAnB;OADK,MAEA;cACC,IAAInW,KAAJ,CAAU,qBAAV,CAAN;;KARJ,MAUO,IACJ,KAAKmW,WAAL,KAAqB,WAArB,IAAoCH,KAAK,CAAC9U,MAAN,KAAiB,CAAtD,IACC,KAAKiV,WAAL,KAAqB,YAArB,IAAqCH,KAAK,CAAC9U,MAAN,KAAiB,CADvD,IAEC,KAAKiV,WAAL,KAAqB,YAArB,IAAqCH,KAAK,CAAC9U,MAAN,KAAiB,CAHlD,EAIL;YACM,IAAIlB,KAAJ,CAAU,kDAAV,CAAN;;;IAGFiW,OAAO,GAAGhS,IAAI,CAACmS,GAAL,CAAS,CAAT,EAAYnS,IAAI,CAACkP,GAAL,CAAS,CAAT,EAAY8C,OAAZ,CAAZ,CAAV;SACKN,KAAL,CAAWtU,IAAX,CAAgB,CAAC0U,GAAD,EAAMC,KAAN,EAAaC,OAAb,CAAhB;WACO,IAAP;;;EAGFI,YAAY,CAACC,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6B;SAClCd,SAAL,GAAiB,CAACS,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,CAAjB;WACO,IAAP;;;EAGFC,KAAK,CAACC,CAAD,EAAI;QACHC,EAAJ;UACMC,WAAW,GAAG,KAAKpB,KAAL,CAAWzU,MAA/B;;QACI6V,WAAW,KAAK,CAApB,EAAuB;;;;SAGlBnB,QAAL,GAAgB,IAAhB;SACKoB,MAAL,GAAcH,CAAd,CAPO;;UAUDzV,IAAI,GAAG,KAAKuU,KAAL,CAAWoB,WAAW,GAAG,CAAzB,CAAb;;QACI3V,IAAI,CAAC,CAAD,CAAJ,GAAU,CAAd,EAAiB;WACVuU,KAAL,CAAWtU,IAAX,CAAgB,CAAC,CAAD,EAAID,IAAI,CAAC,CAAD,CAAR,EAAaA,IAAI,CAAC,CAAD,CAAjB,CAAhB;;;UAGI6V,MAAM,GAAG,EAAf;UACMC,MAAM,GAAG,EAAf;UACMvB,KAAK,GAAG,EAAd;;SAEK,IAAIvT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2U,WAAW,GAAG,CAAlC,EAAqC3U,CAAC,EAAtC,EAA0C;MACxC8U,MAAM,CAAC7V,IAAP,CAAY,CAAZ,EAAe,CAAf;;UACIe,CAAC,GAAG,CAAJ,KAAU2U,WAAd,EAA2B;QACzBE,MAAM,CAAC5V,IAAP,CAAY,KAAKsU,KAAL,CAAWvT,CAAC,GAAG,CAAf,EAAkB,CAAlB,CAAZ;;;MAGF0U,EAAE,GAAG,KAAKpB,GAAL,CAASjM,GAAT,CAAa;QAChB0N,YAAY,EAAE,CADE;QAEhBC,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAFQ;QAGhB/P,EAAE,EAAE,KAAKsO,KAAL,CAAWvT,CAAC,GAAG,CAAf,EAAkB,CAAlB,CAHY;QAIhBkF,EAAE,EAAE,KAAKqO,KAAL,CAAWvT,CAAC,GAAG,CAAf,EAAkB,CAAlB,CAJY;QAKhBiV,CAAC,EAAE;OALA,CAAL;MAQA1B,KAAK,CAACtU,IAAN,CAAWyV,EAAX;MACAA,EAAE,CAACzU,GAAH;KAlCK;;;QAsCH0U,WAAW,KAAK,CAApB,EAAuB;MACrBD,EAAE,GAAGnB,KAAK,CAAC,CAAD,CAAV;KADF,MAEO;MACLmB,EAAE,GAAG,KAAKpB,GAAL,CAASjM,GAAT,CAAa;QAChB0N,YAAY,EAAE,CADE;;QAEhBC,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAFQ;QAGhBE,SAAS,EAAE3B,KAHK;QAIhB4B,MAAM,EAAEN,MAJQ;QAKhBO,MAAM,EAAEN;OALL,CAAL;MAQAJ,EAAE,CAACzU,GAAH;;;SAGGgC,EAAL,GAAW,KAAI,EAAE,KAAKqR,GAAL,CAAS+B,UAAW,EAArC;UAEMC,MAAM,GAAG,KAAKA,MAAL,CAAYZ,EAAZ,CAAf;IACAY,MAAM,CAACrV,GAAP;UAEMsV,OAAO,GAAG,KAAKjC,GAAL,CAASjM,GAAT,CAAa;MAC3BI,IAAI,EAAE,SADqB;MAE3B+N,WAAW,EAAE,CAFc;MAG3BC,OAAO,EAAEH,MAHkB;MAI3BI,MAAM,EAAE,KAAKd,MAAL,CAAYpT,GAAZ,CAAgBG,MAAhB;KAJM,CAAhB;IAOA4T,OAAO,CAACtV,GAAR;;QAEI,KAAKsT,KAAL,CAAWzH,IAAX,CAAgB4H,IAAI,IAAIA,IAAI,CAAC,CAAD,CAAJ,GAAU,CAAlC,CAAJ,EAA0C;UACpCiC,IAAI,GAAG,KAAKC,eAAL,EAAX;MACAD,IAAI,CAAC5B,WAAL,GAAmB,YAAnB;;WAEK,IAAIL,IAAT,IAAiB,KAAKH,KAAtB,EAA6B;QAC3BoC,IAAI,CAACjC,IAAL,CAAUA,IAAI,CAAC,CAAD,CAAd,EAAmB,CAACA,IAAI,CAAC,CAAD,CAAL,CAAnB;;;MAGFiC,IAAI,GAAGA,IAAI,CAACnB,KAAL,CAAW,KAAKI,MAAhB,CAAP;YAEMiB,QAAQ,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,KAAKvC,GAAL,CAASwC,IAAT,CAAc5O,KAArB,EAA4B,KAAKoM,GAAL,CAASwC,IAAT,CAAc3O,MAA1C,CAAjB;YAEM4O,IAAI,GAAG,KAAKzC,GAAL,CAASjM,GAAT,CAAa;QACxBI,IAAI,EAAE,SADkB;QAExBuO,OAAO,EAAE,MAFe;QAGxBC,QAAQ,EAAE,CAHc;QAIxBC,IAAI,EAAEL,QAJkB;QAKxBM,KAAK,EAAE;UACL1O,IAAI,EAAE,OADD;UAEL2O,CAAC,EAAE,cAFE;UAGLC,EAAE,EAAE;SARkB;QAUxBtO,SAAS,EAAE;UACTR,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,QAAhB,EAA0B,QAA1B,EAAoC,QAApC,CADA;UAETiB,OAAO,EAAE;YACP8N,GAAG,EAAEX;;;OAbE,CAAb;MAkBAI,IAAI,CAACvT,KAAL,CAAW,sBAAX;MACAuT,IAAI,CAAC9V,GAAL,CAAU,GAAE4V,QAAQ,CAACvW,IAAT,CAAc,GAAd,CAAmB,OAA/B;YAEMiX,MAAM,GAAG,KAAKjD,GAAL,CAASjM,GAAT,CAAa;QAC1BI,IAAI,EAAE,WADoB;QAE1B+O,KAAK,EAAE;UACL/O,IAAI,EAAE,MADD;UAEL2O,CAAC,EAAE,YAFE;UAGLK,CAAC,EAAEV;;OALQ,CAAf;MASAQ,MAAM,CAACtW,GAAP;YAEMyW,cAAc,GAAG,KAAKpD,GAAL,CAASjM,GAAT,CAAa;QAClCI,IAAI,EAAE,SAD4B;QAElC+N,WAAW,EAAE,CAFqB;QAGlCmB,SAAS,EAAE,CAHuB;QAIlCC,UAAU,EAAE,CAJsB;QAKlCV,IAAI,EAAEL,QAL4B;QAMlCgB,KAAK,EAAEhB,QAAQ,CAAC,CAAD,CANmB;QAOlCiB,KAAK,EAAEjB,QAAQ,CAAC,CAAD,CAPmB;QAQlC9N,SAAS,EAAE;UACTR,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,QAAhB,EAA0B,QAA1B,EAAoC,QAApC,CADA;UAETiB,OAAO,EAAE;YACP8N,GAAG,EAAEf;WAHE;UAKTjN,SAAS,EAAE;YACTyO,GAAG,EAAER;;;OAdY,CAAvB;MAmBAG,cAAc,CAAClU,KAAf,CAAqB,8BAArB;MACAkU,cAAc,CAACzW,GAAf,CAAoB,GAAE4V,QAAQ,CAACvW,IAAT,CAAc,GAAd,CAAmB,OAAzC;WAEKgU,GAAL,CAASwC,IAAT,CAAcvN,QAAd,CAAuB,KAAKtG,EAA5B,IAAkCyU,cAAlC;KAlEF,MAmEO;WACApD,GAAL,CAASwC,IAAT,CAAcvN,QAAd,CAAuB,KAAKtG,EAA5B,IAAkCsT,OAAlC;;;WAGKA,OAAP;;;EAGF7J,KAAK,CAACsL,EAAD,EAAK;;UAEF,CAACC,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiBC,EAAjB,EAAqBC,EAArB,IAA2B,KAAKhE,GAAL,CAASiE,IAA1C;UACM,CAACrD,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,IAA+B,KAAKd,SAA1C;UACMgB,CAAC,GAAG,CACRwC,EAAE,GAAG/C,GAAL,GAAWiD,EAAE,GAAGhD,GADR,EAER+C,EAAE,GAAGhD,GAAL,GAAWkD,EAAE,GAAGjD,GAFR,EAGR8C,EAAE,GAAG7C,GAAL,GAAW+C,EAAE,GAAG9C,GAHR,EAIR6C,EAAE,GAAG9C,GAAL,GAAWgD,EAAE,GAAG/C,GAJR,EAKR4C,EAAE,GAAG3C,EAAL,GAAU6C,EAAE,GAAG5C,EAAf,GAAoB8C,EALZ,EAMRH,EAAE,GAAG5C,EAAL,GAAU8C,EAAE,GAAG7C,EAAf,GAAoB+C,EANZ,CAAV;;QASI,CAAC,KAAK9D,QAAN,IAAkBiB,CAAC,CAACnV,IAAF,CAAO,GAAP,MAAgB,KAAKsV,MAAL,CAAYtV,IAAZ,CAAiB,GAAjB,CAAtC,EAA6D;WACtDkV,KAAL,CAAWC,CAAX;;;WAEK,KAAKnB,GAAL,CAASkE,UAAT,CAAqB,IAAG,KAAKvV,EAAG,IAAG+U,EAAG,EAAtC,CAAP;;;;;AAIJ,MAAMS,iBAAN,SAAgCpE,WAAhC,CAA4C;EAC1CvV,WAAW,CAACwV,GAAD,EAAMoE,EAAN,EAAUC,EAAV,EAAcC,EAAd,EAAkBC,EAAlB,EAAsB;UACzBvE,GAAN;SACKoE,EAAL,GAAUA,EAAV;SACKC,EAAL,GAAUA,EAAV;SACKC,EAAL,GAAUA,EAAV;SACKC,EAAL,GAAUA,EAAV;;;EAGFvC,MAAM,CAACZ,EAAD,EAAK;WACF,KAAKpB,GAAL,CAASjM,GAAT,CAAa;MAClByQ,WAAW,EAAE,CADK;MAElBC,UAAU,EAAE,KAAKhE,WAFC;MAGlBiE,MAAM,EAAE,CAAC,KAAKN,EAAN,EAAU,KAAKC,EAAf,EAAmB,KAAKC,EAAxB,EAA4B,KAAKC,EAAjC,CAHU;MAIlBI,QAAQ,EAAEvD,EAJQ;MAKlBwD,MAAM,EAAE,CAAC,IAAD,EAAO,IAAP;KALH,CAAP;;;EASFtC,eAAe,GAAG;WACT,IAAI6B,iBAAJ,CAAsB,KAAKnE,GAA3B,EAAgC,KAAKoE,EAArC,EAAyC,KAAKC,EAA9C,EAAkD,KAAKC,EAAvD,EAA2D,KAAKC,EAAhE,CAAP;;;;;AAIJ,MAAMM,iBAAN,SAAgC9E,WAAhC,CAA4C;EAC1CvV,WAAW,CAACwV,GAAD,EAAMoE,EAAN,EAAUC,EAAV,EAAcS,EAAd,EAAkBR,EAAlB,EAAsBC,EAAtB,EAA0BQ,EAA1B,EAA8B;UACjC/E,GAAN;SACKA,GAAL,GAAWA,GAAX;SACKoE,EAAL,GAAUA,EAAV;SACKC,EAAL,GAAUA,EAAV;SACKS,EAAL,GAAUA,EAAV;SACKR,EAAL,GAAUA,EAAV;SACKC,EAAL,GAAUA,EAAV;SACKQ,EAAL,GAAUA,EAAV;;;EAGF/C,MAAM,CAACZ,EAAD,EAAK;WACF,KAAKpB,GAAL,CAASjM,GAAT,CAAa;MAClByQ,WAAW,EAAE,CADK;MAElBC,UAAU,EAAE,KAAKhE,WAFC;MAGlBiE,MAAM,EAAE,CAAC,KAAKN,EAAN,EAAU,KAAKC,EAAf,EAAmB,KAAKS,EAAxB,EAA4B,KAAKR,EAAjC,EAAqC,KAAKC,EAA1C,EAA8C,KAAKQ,EAAnD,CAHU;MAIlBJ,QAAQ,EAAEvD,EAJQ;MAKlBwD,MAAM,EAAE,CAAC,IAAD,EAAO,IAAP;KALH,CAAP;;;EASFtC,eAAe,GAAG;WACT,IAAIuC,iBAAJ,CACL,KAAK7E,GADA,EAEL,KAAKoE,EAFA,EAGL,KAAKC,EAHA,EAIL,KAAKS,EAJA,EAKL,KAAKR,EALA,EAML,KAAKC,EANA,EAOL,KAAKQ,EAPA,CAAP;;;;;AAYJ,eAAe;EAAEhF,WAAF;EAAeoE,iBAAf;EAAkCU;CAAjD;;ACvQA,MAAM;eAAE9E,aAAF;qBAAeoE,mBAAf;qBAAkCU;IAAsBG,QAA9D;AAEA,iBAAe;EACbC,SAAS,GAAG;;SAELC,gBAAL,GAAwB,EAAxB;SACKC,aAAL,GAAqB,CAArB;WACQ,KAAKpD,UAAL,GAAkB,CAA1B;GALW;;EAQbvB,eAAe,CAACF,KAAD,EAAQ;QACjBA,KAAK,YAAYP,aAArB,EAAkC;aACzBO,KAAP;;;QAGE,OAAOA,KAAP,KAAiB,QAArB,EAA+B;UACzBA,KAAK,CAAC8E,MAAN,CAAa,CAAb,MAAoB,GAAxB,EAA6B;YACvB9E,KAAK,CAAC9U,MAAN,KAAiB,CAArB,EAAwB;UACtB8U,KAAK,GAAGA,KAAK,CAAChT,OAAN,CACN,kCADM,EAEN,eAFM,CAAR;;;cAKI+X,GAAG,GAAGC,QAAQ,CAAChF,KAAK,CAAClU,KAAN,CAAY,CAAZ,CAAD,EAAiB,EAAjB,CAApB;QACAkU,KAAK,GAAG,CAAC+E,GAAG,IAAI,EAAR,EAAaA,GAAG,IAAI,CAAR,GAAa,IAAzB,EAA+BA,GAAG,GAAG,IAArC,CAAR;OARF,MASO,IAAIE,WAAW,CAACjF,KAAD,CAAf,EAAwB;QAC7BA,KAAK,GAAGiF,WAAW,CAACjF,KAAD,CAAnB;;;;QAIAnU,KAAK,CAAC6B,OAAN,CAAcsS,KAAd,CAAJ,EAA0B;;UAEpBA,KAAK,CAAC9U,MAAN,KAAiB,CAArB,EAAwB;QACtB8U,KAAK,GAAGA,KAAK,CAACpS,GAAN,CAAUsX,IAAI,IAAIA,IAAI,GAAG,GAAzB,CAAR,CADsB;OAAxB,MAGO,IAAIlF,KAAK,CAAC9U,MAAN,KAAiB,CAArB,EAAwB;QAC7B8U,KAAK,GAAGA,KAAK,CAACpS,GAAN,CAAUsX,IAAI,IAAIA,IAAI,GAAG,GAAzB,CAAR;;;aAEKlF,KAAP;;;WAGK,IAAP;GAvCW;;EA0CbmF,SAAS,CAACnF,KAAD,EAAQoF,MAAR,EAAgB;IACvBpF,KAAK,GAAG,KAAKE,eAAL,CAAqBF,KAArB,CAAR;;QACI,CAACA,KAAL,EAAY;aACH,KAAP;;;UAGIoD,EAAE,GAAGgC,MAAM,GAAG,KAAH,GAAW,KAA5B;;QAEIpF,KAAK,YAAYP,aAArB,EAAkC;WAC3B4F,cAAL,CAAoB,SAApB,EAA+BD,MAA/B;;MACApF,KAAK,CAAClI,KAAN,CAAYsL,EAAZ;KAFF,MAGO;YACCkC,KAAK,GAAGtF,KAAK,CAAC9U,MAAN,KAAiB,CAAjB,GAAqB,YAArB,GAAoC,WAAlD;;WACKma,cAAL,CAAoBC,KAApB,EAA2BF,MAA3B;;MAEApF,KAAK,GAAGA,KAAK,CAACtU,IAAN,CAAW,GAAX,CAAR;WACKkY,UAAL,CAAiB,GAAE5D,KAAM,IAAGoD,EAAG,EAA/B;;;WAGK,IAAP;GA7DW;;EAgEbiC,cAAc,CAACC,KAAD,EAAQF,MAAR,EAAgB;UACtBhC,EAAE,GAAGgC,MAAM,GAAG,IAAH,GAAU,IAA3B;WACO,KAAKxB,UAAL,CAAiB,IAAG0B,KAAM,IAAGlC,EAAG,EAAhC,CAAP;GAlEW;;EAqEbmC,SAAS,CAACvF,KAAD,EAAQC,OAAR,EAAiB;UAClBuF,GAAG,GAAG,KAAKL,SAAL,CAAenF,KAAf,EAAsB,KAAtB,CAAZ;;QACIwF,GAAJ,EAAS;WACFC,WAAL,CAAiBxF,OAAjB;KAHsB;;;;SAQnByF,UAAL,GAAkB,CAAC1F,KAAD,EAAQC,OAAR,CAAlB;WACO,IAAP;GA9EW;;EAiFb0F,WAAW,CAAC3F,KAAD,EAAQC,OAAR,EAAiB;UACpBuF,GAAG,GAAG,KAAKL,SAAL,CAAenF,KAAf,EAAsB,IAAtB,CAAZ;;QACIwF,GAAJ,EAAS;WACFI,aAAL,CAAmB3F,OAAnB;;;WAEK,IAAP;GAtFW;;EAyFbA,OAAO,CAACA,OAAD,EAAU;SACV4F,UAAL,CAAgB5F,OAAhB,EAAyBA,OAAzB;;WACO,IAAP;GA3FW;;EA8FbwF,WAAW,CAACxF,OAAD,EAAU;SACd4F,UAAL,CAAgB5F,OAAhB,EAAyB,IAAzB;;WACO,IAAP;GAhGW;;EAmGb2F,aAAa,CAAC3F,OAAD,EAAU;SAChB4F,UAAL,CAAgB,IAAhB,EAAsB5F,OAAtB;;WACO,IAAP;GArGW;;EAwGb4F,UAAU,CAACJ,WAAD,EAAcG,aAAd,EAA6B;QACjChS,UAAJ,EAAgBkS,IAAhB;;QACIL,WAAW,IAAI,IAAf,IAAuBG,aAAa,IAAI,IAA5C,EAAkD;;;;QAI9CH,WAAW,IAAI,IAAnB,EAAyB;MACvBA,WAAW,GAAGxX,IAAI,CAACmS,GAAL,CAAS,CAAT,EAAYnS,IAAI,CAACkP,GAAL,CAAS,CAAT,EAAYsI,WAAZ,CAAZ,CAAd;;;QAEEG,aAAa,IAAI,IAArB,EAA2B;MACzBA,aAAa,GAAG3X,IAAI,CAACmS,GAAL,CAAS,CAAT,EAAYnS,IAAI,CAACkP,GAAL,CAAS,CAAT,EAAYyI,aAAZ,CAAZ,CAAhB;;;UAEIrb,GAAG,GAAI,GAAEkb,WAAY,IAAGG,aAAc,EAA5C;;QAEI,KAAKhB,gBAAL,CAAsBra,GAAtB,CAAJ,EAAgC;OAC7BqJ,UAAD,EAAakS,IAAb,IAAqB,KAAKlB,gBAAL,CAAsBra,GAAtB,CAArB;KADF,MAEO;MACLqJ,UAAU,GAAG;QAAEC,IAAI,EAAE;OAArB;;UAEI4R,WAAW,IAAI,IAAnB,EAAyB;QACvB7R,UAAU,CAACmS,EAAX,GAAgBN,WAAhB;;;UAEEG,aAAa,IAAI,IAArB,EAA2B;QACzBhS,UAAU,CAACoS,EAAX,GAAgBJ,aAAhB;;;MAGFhS,UAAU,GAAG,KAAKH,GAAL,CAASG,UAAT,CAAb;MACAA,UAAU,CAACvH,GAAX;YACMgC,EAAE,GAAG,EAAE,KAAKwW,aAAlB;MACAiB,IAAI,GAAI,KAAIzX,EAAG,EAAf;WACKuW,gBAAL,CAAsBra,GAAtB,IAA6B,CAACqJ,UAAD,EAAakS,IAAb,CAA7B;;;SAGG5D,IAAL,CAAUzN,WAAV,CAAsBqR,IAAtB,IAA8BlS,UAA9B;WACO,KAAKgQ,UAAL,CAAiB,IAAGkC,IAAK,KAAzB,CAAP;GA1IW;;EA6IbG,cAAc,CAACnC,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiB;WACtB,IAAIJ,mBAAJ,CAAsB,IAAtB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,CAAP;GA9IW;;EAiJbiC,cAAc,CAACpC,EAAD,EAAKC,EAAL,EAASS,EAAT,EAAaR,EAAb,EAAiBC,EAAjB,EAAqBQ,EAArB,EAAyB;WAC9B,IAAIF,mBAAJ,CAAsB,IAAtB,EAA4BT,EAA5B,EAAgCC,EAAhC,EAAoCS,EAApC,EAAwCR,EAAxC,EAA4CC,EAA5C,EAAgDQ,EAAhD,CAAP;;;CAlJJ;AAsJA,IAAIQ,WAAW,GAAG;EAChBkB,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CADK;EAEhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAFE;EAGhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAHU;EAIhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAJI;EAKhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CALS;EAMhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CANS;EAOhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAPQ;EAQhBC,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CARS;EAShBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CATA;EAUhBC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAVU;EAWhBC,UAAU,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAXI;EAYhBC,KAAK,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CAZS;EAahBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAbK;EAchBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CAdK;EAehBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAfI;EAgBhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAhBK;EAiBhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAjBS;EAkBhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlBA;EAmBhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnBM;EAoBhBC,OAAO,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CApBO;EAqBhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CArBU;EAsBhBC,QAAQ,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAtBM;EAuBhBC,QAAQ,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvBM;EAwBhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAxBC;EAyBhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzBM;EA0BhBC,SAAS,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CA1BK;EA2BhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3BM;EA4BhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5BK;EA6BhBC,WAAW,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CA7BG;EA8BhBC,cAAc,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CA9BA;EA+BhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CA/BI;EAgChBC,UAAU,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAhCI;EAiChBC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAjCO;EAkChBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlCI;EAmChBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnCE;EAoChBC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,GAAT,CApCC;EAqChBC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,CArCC;EAsChBC,aAAa,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,CAtCC;EAuChBC,aAAa,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvCC;EAwChBC,UAAU,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAxCI;EAyChBC,QAAQ,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAzCM;EA0ChBC,WAAW,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA1CG;EA2ChBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3CO;EA4ChBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5CO;EA6ChBC,UAAU,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7CI;EA8ChBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA9CK;EA+ChBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/CG;EAgDhBC,WAAW,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CAhDG;EAiDhBC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAjDO;EAkDhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlDK;EAmDhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnDI;EAoDhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CApDU;EAqDhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CArDK;EAsDhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtDU;EAuDhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvDU;EAwDhBC,KAAK,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CAxDS;EAyDhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAzDG;EA0DhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA1DM;EA2DhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3DO;EA4DhBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA5DK;EA6DhBC,MAAM,EAAE,CAAC,EAAD,EAAK,CAAL,EAAQ,GAAR,CA7DQ;EA8DhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9DS;EA+DhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/DS;EAgEhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhEM;EAiEhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjEC;EAkEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAlEK;EAmEhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnEE;EAoEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApEK;EAqEhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArEI;EAsEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtEK;EAuEhBC,oBAAoB,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvEN;EAwEhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAxEK;EAyEhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzEI;EA0EhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA1EK;EA2EhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3EK;EA4EhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5EG;EA6EhBC,aAAa,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7EC;EA8EhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9EE;EA+EhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/EA;EAgFhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhFA;EAiFhBC,cAAc,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjFA;EAkFhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlFG;EAmFhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,CAAT,CAnFU;EAoFhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CApFK;EAqFhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArFS;EAsFhBC,OAAO,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAtFO;EAuFhBC,MAAM,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAvFQ;EAwFhBC,gBAAgB,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAxFF;EAyFhBC,UAAU,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAzFI;EA0FhBC,YAAY,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CA1FE;EA2FhBC,YAAY,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3FE;EA4FhBC,cAAc,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA5FA;EA6FhBC,eAAe,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA7FD;EA8FhBC,iBAAiB,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA9FH;EA+FhBC,eAAe,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA/FD;EAgGhBC,eAAe,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAhGD;EAiGhBC,YAAY,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,GAAT,CAjGE;EAkGhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlGK;EAmGhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAnGK;EAoGhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApGM;EAqGhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArGG;EAsGhBC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,GAAP,CAtGU;EAuGhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAvGO;EAwGhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAxGS;EAyGhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAzGK;EA0GhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CA1GQ;EA2GhBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,CAAV,CA3GK;EA4GhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5GQ;EA6GhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA7GC;EA8GhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9GK;EA+GhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/GC;EAgHhBC,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhHC;EAiHhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjHI;EAkHhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlHK;EAmHhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAnHU;EAoHhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApHU;EAqHhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArHU;EAsHhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtHI;EAuHhBC,MAAM,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,GAAT,CAvHQ;EAwHhBC,GAAG,EAAE,CAAC,GAAD,EAAM,CAAN,EAAS,CAAT,CAxHW;EAyHhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzHK;EA0HhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA1HK;EA2HhBC,WAAW,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA3HG;EA4HhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA5HQ;EA6HhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CA7HI;EA8HhBC,QAAQ,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,EAAV,CA9HM;EA+HhBC,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/HM;EAgIhBC,MAAM,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CAhIQ;EAiIhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjIQ;EAkIhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAlIO;EAmIhBC,SAAS,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,GAAV,CAnIK;EAoIhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CApIK;EAqIhBC,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CArIK;EAsIhBC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAtIU;EAuIhBC,WAAW,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CAvIG;EAwIhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CAxIK;EAyIhBC,GAAG,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAzIW;EA0IhBC,IAAI,EAAE,CAAC,CAAD,EAAI,GAAJ,EAAS,GAAT,CA1IU;EA2IhBC,OAAO,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA3IO;EA4IhBC,MAAM,EAAE,CAAC,GAAD,EAAM,EAAN,EAAU,EAAV,CA5IQ;EA6IhBC,SAAS,EAAE,CAAC,EAAD,EAAK,GAAL,EAAU,GAAV,CA7IK;EA8IhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA9IQ;EA+IhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CA/IS;EAgJhBC,KAAK,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAhJS;EAiJhBC,UAAU,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAjJI;EAkJhBC,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAlJQ;EAmJhBC,WAAW,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX;CAnJf;;AC1JA,IAAIC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB;AAEAL,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,CAA9B;AAEA,MAAMC,UAAU,GAAG;EACjBC,CAAC,EAAE,CADc;EAEjB/kB,CAAC,EAAE,CAFc;EAGjBglB,CAAC,EAAE,CAHc;EAIjB7iB,CAAC,EAAE,CAJc;EAKjB8iB,CAAC,EAAE,CALc;EAMjBC,CAAC,EAAE,CANc;EAOjBC,CAAC,EAAE,CAPc;EAQjB9jB,CAAC,EAAE,CARc;EASjB+jB,CAAC,EAAE,CATc;EAUjBrP,CAAC,EAAE,CAVc;EAWjBsP,CAAC,EAAE,CAXc;EAYjBC,CAAC,EAAE,CAZc;EAajB5N,CAAC,EAAE,CAbc;EAcjB6N,CAAC,EAAE,CAdc;EAejBC,CAAC,EAAE,CAfc;EAgBjBC,CAAC,EAAE,CAhBc;EAiBjBrV,CAAC,EAAE,CAjBc;EAkBjBjB,CAAC,EAAE,CAlBc;EAmBjBuW,CAAC,EAAE,CAnBc;EAoBjBC,CAAC,EAAE;CApBL;;AAuBA,MAAMC,KAAK,GAAG,UAASC,IAAT,EAAe;MACvBC,GAAJ;QACMC,GAAG,GAAG,EAAZ;MACIC,IAAI,GAAG,EAAX;MACIC,MAAM,GAAG,EAAb;MACIC,YAAY,GAAG,KAAnB;MACIC,MAAM,GAAG,CAAb;;OAEK,IAAIhkB,CAAT,IAAc0jB,IAAd,EAAoB;QACdf,UAAU,CAAC3iB,CAAD,CAAV,IAAiB,IAArB,EAA2B;MACzBgkB,MAAM,GAAGrB,UAAU,CAAC3iB,CAAD,CAAnB;;UACI2jB,GAAJ,EAAS;;YAEHG,MAAM,CAAC7lB,MAAP,GAAgB,CAApB,EAAuB;UACrB4lB,IAAI,CAACA,IAAI,CAAC5lB,MAAN,CAAJ,GAAoB,CAAC6lB,MAArB;;;QAEFF,GAAG,CAACA,GAAG,CAAC3lB,MAAL,CAAH,GAAkB;UAAE0lB,GAAF;UAAOE;SAAzB;QAEAA,IAAI,GAAG,EAAP;QACAC,MAAM,GAAG,EAAT;QACAC,YAAY,GAAG,KAAf;;;MAGFJ,GAAG,GAAG3jB,CAAN;KAdF,MAeO,IACL,CAAC,GAAD,EAAM,GAAN,EAAWikB,QAAX,CAAoBjkB,CAApB,KACCA,CAAC,KAAK,GAAN,IAAa8jB,MAAM,CAAC7lB,MAAP,GAAgB,CAA7B,IAAkC6lB,MAAM,CAACA,MAAM,CAAC7lB,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GADjE,IAEC+B,CAAC,KAAK,GAAN,IAAa+jB,YAHT,EAIL;UACID,MAAM,CAAC7lB,MAAP,KAAkB,CAAtB,EAAyB;;;;UAIrB4lB,IAAI,CAAC5lB,MAAL,KAAgB+lB,MAApB,EAA4B;;QAE1BJ,GAAG,CAACA,GAAG,CAAC3lB,MAAL,CAAH,GAAkB;UAAE0lB,GAAF;UAAOE;SAAzB;QACAA,IAAI,GAAG,CAAC,CAACC,MAAF,CAAP,CAH0B;;YAMtBH,GAAG,KAAK,GAAZ,EAAiB;UACfA,GAAG,GAAG,GAAN;;;YAEEA,GAAG,KAAK,GAAZ,EAAiB;UACfA,GAAG,GAAG,GAAN;;OAVJ,MAYO;QACLE,IAAI,CAACA,IAAI,CAAC5lB,MAAN,CAAJ,GAAoB,CAAC6lB,MAArB;;;MAGFC,YAAY,GAAG/jB,CAAC,KAAK,GAArB,CArBA;;MAwBA8jB,MAAM,GAAG,CAAC,GAAD,EAAM,GAAN,EAAWG,QAAX,CAAoBjkB,CAApB,IAAyBA,CAAzB,GAA6B,EAAtC;KA5BK,MA6BA;MACL8jB,MAAM,IAAI9jB,CAAV;;UACIA,CAAC,KAAK,GAAV,EAAe;QACb+jB,YAAY,GAAG,IAAf;;;GAxDqB;;;MA8DvBD,MAAM,CAAC7lB,MAAP,GAAgB,CAApB,EAAuB;QACjB4lB,IAAI,CAAC5lB,MAAL,KAAgB+lB,MAApB,EAA4B;;MAE1BJ,GAAG,CAACA,GAAG,CAAC3lB,MAAL,CAAH,GAAkB;QAAE0lB,GAAF;QAAOE;OAAzB;MACAA,IAAI,GAAG,CAAC,CAACC,MAAF,CAAP,CAH0B;;UAMtBH,GAAG,KAAK,GAAZ,EAAiB;QACfA,GAAG,GAAG,GAAN;;;UAEEA,GAAG,KAAK,GAAZ,EAAiB;QACfA,GAAG,GAAG,GAAN;;KAVJ,MAYO;MACLE,IAAI,CAACA,IAAI,CAAC5lB,MAAN,CAAJ,GAAoB,CAAC6lB,MAArB;;;;EAIJF,GAAG,CAACA,GAAG,CAAC3lB,MAAL,CAAH,GAAkB;IAAE0lB,GAAF;IAAOE;GAAzB;SAEOD,GAAP;CAlFF;;AAqFA,MAAM/Y,KAAK,GAAG,UAASqZ,QAAT,EAAmBzR,GAAnB,EAAwB;;EAEpC4P,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,CAA9B,CAFoC;;OAK/B,IAAIvjB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+kB,QAAQ,CAACjmB,MAA7B,EAAqCkB,CAAC,EAAtC,EAA0C;UAClCa,CAAC,GAAGkkB,QAAQ,CAAC/kB,CAAD,CAAlB;;QACI,OAAOglB,OAAO,CAACnkB,CAAC,CAAC2jB,GAAH,CAAd,KAA0B,UAA9B,EAA0C;MACxCQ,OAAO,CAACnkB,CAAC,CAAC2jB,GAAH,CAAP,CAAelR,GAAf,EAAoBzS,CAAC,CAAC6jB,IAAtB;;;CARN;;AAaA,MAAMM,OAAO,GAAG;EACdlB,CAAC,CAACxQ,GAAD,EAAM5U,CAAN,EAAS;IACRwkB,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAN;IACAykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAN;IACA0kB,EAAE,GAAGC,EAAE,GAAG,IAAV;IACAC,EAAE,GAAGJ,EAAL;IACAK,EAAE,GAAGJ,EAAL;WACO7P,GAAG,CAAC2R,MAAJ,CAAW/B,EAAX,EAAeC,EAAf,CAAP;GAPY;;EAUd1O,CAAC,CAACnB,GAAD,EAAM5U,CAAN,EAAS;IACRwkB,EAAE,IAAIxkB,CAAC,CAAC,CAAD,CAAP;IACAykB,EAAE,IAAIzkB,CAAC,CAAC,CAAD,CAAP;IACA0kB,EAAE,GAAGC,EAAE,GAAG,IAAV;IACAC,EAAE,GAAGJ,EAAL;IACAK,EAAE,GAAGJ,EAAL;WACO7P,GAAG,CAAC2R,MAAJ,CAAW/B,EAAX,EAAeC,EAAf,CAAP;GAhBY;;EAmBdO,CAAC,CAACpQ,GAAD,EAAM5U,CAAN,EAAS;IACRwkB,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAN;IACAykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAN;IACA0kB,EAAE,GAAG1kB,CAAC,CAAC,CAAD,CAAN;IACA2kB,EAAE,GAAG3kB,CAAC,CAAC,CAAD,CAAN;WACO4U,GAAG,CAAC4R,aAAJ,CAAkB,GAAGxmB,CAArB,CAAP;GAxBY;;EA2BdmC,CAAC,CAACyS,GAAD,EAAM5U,CAAN,EAAS;IACR4U,GAAG,CAAC4R,aAAJ,CACExmB,CAAC,CAAC,CAAD,CAAD,GAAOwkB,EADT,EAEExkB,CAAC,CAAC,CAAD,CAAD,GAAOykB,EAFT,EAGEzkB,CAAC,CAAC,CAAD,CAAD,GAAOwkB,EAHT,EAIExkB,CAAC,CAAC,CAAD,CAAD,GAAOykB,EAJT,EAKEzkB,CAAC,CAAC,CAAD,CAAD,GAAOwkB,EALT,EAMExkB,CAAC,CAAC,CAAD,CAAD,GAAOykB,EANT;IAQAC,EAAE,GAAGF,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAX;IACA2kB,EAAE,GAAGF,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAX;IACAwkB,EAAE,IAAIxkB,CAAC,CAAC,CAAD,CAAP;WACQykB,EAAE,IAAIzkB,CAAC,CAAC,CAAD,CAAf;GAvCY;;EA0Cd0X,CAAC,CAAC9C,GAAD,EAAM5U,CAAN,EAAS;QACJ0kB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;;;IAGF7P,GAAG,CAAC4R,aAAJ,CAAkBhC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAApB,EAAkCC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAApC,EAAkDzkB,CAAC,CAAC,CAAD,CAAnD,EAAwDA,CAAC,CAAC,CAAD,CAAzD,EAA8DA,CAAC,CAAC,CAAD,CAA/D,EAAoEA,CAAC,CAAC,CAAD,CAArE;IACA0kB,EAAE,GAAG1kB,CAAC,CAAC,CAAD,CAAN;IACA2kB,EAAE,GAAG3kB,CAAC,CAAC,CAAD,CAAN;IACAwkB,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAN;WACQykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAd;GApDY;;EAuDdulB,CAAC,CAAC3Q,GAAD,EAAM5U,CAAN,EAAS;QACJ0kB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;;;IAGF7P,GAAG,CAAC4R,aAAJ,CACEhC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CADJ,EAEEC,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAFJ,EAGED,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAHR,EAIEykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAJR,EAKEwkB,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CALR,EAMEykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CANR;IAQA0kB,EAAE,GAAGF,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAX;IACA2kB,EAAE,GAAGF,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAX;IACAwkB,EAAE,IAAIxkB,CAAC,CAAC,CAAD,CAAP;WACQykB,EAAE,IAAIzkB,CAAC,CAAC,CAAD,CAAf;GAxEY;;EA2EdqlB,CAAC,CAACzQ,GAAD,EAAM5U,CAAN,EAAS;IACR0kB,EAAE,GAAG1kB,CAAC,CAAC,CAAD,CAAN;IACA2kB,EAAE,GAAG3kB,CAAC,CAAC,CAAD,CAAN;IACAwkB,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAN;IACAykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAN;WACO4U,GAAG,CAAC6R,gBAAJ,CAAqBzmB,CAAC,CAAC,CAAD,CAAtB,EAA2BA,CAAC,CAAC,CAAD,CAA5B,EAAiCwkB,EAAjC,EAAqCC,EAArC,CAAP;GAhFY;;EAmFda,CAAC,CAAC1Q,GAAD,EAAM5U,CAAN,EAAS;IACR4U,GAAG,CAAC6R,gBAAJ,CAAqBzmB,CAAC,CAAC,CAAD,CAAD,GAAOwkB,EAA5B,EAAgCxkB,CAAC,CAAC,CAAD,CAAD,GAAOykB,EAAvC,EAA2CzkB,CAAC,CAAC,CAAD,CAAD,GAAOwkB,EAAlD,EAAsDxkB,CAAC,CAAC,CAAD,CAAD,GAAOykB,EAA7D;IACAC,EAAE,GAAGF,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAX;IACA2kB,EAAE,GAAGF,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAX;IACAwkB,EAAE,IAAIxkB,CAAC,CAAC,CAAD,CAAP;WACQykB,EAAE,IAAIzkB,CAAC,CAAC,CAAD,CAAf;GAxFY;;EA2FdwlB,CAAC,CAAC5Q,GAAD,EAAM5U,CAAN,EAAS;QACJ0kB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;KAFF,MAGO;MACLC,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;MACAG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;;;IAGF7P,GAAG,CAAC6R,gBAAJ,CAAqB/B,EAArB,EAAyBC,EAAzB,EAA6B3kB,CAAC,CAAC,CAAD,CAA9B,EAAmCA,CAAC,CAAC,CAAD,CAApC;IACA0kB,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;IACAG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;IACAD,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAN;WACQykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAd;GAxGY;;EA2GdylB,CAAC,CAAC7Q,GAAD,EAAM5U,CAAN,EAAS;QACJ0kB,EAAE,KAAK,IAAX,EAAiB;MACfA,EAAE,GAAGF,EAAL;MACAG,EAAE,GAAGF,EAAL;KAFF,MAGO;MACLC,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;MACAG,EAAE,GAAGF,EAAE,IAAIE,EAAE,GAAGF,EAAT,CAAP;;;IAGF7P,GAAG,CAAC6R,gBAAJ,CAAqB/B,EAArB,EAAyBC,EAAzB,EAA6BH,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAnC,EAAwCykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAA9C;IACAwkB,EAAE,IAAIxkB,CAAC,CAAC,CAAD,CAAP;WACQykB,EAAE,IAAIzkB,CAAC,CAAC,CAAD,CAAf;GAtHY;;EAyHd+kB,CAAC,CAACnQ,GAAD,EAAM5U,CAAN,EAAS;IACR0mB,QAAQ,CAAC9R,GAAD,EAAM4P,EAAN,EAAUC,EAAV,EAAczkB,CAAd,CAAR;IACAwkB,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAN;WACQykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAd;GA5HY;;EA+HdA,CAAC,CAAC4U,GAAD,EAAM5U,CAAN,EAAS;IACRA,CAAC,CAAC,CAAD,CAAD,IAAQwkB,EAAR;IACAxkB,CAAC,CAAC,CAAD,CAAD,IAAQykB,EAAR;IACAiC,QAAQ,CAAC9R,GAAD,EAAM4P,EAAN,EAAUC,EAAV,EAAczkB,CAAd,CAAR;IACAwkB,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAN;WACQykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAd;GApIY;;EAuIdmlB,CAAC,CAACvQ,GAAD,EAAM5U,CAAN,EAAS;IACRwkB,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAN;IACAykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAN;IACA0kB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACO/P,GAAG,CAAC+R,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GA3IY;;EA8IdpjB,CAAC,CAACuT,GAAD,EAAM5U,CAAN,EAAS;IACRwkB,EAAE,IAAIxkB,CAAC,CAAC,CAAD,CAAP;IACAykB,EAAE,IAAIzkB,CAAC,CAAC,CAAD,CAAP;IACA0kB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACO/P,GAAG,CAAC+R,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GAlJY;;EAqJdQ,CAAC,CAACrQ,GAAD,EAAM5U,CAAN,EAAS;IACRwkB,EAAE,GAAGxkB,CAAC,CAAC,CAAD,CAAN;IACA0kB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACO/P,GAAG,CAAC+R,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GAxJY;;EA2JdS,CAAC,CAACtQ,GAAD,EAAM5U,CAAN,EAAS;IACRwkB,EAAE,IAAIxkB,CAAC,CAAC,CAAD,CAAP;IACA0kB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACO/P,GAAG,CAAC+R,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GA9JY;;EAiKdrU,CAAC,CAACwE,GAAD,EAAM5U,CAAN,EAAS;IACRykB,EAAE,GAAGzkB,CAAC,CAAC,CAAD,CAAN;IACA0kB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACO/P,GAAG,CAAC+R,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GApKY;;EAuKdtV,CAAC,CAACyF,GAAD,EAAM5U,CAAN,EAAS;IACRykB,EAAE,IAAIzkB,CAAC,CAAC,CAAD,CAAP;IACA0kB,EAAE,GAAGC,EAAE,GAAG,IAAV;WACO/P,GAAG,CAAC+R,MAAJ,CAAWnC,EAAX,EAAeC,EAAf,CAAP;GA1KY;;EA6KdiB,CAAC,CAAC9Q,GAAD,EAAM;IACLA,GAAG,CAACgS,SAAJ;IACApC,EAAE,GAAGI,EAAL;WACQH,EAAE,GAAGI,EAAb;GAhLY;;EAmLdc,CAAC,CAAC/Q,GAAD,EAAM;IACLA,GAAG,CAACgS,SAAJ;IACApC,EAAE,GAAGI,EAAL;WACQH,EAAE,GAAGI,EAAb;;;CAtLJ;;AA0LA,MAAM6B,QAAQ,GAAG,UAAS9R,GAAT,EAAczI,CAAd,EAAiB0a,CAAjB,EAAoBC,MAApB,EAA4B;QACrC,CAACC,EAAD,EAAKC,EAAL,EAASC,GAAT,EAAcC,KAAd,EAAqBC,KAArB,EAA4BC,EAA5B,EAAgCC,EAAhC,IAAsCP,MAA5C;QACMQ,IAAI,GAAGC,aAAa,CAACH,EAAD,EAAKC,EAAL,EAASN,EAAT,EAAaC,EAAb,EAAiBE,KAAjB,EAAwBC,KAAxB,EAA+BF,GAA/B,EAAoC9a,CAApC,EAAuC0a,CAAvC,CAA1B;;OAEK,IAAIW,GAAT,IAAgBF,IAAhB,EAAsB;UACdG,GAAG,GAAGC,eAAe,CAAC,GAAGF,GAAJ,CAA3B;IACA5S,GAAG,CAAC4R,aAAJ,CAAkB,GAAGiB,GAArB;;CANJ;;;AAWA,MAAMF,aAAa,GAAG,UAASpb,CAAT,EAAY0a,CAAZ,EAAeE,EAAf,EAAmBC,EAAnB,EAAuBE,KAAvB,EAA8BC,KAA9B,EAAqCQ,OAArC,EAA8CC,EAA9C,EAAkDC,EAAlD,EAAsD;QACpEC,EAAE,GAAGH,OAAO,IAAIxkB,IAAI,CAAC4kB,EAAL,GAAU,GAAd,CAAlB;QACMC,MAAM,GAAG7kB,IAAI,CAAC8kB,GAAL,CAASH,EAAT,CAAf;QACMI,MAAM,GAAG/kB,IAAI,CAACglB,GAAL,CAASL,EAAT,CAAf;EACAf,EAAE,GAAG5jB,IAAI,CAACilB,GAAL,CAASrB,EAAT,CAAL;EACAC,EAAE,GAAG7jB,IAAI,CAACilB,GAAL,CAASpB,EAAT,CAAL;EACAtC,EAAE,GAAGwD,MAAM,IAAIN,EAAE,GAAGzb,CAAT,CAAN,GAAoB,GAApB,GAA0B6b,MAAM,IAAIH,EAAE,GAAGhB,CAAT,CAAN,GAAoB,GAAnD;EACAlC,EAAE,GAAGuD,MAAM,IAAIL,EAAE,GAAGhB,CAAT,CAAN,GAAoB,GAApB,GAA0BmB,MAAM,IAAIJ,EAAE,GAAGzb,CAAT,CAAN,GAAoB,GAAnD;MACIkc,EAAE,GAAI3D,EAAE,GAAGA,EAAN,IAAaqC,EAAE,GAAGA,EAAlB,IAAyBpC,EAAE,GAAGA,EAAN,IAAaqC,EAAE,GAAGA,EAAlB,CAAjC;;MACIqB,EAAE,GAAG,CAAT,EAAY;IACVA,EAAE,GAAGllB,IAAI,CAACmlB,IAAL,CAAUD,EAAV,CAAL;IACAtB,EAAE,IAAIsB,EAAN;IACArB,EAAE,IAAIqB,EAAN;;;QAGIE,GAAG,GAAGL,MAAM,GAAGnB,EAArB;QACMyB,GAAG,GAAGR,MAAM,GAAGjB,EAArB;QACM0B,GAAG,GAAG,CAACT,MAAD,GAAUhB,EAAtB;QACM0B,GAAG,GAAGR,MAAM,GAAGlB,EAArB;QACM2B,EAAE,GAAGJ,GAAG,GAAGX,EAAN,GAAWY,GAAG,GAAGX,EAA5B;QACMe,EAAE,GAAGH,GAAG,GAAGb,EAAN,GAAWc,GAAG,GAAGb,EAA5B;QACM7O,EAAE,GAAGuP,GAAG,GAAGpc,CAAN,GAAUqc,GAAG,GAAG3B,CAA3B;QACM5N,EAAE,GAAGwP,GAAG,GAAGtc,CAAN,GAAUuc,GAAG,GAAG7B,CAA3B;QAEMgC,CAAC,GAAG,CAAC7P,EAAE,GAAG2P,EAAN,KAAa3P,EAAE,GAAG2P,EAAlB,IAAwB,CAAC1P,EAAE,GAAG2P,EAAN,KAAa3P,EAAE,GAAG2P,EAAlB,CAAlC;MACIE,UAAU,GAAG,IAAID,CAAJ,GAAQ,IAAzB;;MACIC,UAAU,GAAG,CAAjB,EAAoB;IAClBA,UAAU,GAAG,CAAb;;;MAEEC,OAAO,GAAG5lB,IAAI,CAACmlB,IAAL,CAAUQ,UAAV,CAAd;;MACI3B,KAAK,KAAKD,KAAd,EAAqB;IACnB6B,OAAO,GAAG,CAACA,OAAX;;;QAGIC,EAAE,GAAG,OAAOL,EAAE,GAAG3P,EAAZ,IAAkB+P,OAAO,IAAI9P,EAAE,GAAG2P,EAAT,CAApC;QACMK,EAAE,GAAG,OAAOL,EAAE,GAAG3P,EAAZ,IAAkB8P,OAAO,IAAI/P,EAAE,GAAG2P,EAAT,CAApC;QAEMO,GAAG,GAAG/lB,IAAI,CAACgmB,KAAL,CAAWP,EAAE,GAAGK,EAAhB,EAAoBN,EAAE,GAAGK,EAAzB,CAAZ;QACMI,GAAG,GAAGjmB,IAAI,CAACgmB,KAAL,CAAWlQ,EAAE,GAAGgQ,EAAhB,EAAoBjQ,EAAE,GAAGgQ,EAAzB,CAAZ;MAEIK,MAAM,GAAGD,GAAG,GAAGF,GAAnB;;MACIG,MAAM,GAAG,CAAT,IAAclC,KAAK,KAAK,CAA5B,EAA+B;IAC7BkC,MAAM,IAAI,IAAIlmB,IAAI,CAAC4kB,EAAnB;GADF,MAEO,IAAIsB,MAAM,GAAG,CAAT,IAAclC,KAAK,KAAK,CAA5B,EAA+B;IACpCkC,MAAM,IAAI,IAAIlmB,IAAI,CAAC4kB,EAAnB;;;QAGIuB,QAAQ,GAAGnmB,IAAI,CAACuQ,IAAL,CAAUvQ,IAAI,CAACilB,GAAL,CAASiB,MAAM,IAAIlmB,IAAI,CAAC4kB,EAAL,GAAU,GAAV,GAAgB,KAApB,CAAf,CAAV,CAAjB;QACMwB,MAAM,GAAG,EAAf;;OAEK,IAAIjoB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgoB,QAApB,EAA8BhoB,CAAC,EAA/B,EAAmC;UAC3BkoB,GAAG,GAAGN,GAAG,GAAI5nB,CAAC,GAAG+nB,MAAL,GAAeC,QAAjC;UACMG,GAAG,GAAGP,GAAG,GAAI,CAAC5nB,CAAC,GAAG,CAAL,IAAU+nB,MAAX,GAAqBC,QAAvC;IACAC,MAAM,CAACjoB,CAAD,CAAN,GAAY,CAAC0nB,EAAD,EAAKC,EAAL,EAASO,GAAT,EAAcC,GAAd,EAAmB1C,EAAnB,EAAuBC,EAAvB,EAA2BgB,MAA3B,EAAmCE,MAAnC,CAAZ;;;SAGKqB,MAAP;CAxDF;;AA2DA,MAAM7B,eAAe,GAAG,UAASlD,EAAT,EAAaC,EAAb,EAAiByE,GAAjB,EAAsBE,GAAtB,EAA2BrC,EAA3B,EAA+BC,EAA/B,EAAmCgB,MAAnC,EAA2CE,MAA3C,EAAmD;QACnEK,GAAG,GAAGL,MAAM,GAAGnB,EAArB;QACMyB,GAAG,GAAG,CAACR,MAAD,GAAUhB,EAAtB;QACMyB,GAAG,GAAGT,MAAM,GAAGjB,EAArB;QACM2B,GAAG,GAAGR,MAAM,GAAGlB,EAArB;QAEM0C,OAAO,GAAG,OAAON,GAAG,GAAGF,GAAb,CAAhB;QACMzD,CAAC,GACH,IAAI,CAAL,GAAUtiB,IAAI,CAAC8kB,GAAL,CAASyB,OAAO,GAAG,GAAnB,CAAV,GAAoCvmB,IAAI,CAAC8kB,GAAL,CAASyB,OAAO,GAAG,GAAnB,CAArC,GACAvmB,IAAI,CAAC8kB,GAAL,CAASyB,OAAT,CAFF;QAGM1Q,EAAE,GAAGwL,EAAE,GAAGrhB,IAAI,CAACglB,GAAL,CAASe,GAAT,CAAL,GAAqBzD,CAAC,GAAGtiB,IAAI,CAAC8kB,GAAL,CAASiB,GAAT,CAApC;QACMjQ,EAAE,GAAGwL,EAAE,GAAGthB,IAAI,CAAC8kB,GAAL,CAASiB,GAAT,CAAL,GAAqBzD,CAAC,GAAGtiB,IAAI,CAACglB,GAAL,CAASe,GAAT,CAApC;QACMS,EAAE,GAAGnF,EAAE,GAAGrhB,IAAI,CAACglB,GAAL,CAASiB,GAAT,CAAhB;QACMQ,EAAE,GAAGnF,EAAE,GAAGthB,IAAI,CAAC8kB,GAAL,CAASmB,GAAT,CAAhB;QACMlQ,EAAE,GAAGyQ,EAAE,GAAGlE,CAAC,GAAGtiB,IAAI,CAAC8kB,GAAL,CAASmB,GAAT,CAApB;QACMjQ,EAAE,GAAGyQ,EAAE,GAAGnE,CAAC,GAAGtiB,IAAI,CAACglB,GAAL,CAASiB,GAAT,CAApB;SAEO,CACLb,GAAG,GAAGvP,EAAN,GAAWwP,GAAG,GAAGvP,EADZ,EAELwP,GAAG,GAAGzP,EAAN,GAAW0P,GAAG,GAAGzP,EAFZ,EAGLsP,GAAG,GAAGrP,EAAN,GAAWsP,GAAG,GAAGrP,EAHZ,EAILsP,GAAG,GAAGvP,EAAN,GAAWwP,GAAG,GAAGvP,EAJZ,EAKLoP,GAAG,GAAGoB,EAAN,GAAWnB,GAAG,GAAGoB,EALZ,EAMLnB,GAAG,GAAGkB,EAAN,GAAWjB,GAAG,GAAGkB,EANZ,CAAP;CAjBF;;AA2BA,MAAMC,OAAN,CAAc;SACL7c,KAAP,CAAa4H,GAAb,EAAkBiR,IAAlB,EAAwB;UAChBQ,QAAQ,GAAGT,KAAK,CAACC,IAAD,CAAtB;IACA7Y,KAAK,CAACqZ,QAAD,EAAWzR,GAAX,CAAL;;;;;ACxZJ,MAAM;UAAE3R;IAAWzC,SAAnB;;;AAIA,MAAMspB,KAAK,GAAG,OAAO,CAAC3mB,IAAI,CAACmlB,IAAL,CAAU,CAAV,IAAe,GAAhB,IAAuB,GAA9B,CAAd;AACA,kBAAe;EACbyB,UAAU,GAAG;SACNlR,IAAL,GAAY,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAAZ,CADW;;WAEH,KAAKmR,SAAL,GAAiB,EAAzB;GAHW;;EAMbC,IAAI,GAAG;SACAD,SAAL,CAAezpB,IAAf,CAAoB,KAAKsY,IAAL,CAAU7X,KAAV,EAApB,EADK;;;WAGE,KAAK8X,UAAL,CAAgB,GAAhB,CAAP;GATW;;EAYboR,OAAO,GAAG;SACHrR,IAAL,GAAY,KAAKmR,SAAL,CAAeG,GAAf,MAAwB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAApC;WACO,KAAKrR,UAAL,CAAgB,GAAhB,CAAP;GAdW;;EAiBb8N,SAAS,GAAG;WACH,KAAK9N,UAAL,CAAgB,GAAhB,CAAP;GAlBW;;EAqBbsR,SAAS,CAACC,CAAD,EAAI;WACJ,KAAKvR,UAAL,CAAiB,GAAE7V,QAAM,CAAConB,CAAD,CAAI,IAA7B,CAAP;GAtBW;;EAyBbC,WAAW,EAAE;IACXC,IAAI,EAAE,CADK;IAEXC,KAAK,EAAE,CAFI;IAGXC,MAAM,EAAE;GA5BG;;EA+BbC,OAAO,CAACvoB,CAAD,EAAI;QACL,OAAOA,CAAP,KAAa,QAAjB,EAA2B;MACzBA,CAAC,GAAG,KAAKmoB,WAAL,CAAiBnoB,CAAC,CAACoG,WAAF,EAAjB,CAAJ;;;WAEK,KAAKuQ,UAAL,CAAiB,GAAE3W,CAAE,IAArB,CAAP;GAnCW;;EAsCbwoB,YAAY,EAAE;IACZC,KAAK,EAAE,CADK;IAEZJ,KAAK,EAAE,CAFK;IAGZK,KAAK,EAAE;GAzCI;;EA4CbC,QAAQ,CAACnX,CAAD,EAAI;QACN,OAAOA,CAAP,KAAa,QAAjB,EAA2B;MACzBA,CAAC,GAAG,KAAKgX,YAAL,CAAkBhX,CAAC,CAACpL,WAAF,EAAlB,CAAJ;;;WAEK,KAAKuQ,UAAL,CAAiB,GAAEnF,CAAE,IAArB,CAAP;GAhDW;;EAmDboX,UAAU,CAAChV,CAAD,EAAI;WACL,KAAK+C,UAAL,CAAiB,GAAE7V,QAAM,CAAC8S,CAAD,CAAI,IAA7B,CAAP;GApDW;;EAuDbiV,IAAI,CAAC5qB,MAAD,EAASf,OAAO,GAAG,EAAnB,EAAuB;UACnB4rB,cAAc,GAAG7qB,MAAvB;;QACI,CAACW,KAAK,CAAC6B,OAAN,CAAcxC,MAAd,CAAL,EAA4B;MAC1BA,MAAM,GAAG,CAACA,MAAD,EAASf,OAAO,CAACmb,KAAR,IAAiBpa,MAA1B,CAAT;;;UAGI8qB,KAAK,GAAG9qB,MAAM,CAAC+qB,KAAP,CAAahf,CAAC,IAAIif,MAAM,CAACC,QAAP,CAAgBlf,CAAhB,KAAsBA,CAAC,GAAG,CAA5C,CAAd;;QACI,CAAC+e,KAAL,EAAY;YACJ,IAAIhsB,KAAJ,CACH,QAAOosB,IAAI,CAACC,SAAL,CAAeN,cAAf,CAA+B,KAAIK,IAAI,CAACC,SAAL,CACzClsB,OADyC,CAEzC,0DAHE,CAAN;;;IAOFe,MAAM,GAAGA,MAAM,CAAC0C,GAAP,CAAWG,QAAX,EAAmBrC,IAAnB,CAAwB,GAAxB,CAAT;WACO,KAAKkY,UAAL,CAAiB,IAAG1Y,MAAO,KAAI6C,QAAM,CAAC5D,OAAO,CAACmsB,KAAR,IAAiB,CAAlB,CAAqB,IAA1D,CAAP;GAvEW;;EA0EbC,MAAM,GAAG;WACA,KAAK3S,UAAL,CAAgB,QAAhB,CAAP;GA3EW;;EA8EbyN,MAAM,CAACpa,CAAD,EAAI0a,CAAJ,EAAO;WACJ,KAAK/N,UAAL,CAAiB,GAAE7V,QAAM,CAACkJ,CAAD,CAAI,IAAGlJ,QAAM,CAAC4jB,CAAD,CAAI,IAA1C,CAAP;GA/EW;;EAkFbF,MAAM,CAACxa,CAAD,EAAI0a,CAAJ,EAAO;WACJ,KAAK/N,UAAL,CAAiB,GAAE7V,QAAM,CAACkJ,CAAD,CAAI,IAAGlJ,QAAM,CAAC4jB,CAAD,CAAI,IAA1C,CAAP;GAnFW;;EAsFbL,aAAa,CAACkF,IAAD,EAAOC,IAAP,EAAaC,IAAb,EAAmBC,IAAnB,EAAyB1f,CAAzB,EAA4B0a,CAA5B,EAA+B;WACnC,KAAK/N,UAAL,CACJ,GAAE7V,QAAM,CAACyoB,IAAD,CAAO,IAAGzoB,QAAM,CAAC0oB,IAAD,CAAO,IAAG1oB,QAAM,CAAC2oB,IAAD,CAAO,IAAG3oB,QAAM,CAAC4oB,IAAD,CAAO,IAAG5oB,QAAM,CACvEkJ,CADuE,CAEvE,IAAGlJ,QAAM,CAAC4jB,CAAD,CAAI,IAHV,CAAP;GAvFW;;EA8FbJ,gBAAgB,CAACqF,GAAD,EAAMC,GAAN,EAAW5f,CAAX,EAAc0a,CAAd,EAAiB;WACxB,KAAK/N,UAAL,CACJ,GAAE7V,QAAM,CAAC6oB,GAAD,CAAM,IAAG7oB,QAAM,CAAC8oB,GAAD,CAAM,IAAG9oB,QAAM,CAACkJ,CAAD,CAAI,IAAGlJ,QAAM,CAAC4jB,CAAD,CAAI,IADnD,CAAP;GA/FW;;EAoGbmF,IAAI,CAAC7f,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa;WACR,KAAKpM,UAAL,CACJ,GAAE7V,QAAM,CAACkJ,CAAD,CAAI,IAAGlJ,QAAM,CAAC4jB,CAAD,CAAI,IAAG5jB,QAAM,CAAConB,CAAD,CAAI,IAAGpnB,QAAM,CAACiiB,CAAD,CAAI,KAD/C,CAAP;GArGW;;EA0Gb+G,WAAW,CAAC9f,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa9V,CAAb,EAAgB;QACrBA,CAAC,IAAI,IAAT,EAAe;MACbA,CAAC,GAAG,CAAJ;;;IAEFA,CAAC,GAAGjM,IAAI,CAACkP,GAAL,CAASjD,CAAT,EAAY,MAAMib,CAAlB,EAAqB,MAAMnF,CAA3B,CAAJ,CAJyB;;UAOnB/iB,CAAC,GAAGiN,CAAC,IAAI,MAAM0a,KAAV,CAAX;SAEKvD,MAAL,CAAYpa,CAAC,GAAGiD,CAAhB,EAAmByX,CAAnB;SACKF,MAAL,CAAYxa,CAAC,GAAGke,CAAJ,GAAQjb,CAApB,EAAuByX,CAAvB;SACKL,aAAL,CAAmBra,CAAC,GAAGke,CAAJ,GAAQloB,CAA3B,EAA8B0kB,CAA9B,EAAiC1a,CAAC,GAAGke,CAArC,EAAwCxD,CAAC,GAAG1kB,CAA5C,EAA+CgK,CAAC,GAAGke,CAAnD,EAAsDxD,CAAC,GAAGzX,CAA1D;SACKuX,MAAL,CAAYxa,CAAC,GAAGke,CAAhB,EAAmBxD,CAAC,GAAG3B,CAAJ,GAAQ9V,CAA3B;SACKoX,aAAL,CAAmBra,CAAC,GAAGke,CAAvB,EAA0BxD,CAAC,GAAG3B,CAAJ,GAAQ/iB,CAAlC,EAAqCgK,CAAC,GAAGke,CAAJ,GAAQloB,CAA7C,EAAgD0kB,CAAC,GAAG3B,CAApD,EAAuD/Y,CAAC,GAAGke,CAAJ,GAAQjb,CAA/D,EAAkEyX,CAAC,GAAG3B,CAAtE;SACKyB,MAAL,CAAYxa,CAAC,GAAGiD,CAAhB,EAAmByX,CAAC,GAAG3B,CAAvB;SACKsB,aAAL,CAAmBra,CAAC,GAAGhK,CAAvB,EAA0B0kB,CAAC,GAAG3B,CAA9B,EAAiC/Y,CAAjC,EAAoC0a,CAAC,GAAG3B,CAAJ,GAAQ/iB,CAA5C,EAA+CgK,CAA/C,EAAkD0a,CAAC,GAAG3B,CAAJ,GAAQ9V,CAA1D;SACKuX,MAAL,CAAYxa,CAAZ,EAAe0a,CAAC,GAAGzX,CAAnB;SACKoX,aAAL,CAAmBra,CAAnB,EAAsB0a,CAAC,GAAG1kB,CAA1B,EAA6BgK,CAAC,GAAGhK,CAAjC,EAAoC0kB,CAApC,EAAuC1a,CAAC,GAAGiD,CAA3C,EAA8CyX,CAA9C;WACO,KAAKD,SAAL,EAAP;GA5HW;;EA+HbsF,OAAO,CAAC/f,CAAD,EAAI0a,CAAJ,EAAOnN,EAAP,EAAWC,EAAX,EAAe;;QAEhBA,EAAE,IAAI,IAAV,EAAgB;MACdA,EAAE,GAAGD,EAAL;;;IAEFvN,CAAC,IAAIuN,EAAL;IACAmN,CAAC,IAAIlN,EAAL;UACMiO,EAAE,GAAGlO,EAAE,GAAGoQ,KAAhB;UACMjC,EAAE,GAAGlO,EAAE,GAAGmQ,KAAhB;UACMqC,EAAE,GAAGhgB,CAAC,GAAGuN,EAAE,GAAG,CAApB;UACM0S,EAAE,GAAGvF,CAAC,GAAGlN,EAAE,GAAG,CAApB;UACM0S,EAAE,GAAGlgB,CAAC,GAAGuN,EAAf;UACM4S,EAAE,GAAGzF,CAAC,GAAGlN,EAAf;SAEK4M,MAAL,CAAYpa,CAAZ,EAAemgB,EAAf;SACK9F,aAAL,CAAmBra,CAAnB,EAAsBmgB,EAAE,GAAGzE,EAA3B,EAA+BwE,EAAE,GAAGzE,EAApC,EAAwCf,CAAxC,EAA2CwF,EAA3C,EAA+CxF,CAA/C;SACKL,aAAL,CAAmB6F,EAAE,GAAGzE,EAAxB,EAA4Bf,CAA5B,EAA+BsF,EAA/B,EAAmCG,EAAE,GAAGzE,EAAxC,EAA4CsE,EAA5C,EAAgDG,EAAhD;SACK9F,aAAL,CAAmB2F,EAAnB,EAAuBG,EAAE,GAAGzE,EAA5B,EAAgCwE,EAAE,GAAGzE,EAArC,EAAyCwE,EAAzC,EAA6CC,EAA7C,EAAiDD,EAAjD;SACK5F,aAAL,CAAmB6F,EAAE,GAAGzE,EAAxB,EAA4BwE,EAA5B,EAAgCjgB,CAAhC,EAAmCmgB,EAAE,GAAGzE,EAAxC,EAA4C1b,CAA5C,EAA+CmgB,EAA/C;WACO,KAAK1F,SAAL,EAAP;GAlJW;;EAqJb2F,MAAM,CAACpgB,CAAD,EAAI0a,CAAJ,EAAO2F,MAAP,EAAe;WACZ,KAAKN,OAAL,CAAa/f,CAAb,EAAgB0a,CAAhB,EAAmB2F,MAAnB,CAAP;GAtJW;;EAyJbC,GAAG,CAACtgB,CAAD,EAAI0a,CAAJ,EAAO2F,MAAP,EAAeE,UAAf,EAA2BC,QAA3B,EAAqCC,aAArC,EAAoD;QACjDA,aAAa,IAAI,IAArB,EAA2B;MACzBA,aAAa,GAAG,KAAhB;;;UAEIC,MAAM,GAAG,MAAM1pB,IAAI,CAAC4kB,EAA1B;UACM+E,OAAO,GAAG,MAAM3pB,IAAI,CAAC4kB,EAA3B;QAEIgF,QAAQ,GAAGJ,QAAQ,GAAGD,UAA1B;;QAEIvpB,IAAI,CAACilB,GAAL,CAAS2E,QAAT,IAAqBF,MAAzB,EAAiC;;MAE/BE,QAAQ,GAAGF,MAAX;KAFF,MAGO,IAAIE,QAAQ,KAAK,CAAb,IAAkBH,aAAa,KAAKG,QAAQ,GAAG,CAAnD,EAAsD;;YAErDC,GAAG,GAAGJ,aAAa,GAAG,CAAC,CAAJ,GAAQ,CAAjC;MACAG,QAAQ,GAAGC,GAAG,GAAGH,MAAN,GAAeE,QAA1B;;;UAGIE,OAAO,GAAG9pB,IAAI,CAACuQ,IAAL,CAAUvQ,IAAI,CAACilB,GAAL,CAAS2E,QAAT,IAAqBD,OAA/B,CAAhB;UACMI,MAAM,GAAGH,QAAQ,GAAGE,OAA1B;UACME,SAAS,GAAID,MAAM,GAAGJ,OAAV,GAAqBhD,KAArB,GAA6B0C,MAA/C;QACIY,MAAM,GAAGV,UAAb,CArBqD;;QAwBjDW,OAAO,GAAG,CAAClqB,IAAI,CAAC8kB,GAAL,CAASmF,MAAT,CAAD,GAAoBD,SAAlC;QACIG,OAAO,GAAGnqB,IAAI,CAACglB,GAAL,CAASiF,MAAT,IAAmBD,SAAjC,CAzBqD;;QA4BjDI,EAAE,GAAGphB,CAAC,GAAGhJ,IAAI,CAACglB,GAAL,CAASiF,MAAT,IAAmBZ,MAAhC;QACIgB,EAAE,GAAG3G,CAAC,GAAG1jB,IAAI,CAAC8kB,GAAL,CAASmF,MAAT,IAAmBZ,MAAhC,CA7BqD;;SAgChDjG,MAAL,CAAYgH,EAAZ,EAAgBC,EAAhB;;SAEK,IAAIC,MAAM,GAAG,CAAlB,EAAqBA,MAAM,GAAGR,OAA9B,EAAuCQ,MAAM,EAA7C,EAAiD;;YAEzC/B,IAAI,GAAG6B,EAAE,GAAGF,OAAlB;YACM1B,IAAI,GAAG6B,EAAE,GAAGF,OAAlB,CAH+C;;MAM/CF,MAAM,IAAIF,MAAV,CAN+C;;MAS/CK,EAAE,GAAGphB,CAAC,GAAGhJ,IAAI,CAACglB,GAAL,CAASiF,MAAT,IAAmBZ,MAA5B;MACAgB,EAAE,GAAG3G,CAAC,GAAG1jB,IAAI,CAAC8kB,GAAL,CAASmF,MAAT,IAAmBZ,MAA5B,CAV+C;;MAa/Ca,OAAO,GAAG,CAAClqB,IAAI,CAAC8kB,GAAL,CAASmF,MAAT,CAAD,GAAoBD,SAA9B;MACAG,OAAO,GAAGnqB,IAAI,CAACglB,GAAL,CAASiF,MAAT,IAAmBD,SAA7B,CAd+C;;YAiBzCvB,IAAI,GAAG2B,EAAE,GAAGF,OAAlB;YACMxB,IAAI,GAAG2B,EAAE,GAAGF,OAAlB,CAlB+C;;WAqB1C9G,aAAL,CAAmBkF,IAAnB,EAAyBC,IAAzB,EAA+BC,IAA/B,EAAqCC,IAArC,EAA2C0B,EAA3C,EAA+CC,EAA/C;;;WAGK,IAAP;GAnNW;;EAsNbE,OAAO,CAAC,GAAGC,MAAJ,EAAY;SACZpH,MAAL,CAAY,IAAIoH,MAAM,CAACC,KAAP,MAAkB,EAAtB,CAAZ;;SACK,IAAIC,KAAT,IAAkBF,MAAlB,EAA0B;WACnBhH,MAAL,CAAY,IAAIkH,KAAK,IAAI,EAAb,CAAZ;;;WAEK,KAAKjH,SAAL,EAAP;GA3NW;;EA8Nbf,IAAI,CAACA,IAAD,EAAO;IACTgE,OAAO,CAAC7c,KAAR,CAAc,IAAd,EAAoB6Y,IAApB;WACO,IAAP;GAhOW;;EAmObiI,YAAY,CAACC,IAAD,EAAO;QACb,YAAYC,IAAZ,CAAiBD,IAAjB,CAAJ,EAA4B;aACnB,GAAP;;;WAGK,EAAP;GAxOW;;EA2ObE,IAAI,CAAC/Y,KAAD,EAAQ6Y,IAAR,EAAc;QACZ,0BAA0BC,IAA1B,CAA+B9Y,KAA/B,CAAJ,EAA2C;MACzC6Y,IAAI,GAAG7Y,KAAP;MACAA,KAAK,GAAG,IAAR;;;QAGEA,KAAJ,EAAW;WACJuF,SAAL,CAAevF,KAAf;;;WAEK,KAAK4D,UAAL,CAAiB,IAAG,KAAKgV,YAAL,CAAkBC,IAAlB,CAAwB,EAA5C,CAAP;GApPW;;EAuPbzT,MAAM,CAACpF,KAAD,EAAQ;QACRA,KAAJ,EAAW;WACJ2F,WAAL,CAAiB3F,KAAjB;;;WAEK,KAAK4D,UAAL,CAAgB,GAAhB,CAAP;GA3PW;;EA8PboV,aAAa,CAACzT,SAAD,EAAYI,WAAZ,EAAyBkT,IAAzB,EAA+B;QACtClT,WAAW,IAAI,IAAnB,EAAyB;MACvBA,WAAW,GAAGJ,SAAd;;;UAEI0T,UAAU,GAAG,yBAAnB;;QACIA,UAAU,CAACH,IAAX,CAAgBvT,SAAhB,CAAJ,EAAgC;MAC9BsT,IAAI,GAAGtT,SAAP;MACAA,SAAS,GAAG,IAAZ;;;QAGE0T,UAAU,CAACH,IAAX,CAAgBnT,WAAhB,CAAJ,EAAkC;MAChCkT,IAAI,GAAGlT,WAAP;MACAA,WAAW,GAAGJ,SAAd;;;QAGEA,SAAJ,EAAe;WACRA,SAAL,CAAeA,SAAf;WACKI,WAAL,CAAiBA,WAAjB;;;WAGK,KAAK/B,UAAL,CAAiB,IAAG,KAAKgV,YAAL,CAAkBC,IAAlB,CAAwB,EAA5C,CAAP;GAlRW;;EAqRbK,IAAI,CAACL,IAAD,EAAO;WACF,KAAKjV,UAAL,CAAiB,IAAG,KAAKgV,YAAL,CAAkBC,IAAlB,CAAwB,IAA5C,CAAP;GAtRW;;EAyRbhZ,SAAS,CAACS,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6B;;UAE9BE,CAAC,GAAG,KAAK8C,IAAf;UACM,CAACN,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiBC,EAAjB,EAAqBC,EAArB,IAA2B7C,CAAjC;IACAA,CAAC,CAAC,CAAD,CAAD,GAAOwC,EAAE,GAAG/C,GAAL,GAAWiD,EAAE,GAAGhD,GAAvB;IACAM,CAAC,CAAC,CAAD,CAAD,GAAOyC,EAAE,GAAGhD,GAAL,GAAWkD,EAAE,GAAGjD,GAAvB;IACAM,CAAC,CAAC,CAAD,CAAD,GAAOwC,EAAE,GAAG7C,GAAL,GAAW+C,EAAE,GAAG9C,GAAvB;IACAI,CAAC,CAAC,CAAD,CAAD,GAAOyC,EAAE,GAAG9C,GAAL,GAAWgD,EAAE,GAAG/C,GAAvB;IACAI,CAAC,CAAC,CAAD,CAAD,GAAOwC,EAAE,GAAG3C,EAAL,GAAU6C,EAAE,GAAG5C,EAAf,GAAoB8C,EAA3B;IACA5C,CAAC,CAAC,CAAD,CAAD,GAAOyC,EAAE,GAAG5C,EAAL,GAAU8C,EAAE,GAAG7C,EAAf,GAAoB+C,EAA3B;UAEMyV,MAAM,GAAG,CAAC7Y,GAAD,EAAMC,GAAN,EAAWC,GAAX,EAAgBC,GAAhB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6B/S,GAA7B,CAAiCqM,CAAC,IAAIlM,QAAM,CAACkM,CAAD,CAA5C,EAAiDvO,IAAjD,CAAsD,GAAtD,CAAf;WACO,KAAKkY,UAAL,CAAiB,GAAEuV,MAAO,KAA1B,CAAP;GArSW;;EAwSbC,SAAS,CAACniB,CAAD,EAAI0a,CAAJ,EAAO;WACP,KAAK9R,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,EAA2B5I,CAA3B,EAA8B0a,CAA9B,CAAP;GAzSW;;EA4Sb0H,MAAM,CAACC,KAAD,EAAQnvB,OAAO,GAAG,EAAlB,EAAsB;QACtBwnB,CAAJ;UACM4H,GAAG,GAAID,KAAK,GAAGrrB,IAAI,CAAC4kB,EAAd,GAAoB,GAAhC;UACMI,GAAG,GAAGhlB,IAAI,CAACglB,GAAL,CAASsG,GAAT,CAAZ;UACMxG,GAAG,GAAG9kB,IAAI,CAAC8kB,GAAL,CAASwG,GAAT,CAAZ;QACItiB,CAAC,GAAI0a,CAAC,GAAG,CAAb;;QAEIxnB,OAAO,CAACqvB,MAAR,IAAkB,IAAtB,EAA4B;OACzBviB,CAAD,EAAI0a,CAAJ,IAASxnB,OAAO,CAACqvB,MAAjB;YACM1V,EAAE,GAAG7M,CAAC,GAAGgc,GAAJ,GAAUtB,CAAC,GAAGoB,GAAzB;YACMhP,EAAE,GAAG9M,CAAC,GAAG8b,GAAJ,GAAUpB,CAAC,GAAGsB,GAAzB;MACAhc,CAAC,IAAI6M,EAAL;MACA6N,CAAC,IAAI5N,EAAL;;;WAGK,KAAKlE,SAAL,CAAeoT,GAAf,EAAoBF,GAApB,EAAyB,CAACA,GAA1B,EAA+BE,GAA/B,EAAoChc,CAApC,EAAuC0a,CAAvC,CAAP;GA3TW;;EA8Tb8H,KAAK,CAACC,OAAD,EAAUC,OAAV,EAAmBxvB,OAAO,GAAG,EAA7B,EAAiC;QAChCwnB,CAAJ;;QACIgI,OAAO,IAAI,IAAf,EAAqB;MACnBA,OAAO,GAAGD,OAAV;;;QAEE,OAAOC,OAAP,KAAmB,QAAvB,EAAiC;MAC/BxvB,OAAO,GAAGwvB,OAAV;MACAA,OAAO,GAAGD,OAAV;;;QAGEziB,CAAC,GAAI0a,CAAC,GAAG,CAAb;;QACIxnB,OAAO,CAACqvB,MAAR,IAAkB,IAAtB,EAA4B;OACzBviB,CAAD,EAAI0a,CAAJ,IAASxnB,OAAO,CAACqvB,MAAjB;MACAviB,CAAC,IAAIyiB,OAAO,GAAGziB,CAAf;MACA0a,CAAC,IAAIgI,OAAO,GAAGhI,CAAf;;;WAGK,KAAK9R,SAAL,CAAe6Z,OAAf,EAAwB,CAAxB,EAA2B,CAA3B,EAA8BC,OAA9B,EAAuC1iB,CAAvC,EAA0C0a,CAA1C,CAAP;;;CA/UJ;;ACNA,MAAMiI,YAAY,GAAG;OACd,GADc;QAEb,GAFa;QAGb,GAHa;QAIb,GAJa;QAKb,GALa;QAMb,GANa;QAOb,GAPa;QAQb,GARa;QASb,GATa;QAUb,GAVa;QAWb,GAXa;QAYb,GAZa;QAab,GAba;QAcb,GAda;QAeb,GAfa;QAgBb,GAhBa;QAiBb,GAjBa;OAkBd,GAlBc;QAmBb,GAnBa;OAoBd,GApBc;OAqBd,GArBc;OAsBd,GAtBc;OAuBd,GAvBc;OAwBd,GAxBc;OAyBd,GAzBc;OA0Bd,GA1Bc;OA2Bd;CA3BP;AA8BA,MAAMC,UAAU,GAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAD,CAwEjBC,KAxEiB,CAwEX,KAxEW,CAAnB;;AA0EA,MAAMC,OAAN,CAAc;SACLC,IAAP,CAAYC,QAAZ,EAAsB;WACb,IAAIF,OAAJ,CAAYG,EAAE,CAACC,YAAH,CAAgBF,QAAhB,EAA0B,MAA1B,CAAZ,CAAP;;;EAGF/vB,WAAW,CAACkwB,QAAD,EAAW;SACfA,QAAL,GAAgBA,QAAhB;SACKC,UAAL,GAAkB,EAAlB;SACKC,WAAL,GAAmB,EAAnB;SACKC,aAAL,GAAqB,EAArB;SACKC,SAAL,GAAiB,EAAjB;SAEK9J,KAAL,GAPoB;;SASf+J,UAAL,GAAkB,IAAI5uB,KAAJ,CAAU,GAAV,CAAlB;;SACK,IAAI6uB,IAAI,GAAG,CAAhB,EAAmBA,IAAI,IAAI,GAA3B,EAAgCA,IAAI,EAApC,EAAwC;WACjCD,UAAL,CAAgBC,IAAhB,IAAwB,KAAKJ,WAAL,CAAiBT,UAAU,CAACa,IAAD,CAA3B,CAAxB;;;SAGGC,IAAL,GAAY,KAAKN,UAAL,CAAgB,UAAhB,EAA4BP,KAA5B,CAAkC,KAAlC,EAAyClsB,GAAzC,CAA6CC,CAAC,IAAI,CAACA,CAAnD,CAAZ;SACK+sB,QAAL,GAAgB,EAAE,KAAKP,UAAL,CAAgB,UAAhB,KAA+B,CAAjC,CAAhB;SACKQ,SAAL,GAAiB,EAAE,KAAKR,UAAL,CAAgB,WAAhB,KAAgC,CAAlC,CAAjB;SACKS,OAAL,GAAe,EAAE,KAAKT,UAAL,CAAgB,SAAhB,KAA8B,CAAhC,CAAf;SACKU,SAAL,GAAiB,EAAE,KAAKV,UAAL,CAAgB,WAAhB,KAAgC,CAAlC,CAAjB;SACKW,OAAL,GACE,KAAKL,IAAL,CAAU,CAAV,IAAe,KAAKA,IAAL,CAAU,CAAV,CAAf,IAA+B,KAAKC,QAAL,GAAgB,KAAKC,SAApD,CADF;;;EAIFnK,KAAK,GAAG;QACFuK,OAAO,GAAG,EAAd;;SACK,IAAIC,IAAT,IAAiB,KAAKd,QAAL,CAAcN,KAAd,CAAoB,IAApB,CAAjB,EAA4C;UACtCqB,KAAJ;UACIrwB,CAAJ;;UACKqwB,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,aAAX,CAAb,EAAyC;QACvCF,OAAO,GAAGE,KAAK,CAAC,CAAD,CAAf;;OADF,MAGO,IAAKA,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,WAAX,CAAb,EAAuC;QAC5CF,OAAO,GAAG,EAAV;;;;cAIMA,OAAR;aACO,aAAL;UACEE,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,eAAX,CAAR;cACI5wB,GAAG,GAAG4wB,KAAK,CAAC,CAAD,CAAf;cACI5lB,KAAK,GAAG4lB,KAAK,CAAC,CAAD,CAAjB;;cAEKrwB,CAAC,GAAG,KAAKuvB,UAAL,CAAgB9vB,GAAhB,CAAT,EAAgC;gBAC1B,CAACsB,KAAK,CAAC6B,OAAN,CAAc5C,CAAd,CAAL,EAAuB;cACrBA,CAAC,GAAG,KAAKuvB,UAAL,CAAgB9vB,GAAhB,IAAuB,CAACO,CAAD,CAA3B;;;YAEFA,CAAC,CAACO,IAAF,CAAOkK,KAAP;WAJF,MAKO;iBACA8kB,UAAL,CAAgB9vB,GAAhB,IAAuBgL,KAAvB;;;;;aAIC,aAAL;cACM,CAAC,SAASujB,IAAT,CAAcoC,IAAd,CAAL,EAA0B;;;;cAGtBpV,IAAI,GAAGoV,IAAI,CAACC,KAAL,CAAW,oBAAX,EAAiC,CAAjC,CAAX;eACKb,WAAL,CAAiBxU,IAAjB,IAAyB,CAACoV,IAAI,CAACC,KAAL,CAAW,kBAAX,EAA+B,CAA/B,CAA1B;;;aAGG,WAAL;UACEA,KAAK,GAAGD,IAAI,CAACC,KAAL,CAAW,sCAAX,CAAR;;cACIA,KAAJ,EAAW;iBACJX,SAAL,CAAeW,KAAK,CAAC,CAAD,CAAL,GAAW,IAAX,GAAkBA,KAAK,CAAC,CAAD,CAAtC,IAA6CnW,QAAQ,CAACmW,KAAK,CAAC,CAAD,CAAN,CAArD;;;;;;;;EAOVC,UAAU,CAACC,IAAD,EAAO;UACTC,GAAG,GAAG,EAAZ;;SACK,IAAIlvB,CAAC,GAAG,CAAR,EAAWmvB,GAAG,GAAGF,IAAI,CAACnwB,MAA3B,EAAmCkB,CAAC,GAAGmvB,GAAvC,EAA4CnvB,CAAC,EAA7C,EAAiD;UAC3CsuB,IAAI,GAAGW,IAAI,CAAC1uB,UAAL,CAAgBP,CAAhB,CAAX;MACAsuB,IAAI,GAAGd,YAAY,CAACc,IAAD,CAAZ,IAAsBA,IAA7B;MACAY,GAAG,CAACjwB,IAAJ,CAASqvB,IAAI,CAAC3wB,QAAL,CAAc,EAAd,CAAT;;;WAGKuxB,GAAP;;;EAGFE,eAAe,CAAC/uB,MAAD,EAAS;UAChBgvB,MAAM,GAAG,EAAf;;SAEK,IAAIrvB,CAAC,GAAG,CAAR,EAAWmvB,GAAG,GAAG9uB,MAAM,CAACvB,MAA7B,EAAqCkB,CAAC,GAAGmvB,GAAzC,EAA8CnvB,CAAC,EAA/C,EAAmD;YAC3CsvB,QAAQ,GAAGjvB,MAAM,CAACE,UAAP,CAAkBP,CAAlB,CAAjB;MACAqvB,MAAM,CAACpwB,IAAP,CAAY,KAAKswB,gBAAL,CAAsBD,QAAtB,CAAZ;;;WAGKD,MAAP;;;EAGFE,gBAAgB,CAAC3lB,SAAD,EAAY;WACnB6jB,UAAU,CAACD,YAAY,CAAC5jB,SAAD,CAAZ,IAA2BA,SAA5B,CAAV,IAAoD,SAA3D;;;EAGF4lB,YAAY,CAACC,KAAD,EAAQ;WACX,KAAKvB,WAAL,CAAiBuB,KAAjB,KAA2B,CAAlC;;;EAGFC,WAAW,CAACnsB,IAAD,EAAOE,KAAP,EAAc;WAChB,KAAK2qB,SAAL,CAAe7qB,IAAI,GAAG,IAAP,GAAcE,KAA7B,KAAuC,CAA9C;;;EAGFksB,iBAAiB,CAACN,MAAD,EAAS;UAClBO,QAAQ,GAAG,EAAjB;;SAEK,IAAI9c,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGuc,MAAM,CAACvwB,MAAnC,EAA2CgU,KAAK,EAAhD,EAAoD;YAC5CvP,IAAI,GAAG8rB,MAAM,CAACvc,KAAD,CAAnB;YACMrP,KAAK,GAAG4rB,MAAM,CAACvc,KAAK,GAAG,CAAT,CAApB;MACA8c,QAAQ,CAAC3wB,IAAT,CAAc,KAAKuwB,YAAL,CAAkBjsB,IAAlB,IAA0B,KAAKmsB,WAAL,CAAiBnsB,IAAjB,EAAuBE,KAAvB,CAAxC;;;WAGKmsB,QAAP;;;;;AChOJ,MAAMC,OAAN,CAAc;EACZ/xB,WAAW,GAAG;;EAEdgX,MAAM,GAAG;UACD,IAAIlX,KAAJ,CAAU,mCAAV,CAAN;;;EAGFkyB,aAAa,GAAG;UACR,IAAIlyB,KAAJ,CAAU,mCAAV,CAAN;;;EAGFyJ,GAAG,GAAG;WACG,KAAKG,UAAL,IAAmB,IAAnB,GACH,KAAKA,UADF,GAEF,KAAKA,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,EAFvB;;;EAKF1E,QAAQ,GAAG;QACL,KAAK6Q,QAAL,IAAiB,KAAKhM,UAAL,IAAmB,IAAxC,EAA8C;;;;SAIzCgN,KAAL;WACQ,KAAKhB,QAAL,GAAgB,IAAxB;;;EAGFgB,KAAK,GAAG;UACA,IAAI5W,KAAJ,CAAU,mCAAV,CAAN;;;EAGFmyB,UAAU,CAACnpB,IAAD,EAAOopB,UAAP,EAAmB;QACvBA,UAAU,IAAI,IAAlB,EAAwB;MACtBA,UAAU,GAAG,KAAb;;;UAEIC,GAAG,GAAGD,UAAU,GAAG,KAAKpB,OAAR,GAAkB,CAAxC;WACQ,CAAC,KAAKJ,QAAL,GAAgByB,GAAhB,GAAsB,KAAKxB,SAA5B,IAAyC,IAA1C,GAAkD7nB,IAAzD;;;;;AC9BJ,MAAMspB,cAAc,GAAG;EACrBC,OAAO,GAAG;WACDrC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,mBAA5B,EAAiD,MAAjD,CAAP;GAFmB;;mBAIJ;WACRtC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,wBAA5B,EAAsD,MAAtD,CAAP;GALmB;;sBAOD;WACXtC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,2BAA5B,EAAyD,MAAzD,CAAP;GARmB;;0BAUG;WACftC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,+BAA5B,EAA6D,MAA7D,CAAP;GAXmB;;EAarBC,SAAS,GAAG;WACHvC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,qBAA5B,EAAmD,MAAnD,CAAP;GAdmB;;qBAgBF;WACVtC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,0BAA5B,EAAwD,MAAxD,CAAP;GAjBmB;;wBAmBC;WACbtC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,6BAA5B,EAA2D,MAA3D,CAAP;GApBmB;;4BAsBK;WACjBtC,EAAE,CAACC,YAAH,CACLqC,SAAS,GAAG,iCADP,EAEL,MAFK,CAAP;GAvBmB;;kBA4BL;WACPtC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,uBAA5B,EAAqD,MAArD,CAAP;GA7BmB;;iBA+BN;WACNtC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,sBAA5B,EAAoD,MAApD,CAAP;GAhCmB;;mBAkCJ;WACRtC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,wBAA5B,EAAsD,MAAtD,CAAP;GAnCmB;;uBAqCA;WACZtC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,4BAA5B,EAA0D,MAA1D,CAAP;GAtCmB;;EAwCrBE,MAAM,GAAG;WACAxC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,kBAA5B,EAAgD,MAAhD,CAAP;GAzCmB;;EA2CrBG,YAAY,GAAG;WACNzC,EAAE,CAACC,YAAH,CAAgBqC,SAAS,GAAG,wBAA5B,EAAsD,MAAtD,CAAP;;;CA5CJ;;AAgDA,MAAMI,YAAN,SAA2BX,OAA3B,CAAmC;EACjC/xB,WAAW,CAACkE,QAAD,EAAW0X,IAAX,EAAiBzX,EAAjB,EAAqB;;SAEzBD,QAAL,GAAgBA,QAAhB;SACK0X,IAAL,GAAYA,IAAZ;SACKzX,EAAL,GAAUA,EAAV;SACKwuB,IAAL,GAAY,IAAI9C,OAAJ,CAAYuC,cAAc,CAAC,KAAKxW,IAAN,CAAd,EAAZ,CAAZ;KACC;MACC8U,QAAQ,EAAE,KAAKA,QADhB;MAECC,SAAS,EAAE,KAAKA,SAFjB;MAGCF,IAAI,EAAE,KAAKA,IAHZ;MAICK,OAAO,EAAE,KAAKA,OAJf;MAKCF,OAAO,EAAE,KAAKA,OALf;MAMCC,SAAS,EAAE,KAAKA;QACd,KAAK8B,IAPT;;;EAUFjc,KAAK,GAAG;SACDhN,UAAL,CAAgBtF,IAAhB,GAAuB;MACrBuF,IAAI,EAAE,MADe;MAErBipB,QAAQ,EAAE,KAAKhX,IAFM;MAGrB1D,OAAO,EAAE,OAHY;MAIrB2a,QAAQ,EAAE;KAJZ;WAOO,KAAKnpB,UAAL,CAAgBvH,GAAhB,EAAP;;;EAGF6U,MAAM,CAACma,IAAD,EAAO;UACL2B,OAAO,GAAG,KAAKH,IAAL,CAAUzB,UAAV,CAAqBC,IAArB,CAAhB;UACMI,MAAM,GAAG,KAAKoB,IAAL,CAAUrB,eAAV,CAA2B,GAAEH,IAAK,EAAlC,CAAf;UACMW,QAAQ,GAAG,KAAKa,IAAL,CAAUd,iBAAV,CAA4BN,MAA5B,CAAjB;UACMwB,SAAS,GAAG,EAAlB;;SACK,IAAI7wB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqvB,MAAM,CAACvwB,MAA3B,EAAmCkB,CAAC,EAApC,EAAwC;YAChCyvB,KAAK,GAAGJ,MAAM,CAACrvB,CAAD,CAApB;MACA6wB,SAAS,CAAC5xB,IAAV,CAAe;QACb6xB,QAAQ,EAAElB,QAAQ,CAAC5vB,CAAD,CADL;QAEb+wB,QAAQ,EAAE,CAFG;QAGbC,OAAO,EAAE,CAHI;QAIbC,OAAO,EAAE,CAJI;QAKbC,YAAY,EAAE,KAAKT,IAAL,CAAUjB,YAAV,CAAuBC,KAAvB;OALhB;;;WASK,CAACmB,OAAD,EAAUC,SAAV,CAAP;;;EAGFf,aAAa,CAACzvB,MAAD,EAASuG,IAAT,EAAe;UACpByoB,MAAM,GAAG,KAAKoB,IAAL,CAAUrB,eAAV,CAA2B,GAAE/uB,MAAO,EAApC,CAAf;UACMuvB,QAAQ,GAAG,KAAKa,IAAL,CAAUd,iBAAV,CAA4BN,MAA5B,CAAjB;QAEInoB,KAAK,GAAG,CAAZ;;SACK,IAAIiqB,OAAT,IAAoBvB,QAApB,EAA8B;MAC5B1oB,KAAK,IAAIiqB,OAAT;;;UAGI9D,KAAK,GAAGzmB,IAAI,GAAG,IAArB;WACOM,KAAK,GAAGmmB,KAAf;;;SAGK+D,cAAP,CAAsB1X,IAAtB,EAA4B;WACnBA,IAAI,IAAIwW,cAAf;;;;;AChHJ,MAAMmB,KAAK,GAAG,UAASC,GAAT,EAAc;SAClB,OAAMA,GAAG,CAAC3zB,QAAJ,CAAa,EAAb,CAAiB,EAAxB,CAA0B+B,KAA1B,CAAgC,CAAC,CAAjC,CAAP;CADF;;AAIA,MAAM6xB,YAAN,SAA2B1B,OAA3B,CAAmC;EACjC/xB,WAAW,CAACkE,QAAD,EAAWyuB,IAAX,EAAiBxuB,EAAjB,EAAqB;;SAEzBD,QAAL,GAAgBA,QAAhB;SACKyuB,IAAL,GAAYA,IAAZ;SACKxuB,EAAL,GAAUA,EAAV;SACKuvB,MAAL,GAAc,KAAKf,IAAL,CAAUgB,YAAV,EAAd;SACKC,OAAL,GAAe,CAAC,CAAC,CAAD,CAAD,CAAf;SACKC,MAAL,GAAc,CAAC,KAAKlB,IAAL,CAAUmB,QAAV,CAAmB,CAAnB,EAAsBV,YAAvB,CAAd;SAEKxX,IAAL,GAAY,KAAK+W,IAAL,CAAUoB,cAAtB;SACKxE,KAAL,GAAa,OAAO,KAAKoD,IAAL,CAAUqB,UAA9B;SACKtD,QAAL,GAAgB,KAAKiC,IAAL,CAAUsB,MAAV,GAAmB,KAAK1E,KAAxC;SACKoB,SAAL,GAAiB,KAAKgC,IAAL,CAAUuB,OAAV,GAAoB,KAAK3E,KAA1C;SACKqB,OAAL,GAAe,KAAK+B,IAAL,CAAU/B,OAAV,GAAoB,KAAKrB,KAAxC;SACKsB,SAAL,GAAiB,KAAK8B,IAAL,CAAU9B,SAAV,GAAsB,KAAKtB,KAA5C;SACKuB,OAAL,GAAe,KAAK6B,IAAL,CAAU7B,OAAV,GAAoB,KAAKvB,KAAxC;SACKkB,IAAL,GAAY,KAAKkC,IAAL,CAAUlC,IAAtB;;QAEIvsB,QAAQ,CAACjE,OAAT,CAAiBk0B,eAAjB,KAAqC,KAAzC,EAAgD;WACzCC,WAAL,GAAmB3zB,MAAM,CAAC6O,MAAP,CAAc,IAAd,CAAnB;;;;EAIJ+kB,SAAS,CAAClD,IAAD,EAAOmD,QAAP,EAAiB;UAClBC,GAAG,GAAG,KAAK5B,IAAL,CAAU5pB,MAAV,CAAiBooB,IAAjB,EAAuBmD,QAAvB,CAAZ,CADwB;;SAInB,IAAIpyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqyB,GAAG,CAACxB,SAAJ,CAAc/xB,MAAlC,EAA0CkB,CAAC,EAA3C,EAA+C;YACvCsyB,QAAQ,GAAGD,GAAG,CAACxB,SAAJ,CAAc7wB,CAAd,CAAjB;;WACK,IAAI7B,GAAT,IAAgBm0B,QAAhB,EAA0B;QACxBA,QAAQ,CAACn0B,GAAD,CAAR,IAAiB,KAAKkvB,KAAtB;;;MAGFiF,QAAQ,CAACpB,YAAT,GAAwBmB,GAAG,CAAChD,MAAJ,CAAWrvB,CAAX,EAAckxB,YAAd,GAA6B,KAAK7D,KAA1D;;;WAGKgF,GAAP;;;EAGFE,YAAY,CAACtD,IAAD,EAAO;QACb,CAAC,KAAKiD,WAAV,EAAuB;aACd,KAAKC,SAAL,CAAelD,IAAf,CAAP;;;QAEEuD,MAAJ;;QACKA,MAAM,GAAG,KAAKN,WAAL,CAAiBjD,IAAjB,CAAd,EAAuC;aAC9BuD,MAAP;;;UAGIH,GAAG,GAAG,KAAKF,SAAL,CAAelD,IAAf,CAAZ;SACKiD,WAAL,CAAiBjD,IAAjB,IAAyBoD,GAAzB;WACOA,GAAP;;;EAGFxrB,MAAM,CAACooB,IAAD,EAAOmD,QAAP,EAAiBK,SAAjB,EAA4B;;QAE5BL,QAAJ,EAAc;aACL,KAAKD,SAAL,CAAelD,IAAf,EAAqBmD,QAArB,CAAP;;;QAGE/C,MAAM,GAAGoD,SAAS,GAAG,IAAH,GAAU,EAAhC;QACI5B,SAAS,GAAG4B,SAAS,GAAG,IAAH,GAAU,EAAnC;QACIvB,YAAY,GAAG,CAAnB,CARgC;;;QAY5BlyB,IAAI,GAAG,CAAX;QACI8T,KAAK,GAAG,CAAZ;;WACOA,KAAK,IAAImc,IAAI,CAACnwB,MAArB,EAA6B;UACvB4zB,MAAJ;;UAEG5f,KAAK,KAAKmc,IAAI,CAACnwB,MAAf,IAAyBE,IAAI,GAAG8T,KAAjC,KACE4f,MAAM,GAAGzD,IAAI,CAACvW,MAAL,CAAY5F,KAAZ,CAAV,EAA+B,CAAC,GAAD,EAAM,IAAN,EAAYgS,QAAZ,CAAqB4N,MAArB,CADhC,CADF,EAGE;cACML,GAAG,GAAG,KAAKE,YAAL,CAAkBtD,IAAI,CAACvvB,KAAL,CAAWV,IAAX,EAAiB,EAAE8T,KAAnB,CAAlB,CAAZ;;YACI,CAAC2f,SAAL,EAAgB;UACdpD,MAAM,GAAGA,MAAM,CAACrsB,MAAP,CAAcqvB,GAAG,CAAChD,MAAlB,CAAT;UACAwB,SAAS,GAAGA,SAAS,CAAC7tB,MAAV,CAAiBqvB,GAAG,CAACxB,SAArB,CAAZ;;;QAGFK,YAAY,IAAImB,GAAG,CAACnB,YAApB;QACAlyB,IAAI,GAAG8T,KAAP;OAXF,MAYO;QACLA,KAAK;;;;WAIF;MAAEuc,MAAF;MAAUwB,SAAV;MAAqBK;KAA5B;;;EAGFpc,MAAM,CAACma,IAAD,EAAOmD,QAAP,EAAiB;UACf;MAAE/C,MAAF;MAAUwB;QAAc,KAAKhqB,MAAL,CAAYooB,IAAZ,EAAkBmD,QAAlB,CAA9B;UAEMlD,GAAG,GAAG,EAAZ;;SACK,IAAIlvB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqvB,MAAM,CAACvwB,MAA3B,EAAmCkB,CAAC,EAApC,EAAwC;YAChCyvB,KAAK,GAAGJ,MAAM,CAACrvB,CAAD,CAApB;YACM2yB,GAAG,GAAG,KAAKnB,MAAL,CAAYoB,YAAZ,CAAyBnD,KAAK,CAACxtB,EAA/B,CAAZ;MACAitB,GAAG,CAACjwB,IAAJ,CAAU,OAAM0zB,GAAG,CAACh1B,QAAJ,CAAa,EAAb,CAAiB,EAAxB,CAA0B+B,KAA1B,CAAgC,CAAC,CAAjC,CAAT;;UAEI,KAAKiyB,MAAL,CAAYgB,GAAZ,KAAoB,IAAxB,EAA8B;aACvBhB,MAAL,CAAYgB,GAAZ,IAAmBlD,KAAK,CAACyB,YAAN,GAAqB,KAAK7D,KAA7C;;;UAEE,KAAKqE,OAAL,CAAaiB,GAAb,KAAqB,IAAzB,EAA+B;aACxBjB,OAAL,CAAaiB,GAAb,IAAoBlD,KAAK,CAACoD,UAA1B;;;;WAIG,CAAC3D,GAAD,EAAM2B,SAAN,CAAP;;;EAGFf,aAAa,CAACzvB,MAAD,EAASuG,IAAT,EAAewrB,QAAf,EAAyB;UAC9BlrB,KAAK,GAAG,KAAKL,MAAL,CAAYxG,MAAZ,EAAoB+xB,QAApB,EAA8B,IAA9B,EAAoClB,YAAlD;UACM7D,KAAK,GAAGzmB,IAAI,GAAG,IAArB;WACOM,KAAK,GAAGmmB,KAAf;;;EAGF7Y,KAAK,GAAG;UACAse,KAAK,GAAG,KAAKtB,MAAL,CAAYuB,GAAZ,IAAmB,IAAjC;UACMC,QAAQ,GAAG,KAAKhxB,QAAL,CAAcqF,GAAd,EAAjB;;QAEIyrB,KAAJ,EAAW;MACTE,QAAQ,CAAC9wB,IAAT,CAAc8T,OAAd,GAAwB,eAAxB;;;SAGGwb,MAAL,CACGyB,YADH,GAEGC,EAFH,CAEM,MAFN,EAEchxB,IAAI,IAAI8wB,QAAQ,CAACxwB,KAAT,CAAeN,IAAf,CAFtB,EAGGgxB,EAHH,CAGM,KAHN,EAGa,MAAMF,QAAQ,CAAC/yB,GAAT,EAHnB;UAKMkzB,WAAW,GACf,CAAC,CAAC,KAAK1C,IAAL,CAAU,MAAV,KAAqB,IAArB,GACE,KAAKA,IAAL,CAAU,MAAV,EAAkB2C,YADpB,GAEEC,SAFH,KAEiB,CAFlB,KAEwB,CAH1B;QAIIC,KAAK,GAAG,CAAZ;;QACI,KAAK7C,IAAL,CAAU8C,IAAV,CAAeC,YAAnB,EAAiC;MAC/BF,KAAK,IAAI,KAAK,CAAd;;;QAEE,KAAKH,WAAL,IAAoBA,WAAW,IAAI,CAAvC,EAA0C;MACxCG,KAAK,IAAI,KAAK,CAAd;;;IAEFA,KAAK,IAAI,KAAK,CAAd,CAxBM;;QAyBFH,WAAW,KAAK,EAApB,EAAwB;MACtBG,KAAK,IAAI,KAAK,CAAd;;;QAEE,KAAK7C,IAAL,CAAUgD,IAAV,CAAeC,QAAf,CAAwBC,MAA5B,EAAoC;MAClCL,KAAK,IAAI,KAAK,CAAd;KA7BI;;;UAiCAM,GAAG,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EACTpyB,GADS,CACLxB,CAAC,IAAII,MAAM,CAACyzB,YAAP,CAAoB,CAAC,KAAK5xB,EAAL,CAAQ1B,UAAR,CAAmBP,CAAnB,KAAyB,EAA1B,IAAgC,EAApD,CADA,EAETV,IAFS,CAEJ,EAFI,CAAZ;UAGMoa,IAAI,GAAGka,GAAG,GAAG,GAAN,GAAY,KAAKnD,IAAL,CAAUoB,cAAnC;UAEM;MAAEtD;QAAS,KAAKkC,IAAtB;UACMqD,UAAU,GAAG,KAAK9xB,QAAL,CAAcqF,GAAd,CAAkB;MACnCI,IAAI,EAAE,gBAD6B;MAEnCssB,QAAQ,EAAEra,IAFyB;MAGnCsa,KAAK,EAAEV,KAH4B;MAInCW,QAAQ,EAAE,CACR1F,IAAI,CAAC2F,IAAL,GAAY,KAAK7G,KADT,EAERkB,IAAI,CAAC4F,IAAL,GAAY,KAAK9G,KAFT,EAGRkB,IAAI,CAAC6F,IAAL,GAAY,KAAK/G,KAHT,EAIRkB,IAAI,CAACzlB,IAAL,GAAY,KAAKukB,KAJT,CAJyB;MAUnCgH,WAAW,EAAE,KAAK5D,IAAL,CAAU6D,WAVY;MAWnCC,MAAM,EAAE,KAAK/F,QAXsB;MAYnCgG,OAAO,EAAE,KAAK/F,SAZqB;MAanCgG,SAAS,EAAE,CAAC,KAAKhE,IAAL,CAAU9B,SAAV,IAAuB,KAAK8B,IAAL,CAAUsB,MAAlC,IAA4C,KAAK1E,KAbzB;MAcnCqH,OAAO,EAAE,CAAC,KAAKjE,IAAL,CAAU/B,OAAV,IAAqB,CAAtB,IAA2B,KAAKrB,KAdN;MAenCsH,KAAK,EAAE;KAfU,CAAnB,CAvCM;;QAyDF7B,KAAJ,EAAW;MACTgB,UAAU,CAAC5xB,IAAX,CAAgB0yB,SAAhB,GAA4B5B,QAA5B;KADF,MAEO;MACLc,UAAU,CAAC5xB,IAAX,CAAgB2yB,SAAhB,GAA4B7B,QAA5B;;;IAGFc,UAAU,CAAC7zB,GAAX;UAEM60B,kBAAkB,GAAG;MACzBrtB,IAAI,EAAE,MADmB;MAEzBuO,OAAO,EAAE,cAFgB;MAGzB0a,QAAQ,EAAEhX,IAHe;MAIzBqb,aAAa,EAAE;QACbC,QAAQ,EAAE,IAAI50B,MAAJ,CAAW,OAAX,CADG;QAEb60B,QAAQ,EAAE,IAAI70B,MAAJ,CAAW,UAAX,CAFG;QAGb80B,UAAU,EAAE;OAPW;MASzBC,cAAc,EAAErB,UATS;MAUzBsB,CAAC,EAAE,CAAC,CAAD,EAAI,KAAKzD,MAAT;KAVL;;QAaI,CAACmB,KAAL,EAAY;MACVgC,kBAAkB,CAAC9e,OAAnB,GAA6B,cAA7B;MACA8e,kBAAkB,CAACO,WAAnB,GAAiC,UAAjC;;;UAGIC,cAAc,GAAG,KAAKtzB,QAAL,CAAcqF,GAAd,CAAkBytB,kBAAlB,CAAvB;IAEAQ,cAAc,CAACr1B,GAAf;SAEKuH,UAAL,CAAgBtF,IAAhB,GAAuB;MACrBuF,IAAI,EAAE,MADe;MAErBuO,OAAO,EAAE,OAFY;MAGrB0a,QAAQ,EAAEhX,IAHW;MAIrBiX,QAAQ,EAAE,YAJW;MAKrB4E,eAAe,EAAE,CAACD,cAAD,CALI;MAMrBE,SAAS,EAAE,KAAKC,aAAL;KANb;WASO,KAAKjuB,UAAL,CAAgBvH,GAAhB,EAAP;GApN+B;;;;;EA0NjCw1B,aAAa,GAAG;UACRC,IAAI,GAAG,KAAK1zB,QAAL,CAAcqF,GAAd,EAAb;UAEMsuB,OAAO,GAAG,EAAhB;;SACK,IAAI9C,UAAT,IAAuB,KAAKnB,OAA5B,EAAqC;YAC7Bd,OAAO,GAAG,EAAhB,CADmC;;WAI9B,IAAIznB,KAAT,IAAkB0pB,UAAlB,EAA8B;YACxB1pB,KAAK,GAAG,MAAZ,EAAoB;UAClBA,KAAK,IAAI,OAAT;UACAynB,OAAO,CAAC3xB,IAAR,CAAaoyB,KAAK,CAAGloB,KAAK,KAAK,EAAX,GAAiB,KAAlB,GAA2B,MAA5B,CAAlB;UACAA,KAAK,GAAG,SAAUA,KAAK,GAAG,KAA1B;;;QAGFynB,OAAO,CAAC3xB,IAAR,CAAaoyB,KAAK,CAACloB,KAAD,CAAlB;;;MAGFwsB,OAAO,CAAC12B,IAAR,CAAc,IAAG2xB,OAAO,CAACtxB,IAAR,CAAa,GAAb,CAAkB,GAAnC;;;IAGFo2B,IAAI,CAACz1B,GAAL,CAAU;;;;;;;;;;;;;;;UAeJoxB,KAAK,CAACsE,OAAO,CAAC72B,MAAR,GAAiB,CAAlB,CAAqB,MAAK62B,OAAO,CAACr2B,IAAR,CAAa,GAAb,CAAkB;;;;;;CAfvD;WAuBOo2B,IAAP;;;;;ACvQJ,MAAME,cAAN,CAAqB;SACZhI,IAAP,CAAY5rB,QAAZ,EAAsB6zB,GAAtB,EAA2BC,MAA3B,EAAmC7zB,EAAnC,EAAuC;QACjCwuB,IAAJ;;QACI,OAAOoF,GAAP,KAAe,QAAnB,EAA6B;UACvBrF,YAAY,CAACY,cAAb,CAA4ByE,GAA5B,CAAJ,EAAsC;eAC7B,IAAIrF,YAAJ,CAAiBxuB,QAAjB,EAA2B6zB,GAA3B,EAAgC5zB,EAAhC,CAAP;;;MAGF4zB,GAAG,GAAG/H,EAAE,CAACC,YAAH,CAAgB8H,GAAhB,CAAN;;;QAEEp1B,MAAM,CAACK,QAAP,CAAgB+0B,GAAhB,CAAJ,EAA0B;MACxBpF,IAAI,GAAGsF,OAAO,CAAC3oB,MAAR,CAAeyoB,GAAf,EAAoBC,MAApB,CAAP;KADF,MAEO,IAAID,GAAG,YAAYG,UAAnB,EAA+B;MACpCvF,IAAI,GAAGsF,OAAO,CAAC3oB,MAAR,CAAe3M,MAAM,CAACC,IAAP,CAAYm1B,GAAZ,CAAf,EAAiCC,MAAjC,CAAP;KADK,MAEA,IAAID,GAAG,YAAYI,WAAnB,EAAgC;MACrCxF,IAAI,GAAGsF,OAAO,CAAC3oB,MAAR,CAAe3M,MAAM,CAACC,IAAP,CAAY,IAAIs1B,UAAJ,CAAeH,GAAf,CAAZ,CAAf,EAAiDC,MAAjD,CAAP;;;QAGErF,IAAI,IAAI,IAAZ,EAAkB;YACV,IAAI7yB,KAAJ,CAAU,mDAAV,CAAN;;;WAGK,IAAI2zB,YAAJ,CAAiBvvB,QAAjB,EAA2ByuB,IAA3B,EAAiCxuB,EAAjC,CAAP;;;;;ACzBJ,iBAAe;EACbi0B,SAAS,CAACC,WAAW,GAAG,WAAf,EAA4B;;SAE9BC,aAAL,GAAqB,EAArB;SACKC,UAAL,GAAkB,CAAlB,CAHmC;;SAM9BC,SAAL,GAAiB,EAAjB;SACKC,KAAL,GAAa,IAAb;SAEKC,gBAAL,GAAwB,EAAxB,CATmC;;QAY/BL,WAAJ,EAAiB;WACV1F,IAAL,CAAU0F,WAAV;;GAdS;;EAkBb1F,IAAI,CAACoF,GAAD,EAAMC,MAAN,EAAclvB,IAAd,EAAoB;QAClB6vB,QAAJ,EAAchG,IAAd;;QACI,OAAOqF,MAAP,KAAkB,QAAtB,EAAgC;MAC9BlvB,IAAI,GAAGkvB,MAAP;MACAA,MAAM,GAAG,IAAT;KAJoB;;;QAQlB,OAAOD,GAAP,KAAe,QAAf,IAA2B,KAAKW,gBAAL,CAAsBX,GAAtB,CAA/B,EAA2D;MACzDY,QAAQ,GAAGZ,GAAX;OACC;QAAEA,GAAF;QAAOC;UAAW,KAAKU,gBAAL,CAAsBX,GAAtB,CAAnB;KAFF,MAGO;MACLY,QAAQ,GAAGX,MAAM,IAAID,GAArB;;UACI,OAAOY,QAAP,KAAoB,QAAxB,EAAkC;QAChCA,QAAQ,GAAG,IAAX;;;;QAIA7vB,IAAI,IAAI,IAAZ,EAAkB;WACX8vB,QAAL,CAAc9vB,IAAd;KAnBoB;;;QAuBjB6pB,IAAI,GAAG,KAAK2F,aAAL,CAAmBK,QAAnB,CAAZ,EAA2C;WACpCF,KAAL,GAAa9F,IAAb;aACO,IAAP;KAzBoB;;;UA6BhBxuB,EAAE,GAAI,IAAG,EAAE,KAAKo0B,UAAW,EAAjC;SACKE,KAAL,GAAaX,cAAc,CAAChI,IAAf,CAAoB,IAApB,EAA0BiI,GAA1B,EAA+BC,MAA/B,EAAuC7zB,EAAvC,CAAb,CA9BsB;;;QAkCjBwuB,IAAI,GAAG,KAAK2F,aAAL,CAAmB,KAAKG,KAAL,CAAW7c,IAA9B,CAAZ,EAAkD;WAC3C6c,KAAL,GAAa9F,IAAb;aACO,IAAP;KApCoB;;;QAwClBgG,QAAJ,EAAc;WACPL,aAAL,CAAmBK,QAAnB,IAA+B,KAAKF,KAApC;;;QAGE,KAAKA,KAAL,CAAW7c,IAAf,EAAqB;WACd0c,aAAL,CAAmB,KAAKG,KAAL,CAAW7c,IAA9B,IAAsC,KAAK6c,KAA3C;;;WAGK,IAAP;GAlEW;;EAqEbG,QAAQ,CAACJ,SAAD,EAAY;SACbA,SAAL,GAAiBA,SAAjB;WACO,IAAP;GAvEW;;EA0EbK,iBAAiB,CAAC3G,UAAD,EAAa;QACxBA,UAAU,IAAI,IAAlB,EAAwB;MACtBA,UAAU,GAAG,KAAb;;;WAEK,KAAKuG,KAAL,CAAWxG,UAAX,CAAsB,KAAKuG,SAA3B,EAAsCtG,UAAtC,CAAP;GA9EW;;EAiFb4G,YAAY,CAACld,IAAD,EAAOmc,GAAP,EAAYC,MAAZ,EAAoB;SACzBU,gBAAL,CAAsB9c,IAAtB,IAA8B;MAC5Bmc,GAD4B;MAE5BC;KAFF;WAKO,IAAP;;;CAvFJ;;ACCA,MAAMe,WAAN,SAA0BC,mBAA1B,CAAuC;EACrCh5B,WAAW,CAACkE,QAAD,EAAWjE,OAAX,EAAoB;;SAExBiE,QAAL,GAAgBA,QAAhB;SACK+0B,MAAL,GAAch5B,OAAO,CAACg5B,MAAR,IAAkB,CAAhC;SACKC,gBAAL,GAAwBj5B,OAAO,CAACi5B,gBAAR,IAA4B,CAApD;SACKC,WAAL,GAAmBl5B,OAAO,CAACk5B,WAAR,KAAwB,CAA3C;SACKC,OAAL,GAAen5B,OAAO,CAACm5B,OAAR,IAAmB,CAAlC;SACKC,SAAL,GAAiBp5B,OAAO,CAACo5B,SAAR,IAAqB,IAArB,GAA4Bp5B,OAAO,CAACo5B,SAApC,GAAgD,EAAjE,CAP6B;;SAQxBrO,SAAL,GACE,CAAC/qB,OAAO,CAACmJ,KAAR,GAAgB,KAAKiwB,SAAL,IAAkB,KAAKD,OAAL,GAAe,CAAjC,CAAjB,IAAwD,KAAKA,OAD/D;SAEKE,SAAL,GAAiB,KAAKtO,SAAtB;SACKuO,MAAL,GAAc,KAAKr1B,QAAL,CAAc6I,CAA5B;SACKysB,MAAL,GAAc,KAAKt1B,QAAL,CAAcujB,CAA5B;SACKgS,MAAL,GAAc,CAAd;SACKC,QAAL,GAAgBz5B,OAAO,CAACy5B,QAAxB;SACKC,UAAL,GAAkB,CAAlB;SACKrF,QAAL,GAAgBr0B,OAAO,CAACq0B,QAAxB,CAhB6B;;QAmBzBr0B,OAAO,CAACoJ,MAAR,IAAkB,IAAtB,EAA4B;WACrBA,MAAL,GAAcpJ,OAAO,CAACoJ,MAAtB;WACK2B,IAAL,GAAY,KAAKwuB,MAAL,GAAcv5B,OAAO,CAACoJ,MAAlC;KAFF,MAGO;WACA2B,IAAL,GAAY,KAAK9G,QAAL,CAAc8T,IAAd,CAAmBhN,IAAnB,EAAZ;KAvB2B;;;SA2BxBoqB,EAAL,CAAQ,WAAR,EAAqBn1B,OAAO,IAAI;;;;YAIxBg5B,MAAM,GAAG,KAAKU,UAAL,IAAmB,KAAKV,MAAvC;WACK/0B,QAAL,CAAc6I,CAAd,IAAmBksB,MAAnB;WACKjO,SAAL,IAAkBiO,MAAlB;aAEO,KAAKW,IAAL,CAAU,MAAV,EAAkB,MAAM;aACxB11B,QAAL,CAAc6I,CAAd,IAAmBksB,MAAnB;aACKjO,SAAL,IAAkBiO,MAAlB;;YACIh5B,OAAO,CAAC45B,SAAR,IAAqB,CAAC,KAAKF,UAA/B,EAA2C;eACpCA,UAAL,GAAkB,KAAKV,MAAvB;;;YAEE,CAACh5B,OAAO,CAAC45B,SAAb,EAAwB;iBACd,KAAKF,UAAL,GAAkB,CAA1B;;OAPG,CAAP;KARF,EA3B6B;;SAgDxBvE,EAAL,CAAQ,UAAR,EAAoBn1B,OAAO,IAAI;YACvB;QAAE65B;UAAU75B,OAAlB;;UACI65B,KAAK,KAAK,SAAd,EAAyB;QACvB75B,OAAO,CAAC65B,KAAR,GAAgB,MAAhB;;;WAEGC,QAAL,GAAgB,IAAhB;aAEO,KAAKH,IAAL,CAAU,MAAV,EAAkB,MAAM;aACxB11B,QAAL,CAAcujB,CAAd,IAAmBxnB,OAAO,CAAC+5B,YAAR,IAAwB,CAA3C;QACA/5B,OAAO,CAAC65B,KAAR,GAAgBA,KAAhB;eACQ,KAAKC,QAAL,GAAgB,KAAxB;OAHK,CAAP;KAPF;;;EAeFE,SAAS,CAACC,IAAD,EAAO;WAEZ,KAAKh2B,QAAL,CAAc8tB,aAAd,CAA4BkI,IAA5B,EAAkC,IAAlC,IACA,KAAKhB,gBADL,GAEA,KAAKC,WAHP;;;EAOFgB,QAAQ,CAAChJ,IAAD,EAAOva,EAAP,EAAW;;QAEbwjB,EAAJ;UACMC,OAAO,GAAG,IAAIC,WAAJ,CAAgBnJ,IAAhB,CAAhB;QACIjwB,IAAI,GAAG,IAAX;UACMq5B,UAAU,GAAG95B,MAAM,CAAC6O,MAAP,CAAc,IAAd,CAAnB;;WAEQ8qB,EAAE,GAAGC,OAAO,CAACG,SAAR,EAAb,EAAmC;UAC7BC,cAAJ;UACIP,IAAI,GAAG/I,IAAI,CAACvvB,KAAL,CACT,CAACV,IAAI,IAAI,IAAR,GAAeA,IAAI,CAACszB,QAApB,GAA+Be,SAAhC,KAA8C,CADrC,EAET6E,EAAE,CAAC5F,QAFM,CAAX;UAIIvJ,CAAC,GACHsP,UAAU,CAACL,IAAD,CAAV,IAAoB,IAApB,GACIK,UAAU,CAACL,IAAD,CADd,GAEKK,UAAU,CAACL,IAAD,CAAV,GAAmB,KAAKD,SAAL,CAAeC,IAAf,CAH1B,CANiC;;;UAa7BjP,CAAC,GAAG,KAAKD,SAAL,GAAiB,KAAK2O,UAA9B,EAA0C;;YAEpCe,GAAG,GAAGx5B,IAAV;cACMy5B,GAAG,GAAG,EAAZ;;eAEOT,IAAI,CAACl5B,MAAZ,EAAoB;;cAEdiB,CAAJ,EAAO24B,SAAP;;cACI3P,CAAC,GAAG,KAAKqO,SAAb,EAAwB;;;YAGtBr3B,CAAC,GAAG8B,IAAI,CAACuQ,IAAL,CAAU,KAAKglB,SAAL,IAAkBrO,CAAC,GAAGiP,IAAI,CAACl5B,MAA3B,CAAV,CAAJ;YACAiqB,CAAC,GAAG,KAAKgP,SAAL,CAAeC,IAAI,CAACt4B,KAAL,CAAW,CAAX,EAAcK,CAAd,CAAf,CAAJ;YACA24B,SAAS,GAAG3P,CAAC,IAAI,KAAKqO,SAAV,IAAuBr3B,CAAC,GAAGi4B,IAAI,CAACl5B,MAA5C;WALF,MAMO;YACLiB,CAAC,GAAGi4B,IAAI,CAACl5B,MAAT;;;cAEE65B,UAAU,GAAG5P,CAAC,GAAG,KAAKqO,SAAT,IAAsBr3B,CAAC,GAAG,CAA3C,CAZkB;;iBAcX44B,UAAU,IAAID,SAArB,EAAgC;gBAC1BC,UAAJ,EAAgB;cACd5P,CAAC,GAAG,KAAKgP,SAAL,CAAeC,IAAI,CAACt4B,KAAL,CAAW,CAAX,EAAc,EAAEK,CAAhB,CAAf,CAAJ;cACA44B,UAAU,GAAG5P,CAAC,GAAG,KAAKqO,SAAT,IAAsBr3B,CAAC,GAAG,CAAvC;aAFF,MAGO;cACLgpB,CAAC,GAAG,KAAKgP,SAAL,CAAeC,IAAI,CAACt4B,KAAL,CAAW,CAAX,EAAc,EAAEK,CAAhB,CAAf,CAAJ;cACA44B,UAAU,GAAG5P,CAAC,GAAG,KAAKqO,SAAT,IAAsBr3B,CAAC,GAAG,CAAvC;cACA24B,SAAS,GAAG3P,CAAC,IAAI,KAAKqO,SAAV,IAAuBr3B,CAAC,GAAGi4B,IAAI,CAACl5B,MAA5C;;WArBc;;;cA0BdiB,CAAC,KAAK,CAAN,IAAW,KAAKq3B,SAAL,KAAmB,KAAKtO,SAAvC,EAAkD;YAChD/oB,CAAC,GAAG,CAAJ;WA3BgB;;;UA+BlB04B,GAAG,CAACG,QAAJ,GAAeV,EAAE,CAACU,QAAH,IAAe74B,CAAC,GAAGi4B,IAAI,CAACl5B,MAAvC;UACAy5B,cAAc,GAAG7jB,EAAE,CAACsjB,IAAI,CAACt4B,KAAL,CAAW,CAAX,EAAcK,CAAd,CAAD,EAAmBgpB,CAAnB,EAAsB0P,GAAtB,EAA2BD,GAA3B,CAAnB;UACAA,GAAG,GAAG;YAAEI,QAAQ,EAAE;WAAlB,CAjCkB;;UAoClBZ,IAAI,GAAGA,IAAI,CAACt4B,KAAL,CAAWK,CAAX,CAAP;UACAgpB,CAAC,GAAG,KAAKgP,SAAL,CAAeC,IAAf,CAAJ;;cAEIO,cAAc,KAAK,KAAvB,EAA8B;;;;OA5ClC,MAgDO;;QAELA,cAAc,GAAG7jB,EAAE,CAACsjB,IAAD,EAAOjP,CAAP,EAAUmP,EAAV,EAAcl5B,IAAd,CAAnB;;;UAGEu5B,cAAc,KAAK,KAAvB,EAA8B;;;;MAG9Bv5B,IAAI,GAAGk5B,EAAP;;;;EAIJW,IAAI,CAAC5J,IAAD,EAAOlxB,OAAP,EAAgB;;QAEdA,OAAO,CAACg5B,MAAR,IAAkB,IAAtB,EAA4B;WACrBA,MAAL,GAAch5B,OAAO,CAACg5B,MAAtB;;;QAEEh5B,OAAO,CAACi5B,gBAAR,IAA4B,IAAhC,EAAsC;WAC/BA,gBAAL,GAAwBj5B,OAAO,CAACi5B,gBAAhC;;;QAEEj5B,OAAO,CAACk5B,WAAR,IAAuB,IAA3B,EAAiC;WAC1BA,WAAL,GAAmBl5B,OAAO,CAACk5B,WAA3B;;;QAEEl5B,OAAO,CAACy5B,QAAR,IAAoB,IAAxB,EAA8B;WACvBA,QAAL,GAAgBz5B,OAAO,CAACy5B,QAAxB;KAZgB;;;;;UAkBZsB,KAAK,GAAG,KAAK92B,QAAL,CAAcujB,CAAd,GAAkB,KAAKvjB,QAAL,CAAc20B,iBAAd,CAAgC,IAAhC,CAAhC;;QACI,KAAK30B,QAAL,CAAcujB,CAAd,GAAkB,KAAKzc,IAAvB,IAA+BgwB,KAAK,GAAG,KAAKhwB,IAAhD,EAAsD;WAC/CiwB,WAAL;;;QAGEx2B,MAAM,GAAG,EAAb;QACIy2B,SAAS,GAAG,CAAhB;QACIC,EAAE,GAAG,CAAT;QACIC,EAAE,GAAG,CAAT;QAEI;MAAE3T;QAAM,KAAKvjB,QAAjB,CA5BkB;;UA6BZm3B,QAAQ,GAAG,MAAM;MACrBp7B,OAAO,CAACi7B,SAAR,GAAoBA,SAAS,GAAG,KAAK/B,WAAL,IAAoBgC,EAAE,GAAG,CAAzB,CAAhC;MACAl7B,OAAO,CAACq7B,SAAR,GAAoBH,EAApB;MACAl7B,OAAO,CAAC+qB,SAAR,GAAoB,KAAKA,SAAzB;OACC;QAAEvD;UAAM,KAAKvjB,QAAd;WACKq3B,IAAL,CAAU,MAAV,EAAkB92B,MAAlB,EAA0BxE,OAA1B,EAAmC,IAAnC;aACOm7B,EAAE,EAAT;KANF;;SASKG,IAAL,CAAU,cAAV,EAA0Bt7B,OAA1B,EAAmC,IAAnC;SAEKk6B,QAAL,CAAchJ,IAAd,EAAoB,CAAC+I,IAAD,EAAOjP,CAAP,EAAUmP,EAAV,EAAcl5B,IAAd,KAAuB;UACrCA,IAAI,IAAI,IAAR,IAAgBA,IAAI,CAAC45B,QAAzB,EAAmC;aAC5BS,IAAL,CAAU,WAAV,EAAuBt7B,OAAvB,EAAgC,IAAhC;aACKq5B,SAAL,GAAiB,KAAKtO,SAAtB;;;UAGEC,CAAC,IAAI,KAAKqO,SAAd,EAAyB;QACvB70B,MAAM,IAAIy1B,IAAV;QACAgB,SAAS,IAAIjQ,CAAb;QACAkQ,EAAE;;;UAGAf,EAAE,CAACU,QAAH,IAAe7P,CAAC,GAAG,KAAKqO,SAA5B,EAAuC;;;cAG/BkC,EAAE,GAAG,KAAKt3B,QAAL,CAAc20B,iBAAd,CAAgC,IAAhC,CAAX;;YAEE,KAAKxvB,MAAL,IAAe,IAAf,IACA,KAAKqwB,QADL,IAEA,KAAKx1B,QAAL,CAAcujB,CAAd,GAAkB+T,EAAE,GAAG,CAAvB,GAA2B,KAAKxwB,IAFhC,IAGA,KAAKyuB,MAAL,IAAe,KAAKL,OAJtB,EAKE;cACI,KAAKM,QAAL,KAAkB,IAAtB,EAA4B;iBACrBA,QAAL,GAAgB,GAAhB;WAFF;;;UAIAj1B,MAAM,GAAGA,MAAM,CAAC3B,OAAP,CAAe,MAAf,EAAuB,EAAvB,CAAT;UACAo4B,SAAS,GAAG,KAAKjB,SAAL,CAAex1B,MAAM,GAAG,KAAKi1B,QAA7B,CAAZ,CALA;;;iBASOj1B,MAAM,IAAIy2B,SAAS,GAAG,KAAKlQ,SAAlC,EAA6C;YAC3CvmB,MAAM,GAAGA,MAAM,CAAC7C,KAAP,CAAa,CAAb,EAAgB,CAAC,CAAjB,EAAoBkB,OAApB,CAA4B,MAA5B,EAAoC,EAApC,CAAT;YACAo4B,SAAS,GAAG,KAAKjB,SAAL,CAAex1B,MAAM,GAAG,KAAKi1B,QAA7B,CAAZ;WAXF;;;cAcIwB,SAAS,IAAI,KAAKlQ,SAAtB,EAAiC;YAC/BvmB,MAAM,GAAGA,MAAM,GAAG,KAAKi1B,QAAvB;;;UAGFwB,SAAS,GAAG,KAAKjB,SAAL,CAAex1B,MAAf,CAAZ;;;YAGE21B,EAAE,CAACU,QAAP,EAAiB;cACX7P,CAAC,GAAG,KAAKqO,SAAb,EAAwB;YACtB+B,QAAQ;YACR52B,MAAM,GAAGy1B,IAAT;YACAgB,SAAS,GAAGjQ,CAAZ;YACAkQ,EAAE,GAAG,CAAL;;;eAGGI,IAAL,CAAU,UAAV,EAAsBt7B,OAAtB,EAA+B,IAA/B;;;QAGFo7B,QAAQ,GAzC6B;;;YA6CjC,KAAKn3B,QAAL,CAAcujB,CAAd,GAAkB+T,EAAlB,GAAuB,KAAKxwB,IAAhC,EAAsC;gBAC9ByvB,cAAc,GAAG,KAAKQ,WAAL,EAAvB,CADoC;;cAIhC,CAACR,cAAL,EAAqB;YACnBU,EAAE,GAAG,CAAL;YACA12B,MAAM,GAAG,EAAT;mBACO,KAAP;;SApDiC;;;YAyDjC21B,EAAE,CAACU,QAAP,EAAiB;eACVxB,SAAL,GAAiB,KAAKtO,SAAtB;UACAvmB,MAAM,GAAG,EAAT;UACAy2B,SAAS,GAAG,CAAZ;iBACQC,EAAE,GAAG,CAAb;SAJF,MAKO;;eAEA7B,SAAL,GAAiB,KAAKtO,SAAL,GAAiBC,CAAlC;UACAxmB,MAAM,GAAGy1B,IAAT;UACAgB,SAAS,GAAGjQ,CAAZ;iBACQkQ,EAAE,GAAG,CAAb;;OAnEJ,MAqEO;eACG,KAAK7B,SAAL,IAAkBrO,CAA1B;;KAlFJ;;QAsFIkQ,EAAE,GAAG,CAAT,EAAY;WACLI,IAAL,CAAU,UAAV,EAAsBt7B,OAAtB,EAA+B,IAA/B;MACAo7B,QAAQ;;;SAGLE,IAAL,CAAU,YAAV,EAAwBt7B,OAAxB,EAAiC,IAAjC,EAnIkB;;;;QAwIdA,OAAO,CAAC45B,SAAR,KAAsB,IAA1B,EAAgC;UAC1BuB,EAAE,GAAG,CAAT,EAAY;aACLzB,UAAL,GAAkB,CAAlB;;;WAEGA,UAAL,IAAmB15B,OAAO,CAACi7B,SAAR,IAAqB,CAAxC;aACQ,KAAKh3B,QAAL,CAAcujB,CAAd,GAAkBA,CAA1B;KALF,MAMO;aACG,KAAKvjB,QAAL,CAAc6I,CAAd,GAAkB,KAAKwsB,MAA/B;;;;EAIJ0B,WAAW,CAACh7B,OAAD,EAAU;SACds7B,IAAL,CAAU,YAAV,EAAwBt7B,OAAxB,EAAiC,IAAjC;;QAEI,EAAE,KAAKw5B,MAAP,GAAgB,KAAKL,OAAzB,EAAkC;;;UAG5B,KAAK/vB,MAAL,IAAe,IAAnB,EAAyB;eAChB,KAAP;;;WAGGnF,QAAL,CAAcu3B,iBAAd;WACKhC,MAAL,GAAc,CAAd;WACKD,MAAL,GAAc,KAAKt1B,QAAL,CAAc8T,IAAd,CAAmB/O,OAAnB,CAA2BzD,GAAzC;WACKwF,IAAL,GAAY,KAAK9G,QAAL,CAAc8T,IAAd,CAAmBhN,IAAnB,EAAZ;WACK9G,QAAL,CAAc6I,CAAd,GAAkB,KAAKwsB,MAAvB;;UACI,KAAKr1B,QAAL,CAAcsX,UAAlB,EAA8B;aACvBtX,QAAL,CAAcmX,SAAd,CAAwB,GAAG,KAAKnX,QAAL,CAAcsX,UAAzC;;;WAEG+f,IAAL,CAAU,WAAV,EAAuBt7B,OAAvB,EAAgC,IAAhC;KAfF,MAgBO;WACAiE,QAAL,CAAc6I,CAAd,IAAmB,KAAKie,SAAL,GAAiB,KAAKqO,SAAzC;WACKn1B,QAAL,CAAcujB,CAAd,GAAkB,KAAK+R,MAAvB;WACK+B,IAAL,CAAU,aAAV,EAAyBt7B,OAAzB,EAAkC,IAAlC;;;SAGGs7B,IAAL,CAAU,cAAV,EAA0Bt7B,OAA1B,EAAmC,IAAnC;WACO,IAAP;;;;;ACrUJ,MAAM;UAAE4D;IAAWzC,SAAnB;AAEA,gBAAe;EACbs6B,QAAQ,GAAG;SACJC,KAAL,GAAa,KAAKA,KAAL,CAAWC,IAAX,CAAgB,IAAhB,CAAb,CADS;;SAGJ7uB,CAAL,GAAS,CAAT;SACK0a,CAAL,GAAS,CAAT;WACQ,KAAKoU,QAAL,GAAgB,CAAxB;GANW;;EASb/K,OAAO,CAAC+K,QAAD,EAAW;SACXA,QAAL,GAAgBA,QAAhB;WACO,IAAP;GAXW;;EAcbC,QAAQ,CAACC,KAAD,EAAQ;QACVA,KAAK,IAAI,IAAb,EAAmB;MACjBA,KAAK,GAAG,CAAR;;;SAEGtU,CAAL,IAAU,KAAKoR,iBAAL,CAAuB,IAAvB,IAA+BkD,KAA/B,GAAuC,KAAKF,QAAtD;WACO,IAAP;GAnBW;;EAsBbG,MAAM,CAACD,KAAD,EAAQ;QACRA,KAAK,IAAI,IAAb,EAAmB;MACjBA,KAAK,GAAG,CAAR;;;SAEGtU,CAAL,IAAU,KAAKoR,iBAAL,CAAuB,IAAvB,IAA+BkD,KAA/B,GAAuC,KAAKF,QAAtD;WACO,IAAP;GA3BW;;EA8BbI,KAAK,CAAC9K,IAAD,EAAOpkB,CAAP,EAAU0a,CAAV,EAAaxnB,OAAb,EAAsBi8B,YAAtB,EAAoC;IACvCj8B,OAAO,GAAG,KAAKk8B,YAAL,CAAkBpvB,CAAlB,EAAqB0a,CAArB,EAAwBxnB,OAAxB,CAAV,CADuC;;IAIvCkxB,IAAI,GAAGA,IAAI,IAAI,IAAR,GAAe,EAAf,GAAqB,GAAEA,IAAK,EAAnC,CAJuC;;QAOnClxB,OAAO,CAACk5B,WAAZ,EAAyB;MACvBhI,IAAI,GAAGA,IAAI,CAACruB,OAAL,CAAa,SAAb,EAAwB,GAAxB,CAAP;;;UAGIs5B,YAAY,GAAG,MAAM;UACrBn8B,OAAO,CAACo8B,YAAZ,EAA0B;QACxBp8B,OAAO,CAACo8B,YAAR,CAAqBj8B,GAArB,CAAyB,KAAKk8B,MAAL,CAAYr8B,OAAO,CAACs8B,UAAR,IAAsB,GAAlC,EACvB,CAAE,KAAKC,oBAAL,CAA0Bv8B,OAAO,CAACs8B,UAAR,IAAsB,GAAhD,CAAF,CADuB,CAAzB;;KAFJ,CAXuC;;;QAmBnCt8B,OAAO,CAACmJ,KAAZ,EAAmB;UACbqzB,OAAO,GAAG,KAAKC,QAAnB;;UACI,CAACD,OAAL,EAAc;QACZA,OAAO,GAAG,IAAI1D,WAAJ,CAAgB,IAAhB,EAAsB94B,OAAtB,CAAV;QACAw8B,OAAO,CAACrH,EAAR,CAAW,MAAX,EAAmB8G,YAAnB;QACAO,OAAO,CAACrH,EAAR,CAAW,WAAX,EAAwBgH,YAAxB;;;WAGGM,QAAL,GAAgBz8B,OAAO,CAAC45B,SAAR,GAAoB4C,OAApB,GAA8B,IAA9C;WACKE,YAAL,GAAoB18B,OAAO,CAAC45B,SAAR,GAAoB55B,OAApB,GAA8B,IAAlD;MACAw8B,OAAO,CAAC1B,IAAR,CAAa5J,IAAb,EAAmBlxB,OAAnB,EAViB;KAAnB,MAaO;WACA,IAAI+wB,IAAT,IAAiBG,IAAI,CAACvB,KAAL,CAAW,IAAX,CAAjB,EAAmC;QACjCwM,YAAY;QACZF,YAAY,CAAClL,IAAD,EAAO/wB,OAAP,CAAZ;;;;WAIG,IAAP;GArEW;;EAwEbkxB,IAAI,CAACA,IAAD,EAAOpkB,CAAP,EAAU0a,CAAV,EAAaxnB,OAAb,EAAsB;WACjB,KAAKg8B,KAAL,CAAW9K,IAAX,EAAiBpkB,CAAjB,EAAoB0a,CAApB,EAAuBxnB,OAAvB,EAAgC,KAAK07B,KAArC,CAAP;GAzEW;;EA4Eb3J,aAAa,CAACzvB,MAAD,EAAStC,OAAO,GAAG,EAAnB,EAAuB;WAEhC,KAAKw4B,KAAL,CAAWzG,aAAX,CAAyBzvB,MAAzB,EAAiC,KAAKi2B,SAAtC,EAAiDv4B,OAAO,CAACq0B,QAAzD,IACA,CAACr0B,OAAO,CAACi5B,gBAAR,IAA4B,CAA7B,KAAmC32B,MAAM,CAACvB,MAAP,GAAgB,CAAnD,CAFF;GA7EW;;EAmFb47B,cAAc,CAACzL,IAAD,EAAOlxB,OAAP,EAAgB;UACtB;MAAE8M,CAAF;MAAK0a;QAAM,IAAjB;IAEAxnB,OAAO,GAAG,KAAKk8B,YAAL,CAAkBl8B,OAAlB,CAAV;IACAA,OAAO,CAACoJ,MAAR,GAAiBwzB,QAAjB,CAJ4B;;UAMtB/L,OAAO,GAAG7wB,OAAO,CAAC6wB,OAAR,IAAmB,KAAK+K,QAAxB,IAAoC,CAApD;;SACKI,KAAL,CAAW9K,IAAX,EAAiB,KAAKpkB,CAAtB,EAAyB,KAAK0a,CAA9B,EAAiCxnB,OAAjC,EAA0C,MAAM;aACtC,KAAKwnB,CAAL,IAAU,KAAKoR,iBAAL,CAAuB,IAAvB,IAA+B/H,OAAjD;KADF;;UAIMznB,MAAM,GAAG,KAAKoe,CAAL,GAASA,CAAxB;SACK1a,CAAL,GAASA,CAAT;SACK0a,CAAL,GAASA,CAAT;WAEOpe,MAAP;GAlGW;;EAqGbyzB,IAAI,CAACA,IAAD,EAAO/vB,CAAP,EAAU0a,CAAV,EAAaxnB,OAAb,EAAsBw8B,OAAtB,EAA+B;IACjCx8B,OAAO,GAAG,KAAKk8B,YAAL,CAAkBpvB,CAAlB,EAAqB0a,CAArB,EAAwBxnB,OAAxB,CAAV;UAEM88B,QAAQ,GAAG98B,OAAO,CAAC88B,QAAR,IAAoB,QAArC;UACMC,IAAI,GAAGj5B,IAAI,CAACC,KAAL,CAAY,KAAKy0B,KAAL,CAAW/H,QAAX,GAAsB,IAAvB,GAA+B,KAAK8H,SAA/C,CAAb;UACMyE,OAAO,GAAGD,IAAI,GAAG,CAAvB;UACMhtB,CAAC,GAAG/P,OAAO,CAACi9B,YAAR,IAAwBF,IAAI,GAAG,CAAzC;UACM/D,MAAM,GACVh5B,OAAO,CAACk9B,UAAR,KAAuBJ,QAAQ,KAAK,QAAb,GAAwB/sB,CAAC,GAAG,CAA5B,GAAgCgtB,IAAI,GAAG,CAA9D,CADF;UAEMI,UAAU,GACdn9B,OAAO,CAACo9B,YAAR,KAAyBN,QAAQ,KAAK,QAAb,GAAwB/sB,CAAC,GAAG,CAA5B,GAAgCgtB,IAAI,GAAG,CAAhE,CADF;QAGIM,KAAK,GAAG,CAAZ;UACM75B,KAAK,GAAG,EAAd;UACM85B,MAAM,GAAG,EAAf;UACMC,OAAO,GAAG,EAAhB;;QAEIC,OAAO,GAAG,UAASX,IAAT,EAAe;UACvBh5B,CAAC,GAAG,CAAR;;WACK,IAAI5B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG46B,IAAI,CAAC97B,MAAzB,EAAiCkB,CAAC,EAAlC,EAAsC;cAC9Bw7B,IAAI,GAAGZ,IAAI,CAAC56B,CAAD,CAAjB;;YACIP,KAAK,CAAC6B,OAAN,CAAck6B,IAAd,CAAJ,EAAyB;UACvBJ,KAAK;UACLG,OAAO,CAACC,IAAD,CAAP;UACAJ,KAAK;SAHP,MAIO;UACL75B,KAAK,CAACtC,IAAN,CAAWu8B,IAAX;UACAH,MAAM,CAACp8B,IAAP,CAAYm8B,KAAZ;;cACIP,QAAQ,KAAK,QAAjB,EAA2B;YACzBS,OAAO,CAACr8B,IAAR,CAAa2C,CAAC,EAAd;;;;KAZR;;IAkBA25B,OAAO,CAACX,IAAD,CAAP;;UAEMa,KAAK,GAAG,UAAS75B,CAAT,EAAY;cAChBi5B,QAAR;aACO,UAAL;iBACU,GAAEj5B,CAAE,GAAZ;;aACG,UAAL;cACM85B,MAAM,GAAGt7B,MAAM,CAACyzB,YAAP,CAAqB,CAACjyB,CAAC,GAAG,CAAL,IAAU,EAAX,GAAiB,EAArC,CAAb;cACI+5B,KAAK,GAAG95B,IAAI,CAAC2H,KAAL,CAAW,CAAC5H,CAAC,GAAG,CAAL,IAAU,EAAV,GAAe,CAA1B,CAAZ;cACIqtB,IAAI,GAAGxvB,KAAK,CAACk8B,KAAK,GAAG,CAAT,CAAL,CAAiBr8B,IAAjB,CAAsBo8B,MAAtB,CAAX;iBACQ,GAAEzM,IAAK,GAAf;;KARN;;IAYAsL,OAAO,GAAG,IAAI1D,WAAJ,CAAgB,IAAhB,EAAsB94B,OAAtB,CAAV;IACAw8B,OAAO,CAACrH,EAAR,CAAW,MAAX,EAAmB,KAAKuG,KAAxB;IAEA2B,KAAK,GAAG,CAAR;QACIp7B,CAAC,GAAG,CAAR;IACAu6B,OAAO,CAACrH,EAAR,CAAW,WAAX,EAAwB,MAAM;UACxBsI,IAAJ,EAAUI,QAAV,EAAoBC,SAApB,EAA+BC,QAA/B;;UACI/9B,OAAO,CAACo8B,YAAZ,EAA0B;YACpBp8B,OAAO,CAACg+B,WAAZ,EAAyB;WACrBH,QAAF,EAAYC,SAAZ,EAAuBC,QAAvB,IAAoC/9B,OAAO,CAACg+B,WAA5C;SADF,MAEO;WACHH,QAAF,EAAYC,SAAZ,EAAuBC,QAAvB,IAAoC,CAAE,IAAF,EAAQ,KAAR,EAAe,OAAf,CAApC;;;;UAIAF,QAAJ,EAAc;QACZJ,IAAI,GAAG,KAAKpB,MAAL,CAAYwB,QAAZ,CAAP;QACA79B,OAAO,CAACo8B,YAAR,CAAqBj8B,GAArB,CAAyBs9B,IAAzB;OAFF,MAGO,IAAIz9B,OAAO,CAACo8B,YAAZ,EAA0B;QAC/BqB,IAAI,GAAGz9B,OAAO,CAACo8B,YAAf;;;UAGEp6B,CAAJ;;UACI,CAACA,CAAC,GAAGs7B,MAAM,CAACr7B,CAAC,EAAF,CAAX,MAAsBo7B,KAA1B,EAAiC;cACzBY,IAAI,GAAGd,UAAU,IAAIn7B,CAAC,GAAGq7B,KAAR,CAAvB;aACKvwB,CAAL,IAAUmxB,IAAV;QACAzB,OAAO,CAACzR,SAAR,IAAqBkT,IAArB;QACAZ,KAAK,GAAGr7B,CAAR;;;UAGEy7B,IAAI,KAAKK,SAAS,IAAIC,QAAlB,CAAR,EAAqC;QACnCN,IAAI,CAACt9B,GAAL,CAAS,KAAKk8B,MAAL,CAAYyB,SAAS,IAAIC,QAAzB,EACP,CAAE,KAAKxB,oBAAL,CAA0BuB,SAAS,IAAIC,QAAvC,CAAF,CADO,CAAT;;;cAGMjB,QAAR;aACO,QAAL;eACO5P,MAAL,CAAY,KAAKpgB,CAAL,GAASksB,MAAT,GAAkBjpB,CAA9B,EAAiC,KAAKyX,CAAL,GAASwV,OAA1C,EAAmDjtB,CAAnD;eACK6e,IAAL;;;aAEG,UAAL;aACK,UAAL;cACMsC,IAAI,GAAGwM,KAAK,CAACH,OAAO,CAACt7B,CAAC,GAAG,CAAL,CAAR,CAAhB;;eACKi8B,SAAL,CAAehN,IAAf,EAAqB,KAAKpkB,CAAL,GAASksB,MAA9B,EAAsC,KAAKxR,CAA3C,EAA8CxnB,OAA9C;;;;;UAIAy9B,IAAI,IAAIK,SAAR,IAAqBC,QAAzB,EAAmC;QACjCN,IAAI,CAACt9B,GAAL,CAAS,KAAKk8B,MAAL,CAAY0B,QAAZ,EAAsB,CAAE,KAAKxB,oBAAL,CAA0BwB,QAA1B,CAAF,CAAtB,CAAT;;;UAEEN,IAAI,IAAIA,IAAI,KAAKz9B,OAAO,CAACo8B,YAA7B,EAA2C;QACzCqB,IAAI,CAACv7B,GAAL;;KA7CJ;IAiDAs6B,OAAO,CAACrH,EAAR,CAAW,cAAX,EAA2B,MAAM;YACzBvf,GAAG,GAAGojB,MAAM,GAAGmE,UAAU,IAAIE,KAAK,GAAG,CAAZ,CAA/B;WACKvwB,CAAL,IAAU8I,GAAV;aACQ4mB,OAAO,CAACzR,SAAR,IAAqBnV,GAA7B;KAHF;IAMA4mB,OAAO,CAACrH,EAAR,CAAW,YAAX,EAAyB,MAAM;YACvBvf,GAAG,GAAGojB,MAAM,GAAGmE,UAAU,IAAIE,KAAK,GAAG,CAAZ,CAA/B;WACKvwB,CAAL,IAAU8I,GAAV;aACQ4mB,OAAO,CAACzR,SAAR,IAAqBnV,GAA7B;KAHF;IAMA4mB,OAAO,CAAC1B,IAAR,CAAat3B,KAAK,CAACjC,IAAN,CAAW,IAAX,CAAb,EAA+BvB,OAA/B;WAEO,IAAP;GA1NW;;EA6Nbk8B,YAAY,CAACpvB,CAAC,GAAG,EAAL,EAAS0a,CAAT,EAAYxnB,OAAO,GAAG,EAAtB,EAA0B;QAChC,OAAO8M,CAAP,KAAa,QAAjB,EAA2B;MACzB9M,OAAO,GAAG8M,CAAV;MACAA,CAAC,GAAG,IAAJ;KAHkC;;;UAO9Bod,MAAM,GAAG1pB,MAAM,CAAC29B,MAAP,CAAc,EAAd,EAAkBn+B,OAAlB,CAAf,CAPoC;;QAUhC,KAAK08B,YAAT,EAAuB;WAChB,IAAIt8B,GAAT,IAAgB,KAAKs8B,YAArB,EAAmC;cAC3Br8B,GAAG,GAAG,KAAKq8B,YAAL,CAAkBt8B,GAAlB,CAAZ;;YACIA,GAAG,KAAK,WAAZ,EAAyB;cACnB8pB,MAAM,CAAC9pB,GAAD,CAAN,KAAgBk1B,SAApB,EAA+B;YAC7BpL,MAAM,CAAC9pB,GAAD,CAAN,GAAcC,GAAd;;;;KAf4B;;;QAsBhCyM,CAAC,IAAI,IAAT,EAAe;WACRA,CAAL,GAASA,CAAT;;;QAEE0a,CAAC,IAAI,IAAT,EAAe;WACRA,CAAL,GAASA,CAAT;KA1BkC;;;QA8BhC0C,MAAM,CAACkU,SAAP,KAAqB,KAAzB,EAAgC;UAC1BlU,MAAM,CAAC/gB,KAAP,IAAgB,IAApB,EAA0B;QACxB+gB,MAAM,CAAC/gB,KAAP,GAAe,KAAK4O,IAAL,CAAU5O,KAAV,GAAkB,KAAK2D,CAAvB,GAA2B,KAAKiL,IAAL,CAAU/O,OAAV,CAAkBtD,KAA5D;;;MAEFwkB,MAAM,CAAC/gB,KAAP,GAAerF,IAAI,CAACmS,GAAL,CAASiU,MAAM,CAAC/gB,KAAhB,EAAuB,CAAvB,CAAf;;;QAGE,CAAC+gB,MAAM,CAACiP,OAAZ,EAAqB;MACnBjP,MAAM,CAACiP,OAAP,GAAiB,CAAjB;;;QAEEjP,MAAM,CAACkP,SAAP,IAAoB,IAAxB,EAA8B;MAC5BlP,MAAM,CAACkP,SAAP,GAAmB,EAAnB;KAzCkC;;;WA4C7BlP,MAAP;GAzQW;;EA4QbwR,KAAK,CAACxK,IAAD,EAAOlxB,OAAO,GAAG,EAAjB,EAAqBw8B,OAArB,EAA8B;SAC5B0B,SAAL,CAAehN,IAAf,EAAqB,KAAKpkB,CAA1B,EAA6B,KAAK0a,CAAlC,EAAqCxnB,OAArC;;UACM6wB,OAAO,GAAG7wB,OAAO,CAAC6wB,OAAR,IAAmB,KAAK+K,QAAxB,IAAoC,CAApD;;QAEI,CAACY,OAAL,EAAc;aACJ,KAAK1vB,CAAL,IAAU,KAAKilB,aAAL,CAAmBb,IAAnB,CAAlB;KADF,MAEO;aACG,KAAK1J,CAAL,IAAU,KAAKoR,iBAAL,CAAuB,IAAvB,IAA+B/H,OAAjD;;GAnRS;;EAuRbqN,SAAS,CAAChN,IAAD,EAAOpkB,CAAP,EAAU0a,CAAV,EAAaxnB,OAAb,EAAsB;QACzBwW,EAAJ,EAAQqc,OAAR,EAAiB5wB,CAAjB,EAAoB6wB,SAApB,EAA+BmI,SAA/B,EAA0CjpB,KAA1C;IACAkf,IAAI,GAAI,GAAEA,IAAK,EAAR,CAAUruB,OAAV,CAAkB,KAAlB,EAAyB,EAAzB,CAAP;;QACIquB,IAAI,CAACnwB,MAAL,KAAgB,CAApB,EAAuB;;KAHM;;;UAQvB84B,KAAK,GAAG75B,OAAO,CAAC65B,KAAR,IAAiB,MAA/B;QACIX,WAAW,GAAGl5B,OAAO,CAACk5B,WAAR,IAAuB,CAAzC;UACMD,gBAAgB,GAAGj5B,OAAO,CAACi5B,gBAAR,IAA4B,CAArD,CAV6B;;QAazBj5B,OAAO,CAACmJ,KAAZ,EAAmB;cACT0wB,KAAR;aACO,OAAL;UACEoB,SAAS,GAAG,KAAKlJ,aAAL,CAAmBb,IAAI,CAACruB,OAAL,CAAa,MAAb,EAAqB,EAArB,CAAnB,EAA6C7C,OAA7C,CAAZ;UACA8M,CAAC,IAAI9M,OAAO,CAAC+qB,SAAR,GAAoBkQ,SAAzB;;;aAGG,QAAL;UACEnuB,CAAC,IAAI9M,OAAO,CAAC+qB,SAAR,GAAoB,CAApB,GAAwB/qB,OAAO,CAACi7B,SAAR,GAAoB,CAAjD;;;aAGG,SAAL;;UAEEjpB,KAAK,GAAGkf,IAAI,CAACmN,IAAL,GAAY1O,KAAZ,CAAkB,KAAlB,CAAR;UACAsL,SAAS,GAAG,KAAKlJ,aAAL,CAAmBb,IAAI,CAACruB,OAAL,CAAa,MAAb,EAAqB,EAArB,CAAnB,EAA6C7C,OAA7C,CAAZ;cACIs+B,UAAU,GAAG,KAAKvM,aAAL,CAAmB,GAAnB,IAA0BkH,gBAA3C;UACAC,WAAW,GAAGp1B,IAAI,CAACmS,GAAL,CACZ,CADY,EAEZ,CAACjW,OAAO,CAAC+qB,SAAR,GAAoBkQ,SAArB,IAAkCn3B,IAAI,CAACmS,GAAL,CAAS,CAAT,EAAYjE,KAAK,CAACjR,MAAN,GAAe,CAA3B,CAAlC,GACEu9B,UAHU,CAAd;;;KA7BuB;;;QAuCzB,OAAOt+B,OAAO,CAACu+B,QAAf,KAA4B,QAAhC,EAA0C;MACxC/nB,EAAE,GAAG,CAACxW,OAAO,CAACu+B,QAAd;KADF,MAEO;cACGv+B,OAAO,CAACu+B,QAAhB;aACO,YAAL;UACE/nB,EAAE,GAAG,MAAM,KAAKgiB,KAAL,CAAW7H,OAAtB;;;aAEG,QAAL;aACK,aAAL;UACEna,EAAE,GAAG,OAAO,KAAKgiB,KAAL,CAAW9H,SAAX,GAAuB,KAAK8H,KAAL,CAAW/H,QAAzC,CAAL;;;aAEG,QAAL;aACK,aAAL;UACEja,EAAE,GAAG,KAAKgiB,KAAL,CAAW9H,SAAhB;;;aAEG,YAAL;UACEla,EAAE,GAAG,CAAL;;;aAEG,cAAL;UACEA,EAAE,GAAG,MAAM,KAAKgiB,KAAL,CAAW/H,QAAtB;;;aAEG,SAAL;UACEja,EAAE,GAAG,MAAM,KAAKgiB,KAAL,CAAW/H,QAAtB;;;aAEG,KAAL;UACEja,EAAE,GAAG,KAAKgiB,KAAL,CAAW/H,QAAhB;;;;UAGAja,EAAE,GAAG,KAAKgiB,KAAL,CAAW/H,QAAhB;;;MAEJja,EAAE,GAAIA,EAAE,GAAG,IAAN,GAAc,KAAK+hB,SAAxB;KArE2B;;;UAyEvBiG,aAAa,GACjBx+B,OAAO,CAACi7B,SAAR,GACA/B,WAAW,IAAIl5B,OAAO,CAACq7B,SAAR,GAAoB,CAAxB,CADX,GAEApC,gBAAgB,IAAI/H,IAAI,CAACnwB,MAAL,GAAc,CAAlB,CAHlB,CAzE6B;;QA+EzBf,OAAO,CAACy+B,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAU3xB,CAAV,EAAa0a,CAAb,EAAgBgX,aAAhB,EAA+B,KAAK5F,iBAAL,EAA/B,EAAyD54B,OAAO,CAACy+B,IAAjE;;;QAEEz+B,OAAO,CAAC0+B,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAU5xB,CAAV,EAAa0a,CAAb,EAAgBgX,aAAhB,EAA+B,KAAK5F,iBAAL,EAA/B,EAAyD54B,OAAO,CAAC0+B,IAAjE;;;QAEE1+B,OAAO,CAAC2+B,WAAR,IAAuB,IAA3B,EAAiC;WAC1BC,mBAAL,CAAyB5+B,OAAO,CAAC2+B,WAAjC,EAA8C,KAA9C,EAAqD7xB,CAArD,EAAwD0a,CAAxD,EAA2D,IAA3D;KAtF2B;;;QA0FzBxnB,OAAO,CAAC6+B,SAAZ,EAAuB;WAChBjU,IAAL;;UACI,CAAC5qB,OAAO,CAACib,MAAb,EAAqB;aACdO,WAAL,CAAiB,IAAI,KAAKD,UAAL,IAAmB,EAAvB,CAAjB;;;YAGIwP,SAAS,GACb,KAAKwN,SAAL,GAAiB,EAAjB,GAAsB,GAAtB,GAA4Bz0B,IAAI,CAAC2H,KAAL,CAAW,KAAK8sB,SAAL,GAAiB,EAA5B,CAD9B;WAEKxN,SAAL,CAAeA,SAAf;UAEI+T,KAAK,GAAItX,CAAC,GAAG,KAAKoR,iBAAL,EAAL,GAAkC7N,SAA9C;WACK7D,MAAL,CAAYpa,CAAZ,EAAegyB,KAAf;WACKxX,MAAL,CAAYxa,CAAC,GAAG0xB,aAAhB,EAA+BM,KAA/B;WACK7jB,MAAL;WACK4P,OAAL;KAxG2B;;;QA4GzB7qB,OAAO,CAAC++B,MAAZ,EAAoB;WACbnU,IAAL;;UACI,CAAC5qB,OAAO,CAACib,MAAb,EAAqB;aACdO,WAAL,CAAiB,IAAI,KAAKD,UAAL,IAAmB,EAAvB,CAAjB;;;YAGIwP,SAAS,GACb,KAAKwN,SAAL,GAAiB,EAAjB,GAAsB,GAAtB,GAA4Bz0B,IAAI,CAAC2H,KAAL,CAAW,KAAK8sB,SAAL,GAAiB,EAA5B,CAD9B;WAEKxN,SAAL,CAAeA,SAAf;UAEI+T,KAAK,GAAGtX,CAAC,GAAG,KAAKoR,iBAAL,KAA2B,CAA3C;WACK1R,MAAL,CAAYpa,CAAZ,EAAegyB,KAAf;WACKxX,MAAL,CAAYxa,CAAC,GAAG0xB,aAAhB,EAA+BM,KAA/B;WACK7jB,MAAL;WACK4P,OAAL;;;SAGGD,IAAL,GA7H6B;;QAgIzB5qB,OAAO,CAACg/B,OAAZ,EAAqB;UACfC,IAAJ;;UACI,OAAOj/B,OAAO,CAACg/B,OAAf,KAA2B,QAA/B,EAAyC;QACvCC,IAAI,GAAG,CAACn7B,IAAI,CAAC0gB,GAAL,CAAUxkB,OAAO,CAACg/B,OAAR,GAAkBl7B,IAAI,CAAC4kB,EAAxB,GAA8B,GAAvC,CAAR;OADF,MAEO;QACLuW,IAAI,GAAG,CAAC,IAAR;;;WAEGvpB,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,EAA2B5I,CAA3B,EAA8B0a,CAA9B;WACK9R,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqBupB,IAArB,EAA2B,CAA3B,EAA8B,CAACA,IAAD,GAAQzoB,EAAtC,EAA0C,CAA1C;WACKd,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAxB,EAA2B,CAAC5I,CAA5B,EAA+B,CAAC0a,CAAhC;KAzI2B;;;SA6IxB9R,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAC,CAAzB,EAA4B,CAA5B,EAA+B,KAAKqC,IAAL,CAAU3O,MAAzC;IACAoe,CAAC,GAAG,KAAKzP,IAAL,CAAU3O,MAAV,GAAmBoe,CAAnB,GAAuBhR,EAA3B,CA9I6B;;QAiJzB,KAAKuB,IAAL,CAAU7N,KAAV,CAAgB,KAAKsuB,KAAL,CAAWt0B,EAA3B,KAAkC,IAAtC,EAA4C;WACrC6T,IAAL,CAAU7N,KAAV,CAAgB,KAAKsuB,KAAL,CAAWt0B,EAA3B,IAAiC,KAAKs0B,KAAL,CAAWlvB,GAAX,EAAjC;KAlJ2B;;;SAsJxBmQ,UAAL,CAAgB,IAAhB,EAtJ6B;;SAyJxBA,UAAL,CAAiB,WAAU7V,QAAM,CAACkJ,CAAD,CAAI,IAAGlJ,QAAM,CAAC4jB,CAAD,CAAI,KAAlD,EAzJ6B;;SA4JxB/N,UAAL,CAAiB,IAAG,KAAK+e,KAAL,CAAWt0B,EAAG,IAAGN,QAAM,CAAC,KAAK20B,SAAN,CAAiB,KAA5D,EA5J6B;;UA+JvBllB,IAAI,GAAGrT,OAAO,CAAC4uB,IAAR,IAAgB5uB,OAAO,CAACib,MAAxB,GAAiC,CAAjC,GAAqCjb,OAAO,CAACib,MAAR,GAAiB,CAAjB,GAAqB,CAAvE;;QACI5H,IAAJ,EAAU;WACHoG,UAAL,CAAiB,GAAEpG,IAAK,KAAxB;KAjK2B;;;QAqKzB4lB,gBAAJ,EAAsB;WACfxf,UAAL,CAAiB,GAAE7V,QAAM,CAACq1B,gBAAD,CAAmB,KAA5C;KAtK2B;;;;;;QA6KzBC,WAAJ,EAAiB;MACflnB,KAAK,GAAGkf,IAAI,CAACmN,IAAL,GAAY1O,KAAZ,CAAkB,KAAlB,CAAR;MACAuJ,WAAW,IAAI,KAAKnH,aAAL,CAAmB,GAAnB,IAA0BkH,gBAAzC;MACAC,WAAW,IAAI,OAAO,KAAKX,SAA3B;MAEA1F,OAAO,GAAG,EAAV;MACAC,SAAS,GAAG,EAAZ;;WACK,IAAImH,IAAT,IAAiBjoB,KAAjB,EAAwB;cAChB,CAACktB,WAAD,EAAcC,aAAd,IAA+B,KAAK3G,KAAL,CAAWzhB,MAAX,CACnCkjB,IADmC,EAEnCj6B,OAAO,CAACq0B,QAF2B,CAArC;;QAIAxB,OAAO,GAAGA,OAAO,CAAC5tB,MAAR,CAAei6B,WAAf,CAAV;QACApM,SAAS,GAAGA,SAAS,CAAC7tB,MAAV,CAAiBk6B,aAAjB,CAAZ,CANsB;;;cAUhBhkB,KAAK,GAAG,EAAd;cACMhZ,MAAM,GAAG2wB,SAAS,CAACA,SAAS,CAAC/xB,MAAV,GAAmB,CAApB,CAAxB;;aACK,IAAIX,GAAT,IAAgB+B,MAAhB,EAAwB;gBAChB9B,GAAG,GAAG8B,MAAM,CAAC/B,GAAD,CAAlB;UACA+a,KAAK,CAAC/a,GAAD,CAAL,GAAaC,GAAb;;;QAEF8a,KAAK,CAAC4X,QAAN,IAAkBmG,WAAlB;QACApG,SAAS,CAACA,SAAS,CAAC/xB,MAAV,GAAmB,CAApB,CAAT,GAAkCoa,KAAlC;;KAxBJ,MA0BO;OACJ0X,OAAD,EAAUC,SAAV,IAAuB,KAAK0F,KAAL,CAAWzhB,MAAX,CAAkBma,IAAlB,EAAwBlxB,OAAO,CAACq0B,QAAhC,CAAvB;;;UAGI/E,KAAK,GAAG,KAAKiJ,SAAL,GAAiB,IAA/B;UACMvR,QAAQ,GAAG,EAAjB;QACI/lB,IAAI,GAAG,CAAX;QACIm+B,SAAS,GAAG,KAAhB,CA9M6B;;UAiNvBC,UAAU,GAAGC,GAAG,IAAI;UACpBr+B,IAAI,GAAGq+B,GAAX,EAAgB;cACR1kB,GAAG,GAAGiY,OAAO,CAAClxB,KAAR,CAAcV,IAAd,EAAoBq+B,GAApB,EAAyB/9B,IAAzB,CAA8B,EAA9B,CAAZ;cACM6xB,OAAO,GACXN,SAAS,CAACwM,GAAG,GAAG,CAAP,CAAT,CAAmBvM,QAAnB,GAA8BD,SAAS,CAACwM,GAAG,GAAG,CAAP,CAAT,CAAmBnM,YADnD;QAEAnM,QAAQ,CAAC9lB,IAAT,CAAe,IAAG0Z,GAAI,KAAIhX,QAAM,CAAC,CAACwvB,OAAF,CAAW,EAA3C;;;aAGMnyB,IAAI,GAAGq+B,GAAf;KARF,CAjN6B;;;UA6NvBC,KAAK,GAAGt9B,CAAC,IAAI;MACjBo9B,UAAU,CAACp9B,CAAD,CAAV;;UAEI+kB,QAAQ,CAACjmB,MAAT,GAAkB,CAAtB,EAAyB;aAClB0Y,UAAL,CAAiB,IAAGuN,QAAQ,CAACzlB,IAAT,CAAc,GAAd,CAAmB,MAAvC;eACQylB,QAAQ,CAACjmB,MAAT,GAAkB,CAA1B;;KALJ;;SASKkB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG6wB,SAAS,CAAC/xB,MAA1B,EAAkCkB,CAAC,EAAnC,EAAuC;;;YAG/B2T,GAAG,GAAGkd,SAAS,CAAC7wB,CAAD,CAArB;;UACI2T,GAAG,CAACqd,OAAJ,IAAerd,GAAG,CAACsd,OAAvB,EAAgC;;QAE9BqM,KAAK,CAACt9B,CAAD,CAAL,CAF8B;;aAKzBwX,UAAL,CACG,WAAU7V,QAAM,CAACkJ,CAAC,GAAG8I,GAAG,CAACqd,OAAJ,GAAc3D,KAAnB,CAA0B,IAAG1rB,QAAM,CAClD4jB,CAAC,GAAG5R,GAAG,CAACsd,OAAJ,GAAc5D,KADgC,CAElD,KAHJ;QAKAiQ,KAAK,CAACt9B,CAAC,GAAG,CAAL,CAAL;QAEAm9B,SAAS,GAAG,IAAZ;OAZF,MAaO;;YAEDA,SAAJ,EAAe;eACR3lB,UAAL,CAAiB,WAAU7V,QAAM,CAACkJ,CAAD,CAAI,IAAGlJ,QAAM,CAAC4jB,CAAD,CAAI,KAAlD;UACA4X,SAAS,GAAG,KAAZ;SAJG;;;YAQDxpB,GAAG,CAACmd,QAAJ,GAAend,GAAG,CAACud,YAAnB,KAAoC,CAAxC,EAA2C;UACzCkM,UAAU,CAACp9B,CAAC,GAAG,CAAL,CAAV;;;;MAIJ6K,CAAC,IAAI8I,GAAG,CAACmd,QAAJ,GAAezD,KAApB;KApQ2B;;;IAwQ7BiQ,KAAK,CAACt9B,CAAD,CAAL,CAxQ6B;;SA2QxBwX,UAAL,CAAgB,IAAhB,EA3Q6B;;WA8QtB,KAAKoR,OAAL,EAAP;;;CAriBJ;;ACLA,MAAM2U,OAAO,GAAG,CACd,MADc,EAEd,MAFc,EAGd,MAHc,EAId,MAJc,EAKd,MALc,EAMd,MANc,EAOd,MAPc,EAQd,MARc,EASd,MATc,EAUd,MAVc,EAWd,MAXc,EAYd,MAZc,EAad,MAbc,EAcd,MAdc,EAed,MAfc,CAAhB;AAkBA,MAAMC,eAAe,GAAG;KACnB,YADmB;KAEnB,WAFmB;KAGnB;CAHL;;AAMA,MAAMC,IAAN,CAAW;EACT3/B,WAAW,CAACoE,IAAD,EAAOu5B,KAAP,EAAc;QACnBiC,MAAJ;SACKx7B,IAAL,GAAYA,IAAZ;SACKu5B,KAAL,GAAaA,KAAb;;QACI,KAAKv5B,IAAL,CAAUy7B,YAAV,CAAuB,CAAvB,MAA8B,MAAlC,EAA0C;YAClC,uBAAN;;;QAGEhqB,GAAG,GAAG,CAAV;;WACOA,GAAG,GAAG,KAAKzR,IAAL,CAAUpD,MAAvB,EAA+B;MAC7B4+B,MAAM,GAAG,KAAKx7B,IAAL,CAAUy7B,YAAV,CAAuBhqB,GAAvB,CAAT;MACAA,GAAG,IAAI,CAAP;;UACI4pB,OAAO,CAACzY,QAAR,CAAiB4Y,MAAjB,CAAJ,EAA8B;;;;MAG9B/pB,GAAG,IAAI,KAAKzR,IAAL,CAAUy7B,YAAV,CAAuBhqB,GAAvB,CAAP;;;QAGE,CAAC4pB,OAAO,CAACzY,QAAR,CAAiB4Y,MAAjB,CAAL,EAA+B;YACvB,eAAN;;;IAEF/pB,GAAG,IAAI,CAAP;SAEKiqB,IAAL,GAAY,KAAK17B,IAAL,CAAUyR,GAAG,EAAb,CAAZ;SACKxM,MAAL,GAAc,KAAKjF,IAAL,CAAUy7B,YAAV,CAAuBhqB,GAAvB,CAAd;IACAA,GAAG,IAAI,CAAP;SAEKzM,KAAL,GAAa,KAAKhF,IAAL,CAAUy7B,YAAV,CAAuBhqB,GAAvB,CAAb;IACAA,GAAG,IAAI,CAAP;UAEMkqB,QAAQ,GAAG,KAAK37B,IAAL,CAAUyR,GAAG,EAAb,CAAjB;SACKmqB,UAAL,GAAkBN,eAAe,CAACK,QAAD,CAAjC;SAEKltB,GAAL,GAAW,IAAX;;;EAGF6D,KAAK,CAACxS,QAAD,EAAW;QACV,KAAK2O,GAAT,EAAc;;;;SAITA,GAAL,GAAW3O,QAAQ,CAACqF,GAAT,CAAa;MACtBI,IAAI,EAAE,SADgB;MAEtBuO,OAAO,EAAE,OAFa;MAGtB+nB,gBAAgB,EAAE,KAAKH,IAHD;MAItBI,KAAK,EAAE,KAAK92B,KAJU;MAKtB+2B,MAAM,EAAE,KAAK92B,MALS;MAMtB4Q,UAAU,EAAE,KAAK+lB,UANK;MAOtBz7B,MAAM,EAAE;KAPC,CAAX,CALc;;;;QAkBV,KAAKy7B,UAAL,KAAoB,YAAxB,EAAsC;WAC/BntB,GAAL,CAASzO,IAAT,CAAc,QAAd,IAA0B,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,GAApC,CAA1B;;;SAGGyO,GAAL,CAAS1Q,GAAT,CAAa,KAAKiC,IAAlB,EAtBc;;WAyBN,KAAKA,IAAL,GAAY,IAApB;;;;;ACnFJ,MAAMg8B,QAAN,CAAe;EACbpgC,WAAW,CAACoE,IAAD,EAAOu5B,KAAP,EAAc;SAClBA,KAAL,GAAaA,KAAb;SACK0C,KAAL,GAAa,IAAIC,GAAJ,CAAQl8B,IAAR,CAAb;SACKgF,KAAL,GAAa,KAAKi3B,KAAL,CAAWj3B,KAAxB;SACKC,MAAL,GAAc,KAAKg3B,KAAL,CAAWh3B,MAAzB;SACKk3B,OAAL,GAAe,KAAKF,KAAL,CAAWE,OAA1B;SACK1tB,GAAL,GAAW,IAAX;;;EAGF6D,KAAK,CAACxS,QAAD,EAAW;QACVs8B,WAAW,GAAG,KAAlB;SAEKt8B,QAAL,GAAgBA,QAAhB;;QACI,KAAK2O,GAAT,EAAc;;;;UAIR4tB,eAAe,GAAG,KAAKJ,KAAL,CAAWI,eAAnC;UACMC,YAAY,GAAG,KAAKL,KAAL,CAAWM,eAAX,KAA+B,CAApD;SAEK9tB,GAAL,GAAW,KAAK3O,QAAL,CAAcqF,GAAd,CAAkB;MAC3BI,IAAI,EAAE,SADqB;MAE3BuO,OAAO,EAAE,OAFkB;MAG3B+nB,gBAAgB,EAAEQ,eAAe,GAAG,CAAH,GAAO,KAAKJ,KAAL,CAAWP,IAHxB;MAI3BI,KAAK,EAAE,KAAK92B,KAJe;MAK3B+2B,MAAM,EAAE,KAAK92B,MALc;MAM3B9E,MAAM,EAAE;KANC,CAAX;;QASI,CAACk8B,eAAL,EAAsB;YACd1Z,MAAM,GAAG,KAAK7iB,QAAL,CAAcqF,GAAd,CAAkB;QAC/Bq3B,SAAS,EAAEF,YAAY,GAAG,CAAH,GAAO,EADC;QAE/BG,MAAM,EAAE,KAAKR,KAAL,CAAWS,MAFY;QAG/Bb,gBAAgB,EAAE,KAAKI,KAAL,CAAWP,IAHE;QAI/BiB,OAAO,EAAE,KAAK33B;OAJD,CAAf;WAOKyJ,GAAL,CAASzO,IAAT,CAAc,aAAd,IAA+B2iB,MAA/B;MACAA,MAAM,CAAC5kB,GAAP;;;QAGE,KAAKk+B,KAAL,CAAWW,OAAX,CAAmBhgC,MAAnB,KAA8B,CAAlC,EAAqC;WAC9B6R,GAAL,CAASzO,IAAT,CAAc,YAAd,IAA8B,KAAKi8B,KAAL,CAAWL,UAAzC;KADF,MAEO;;YAECgB,OAAO,GAAG,KAAK98B,QAAL,CAAcqF,GAAd,EAAhB;MACAy3B,OAAO,CAAC7+B,GAAR,CAAYQ,MAAM,CAACC,IAAP,CAAY,KAAKy9B,KAAL,CAAWW,OAAvB,CAAZ,EAHK;;WAMAnuB,GAAL,CAASzO,IAAT,CAAc,YAAd,IAA8B,CAC5B,SAD4B,EAE5B,WAF4B,EAG5B,KAAKi8B,KAAL,CAAWW,OAAX,CAAmBhgC,MAAnB,GAA4B,CAA5B,GAAgC,CAHJ,EAI5BggC,OAJ4B,CAA9B;KAxCY;;;;QAkDV,KAAKX,KAAL,CAAWY,YAAX,CAAwBC,SAAxB,IAAqC,IAAzC,EAA+C;;;YAGvC5gC,GAAG,GAAG,KAAK+/B,KAAL,CAAWY,YAAX,CAAwBC,SAApC;WACKruB,GAAL,CAASzO,IAAT,CAAc,MAAd,IAAwB,CAAC9D,GAAD,EAAMA,GAAN,CAAxB;KAJF,MAKO,IAAI,KAAK+/B,KAAL,CAAWY,YAAX,CAAwBE,GAA5B,EAAiC;;;YAGhC;QAAEA;UAAQ,KAAKd,KAAL,CAAWY,YAA3B;YACMG,IAAI,GAAG,EAAb;;WACK,IAAIr0B,CAAT,IAAco0B,GAAd,EAAmB;QACjBC,IAAI,CAACjgC,IAAL,CAAU4L,CAAV,EAAaA,CAAb;;;WAGG8F,GAAL,CAASzO,IAAT,CAAc,MAAd,IAAwBg9B,IAAxB;KATK,MAUA,IAAI,KAAKf,KAAL,CAAWY,YAAX,CAAwBI,OAA5B,EAAqC;;;MAG1Cb,WAAW,GAAG,IAAd;aACO,KAAKc,uBAAL,EAAP;KAJK,MAKA,IAAIb,eAAJ,EAAqB;;;;MAI1BD,WAAW,GAAG,IAAd;aACO,KAAKe,iBAAL,EAAP;;;QAGEb,YAAY,IAAI,CAACF,WAArB,EAAkC;aACzB,KAAKgB,UAAL,EAAP;;;SAGG38B,QAAL;;;EAGFA,QAAQ,GAAG;QACL,KAAK48B,YAAT,EAAuB;YACfC,KAAK,GAAG,KAAKx9B,QAAL,CAAcqF,GAAd,CAAkB;QAC9BI,IAAI,EAAE,SADwB;QAE9BuO,OAAO,EAAE,OAFqB;QAG9BioB,MAAM,EAAE,KAAK92B,MAHiB;QAI9B62B,KAAK,EAAE,KAAK92B,KAJkB;QAK9B62B,gBAAgB,EAAE,CALY;QAM9B17B,MAAM,EAAE,aANsB;QAO9B0V,UAAU,EAAE,YAPkB;QAQ9B0nB,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ;OARI,CAAd;MAWAD,KAAK,CAACv/B,GAAN,CAAU,KAAKs/B,YAAf;WACK5uB,GAAL,CAASzO,IAAT,CAAc,OAAd,IAAyBs9B,KAAzB;KAdO;;;SAkBJ7uB,GAAL,CAAS1Q,GAAT,CAAa,KAAKo+B,OAAlB,EAlBS;;SAqBJF,KAAL,GAAa,IAAb;WACQ,KAAKE,OAAL,GAAe,IAAvB;;;EAGFgB,iBAAiB,GAAG;WACX,KAAKlB,KAAL,CAAWuB,YAAX,CAAwBC,MAAM,IAAI;UACnCjhC,CAAJ,EAAOkhC,CAAP;YACMC,UAAU,GAAG,KAAK1B,KAAL,CAAWS,MAA9B;YACMkB,UAAU,GAAG,KAAK54B,KAAL,GAAa,KAAKC,MAArC;YACMk3B,OAAO,GAAG59B,MAAM,CAACoS,KAAP,CAAaitB,UAAU,GAAGD,UAA1B,CAAhB;YACMN,YAAY,GAAG9+B,MAAM,CAACoS,KAAP,CAAaitB,UAAb,CAArB;UAEI9/B,CAAC,GAAI4/B,CAAC,GAAGlhC,CAAC,GAAG,CAAjB;YACMywB,GAAG,GAAGwQ,MAAM,CAAC7gC,MAAnB,CARuC;;YAUjCihC,aAAa,GAAG,KAAK5B,KAAL,CAAWP,IAAX,KAAoB,EAApB,GAAyB,CAAzB,GAA6B,CAAnD;;aACO59B,CAAC,GAAGmvB,GAAX,EAAgB;aACT,IAAI6Q,UAAU,GAAG,CAAtB,EAAyBA,UAAU,GAAGH,UAAtC,EAAkDG,UAAU,EAA5D,EAAgE;UAC9D3B,OAAO,CAACuB,CAAC,EAAF,CAAP,GAAeD,MAAM,CAAC3/B,CAAC,EAAF,CAArB;UACAA,CAAC,IAAI+/B,aAAL;;;QAEFR,YAAY,CAAC7gC,CAAC,EAAF,CAAZ,GAAoBihC,MAAM,CAAC3/B,CAAC,EAAF,CAA1B;QACAA,CAAC,IAAI+/B,aAAL;;;WAGG1B,OAAL,GAAep7B,IAAI,CAACC,WAAL,CAAiBm7B,OAAjB,CAAf;WACKkB,YAAL,GAAoBt8B,IAAI,CAACC,WAAL,CAAiBq8B,YAAjB,CAApB;aACO,KAAK58B,QAAL,EAAP;KAtBK,CAAP;;;EA0BFy8B,uBAAuB,GAAG;UAClBL,YAAY,GAAG,KAAKZ,KAAL,CAAWY,YAAX,CAAwBI,OAA7C;WACO,KAAKhB,KAAL,CAAWuB,YAAX,CAAwBC,MAAM,IAAI;YACjCJ,YAAY,GAAG9+B,MAAM,CAACoS,KAAP,CAAa,KAAK3L,KAAL,GAAa,KAAKC,MAA/B,CAArB;UAEInH,CAAC,GAAG,CAAR;;WACK,IAAIqS,CAAC,GAAG,CAAR,EAAWpS,GAAG,GAAG0/B,MAAM,CAAC7gC,MAA7B,EAAqCuT,CAAC,GAAGpS,GAAzC,EAA8CoS,CAAC,EAA/C,EAAmD;QACjDktB,YAAY,CAACv/B,CAAC,EAAF,CAAZ,GAAoB++B,YAAY,CAACY,MAAM,CAACttB,CAAD,CAAP,CAAhC;;;WAGGktB,YAAL,GAAoBt8B,IAAI,CAACC,WAAL,CAAiBq8B,YAAjB,CAApB;aACO,KAAK58B,QAAL,EAAP;KATK,CAAP;;;EAaF28B,UAAU,GAAG;SACNnB,KAAL,CAAWuB,YAAX,CAAwBC,MAAM,IAAI;WAC3BtB,OAAL,GAAep7B,IAAI,CAACC,WAAL,CAAiBy8B,MAAjB,CAAf;WACKh9B,QAAL;KAFF;;;;;ACtKJ;;;;AAKA;AAIA,MAAMs9B,QAAN,CAAe;SACNrS,IAAP,CAAYiI,GAAZ,EAAiB4F,KAAjB,EAAwB;QAClBv5B,IAAJ;;QACIzB,MAAM,CAACK,QAAP,CAAgB+0B,GAAhB,CAAJ,EAA0B;MACxB3zB,IAAI,GAAG2zB,GAAP;KADF,MAEO,IAAIA,GAAG,YAAYI,WAAnB,EAAgC;MACrC/zB,IAAI,GAAGzB,MAAM,CAACC,IAAP,CAAY,IAAIs1B,UAAJ,CAAeH,GAAf,CAAZ,CAAP;KADK,MAEA;UACD9G,KAAJ;;UACKA,KAAK,GAAG,wBAAwBmR,IAAxB,CAA6BrK,GAA7B,CAAb,EAAiD;QAC/C3zB,IAAI,GAAGzB,MAAM,CAACC,IAAP,CAAYquB,KAAK,CAAC,CAAD,CAAjB,EAAsB,QAAtB,CAAP;OADF,MAEO;QACL7sB,IAAI,GAAG4rB,EAAE,CAACC,YAAH,CAAgB8H,GAAhB,CAAP;;YACI,CAAC3zB,IAAL,EAAW;;;;;;QAMXA,IAAI,CAAC,CAAD,CAAJ,KAAY,IAAZ,IAAoBA,IAAI,CAAC,CAAD,CAAJ,KAAY,IAApC,EAA0C;aACjC,IAAIu7B,IAAJ,CAASv7B,IAAT,EAAeu5B,KAAf,CAAP;KADF,MAEO,IAAIv5B,IAAI,CAAC,CAAD,CAAJ,KAAY,IAAZ,IAAoBA,IAAI,CAACvE,QAAL,CAAc,OAAd,EAAuB,CAAvB,EAA0B,CAA1B,MAAiC,KAAzD,EAAgE;aAC9D,IAAIygC,QAAJ,CAAQl8B,IAAR,EAAcu5B,KAAd,CAAP;KADK,MAEA;YACC,IAAI79B,KAAJ,CAAU,uBAAV,CAAN;;;;;;AC/BN,kBAAe;EACbuiC,UAAU,GAAG;SACNC,cAAL,GAAsB,EAAtB;WACQ,KAAKC,WAAL,GAAmB,CAA3B;GAHW;;EAMblC,KAAK,CAACtI,GAAD,EAAMhrB,CAAN,EAAS0a,CAAT,EAAYxnB,OAAO,GAAG,EAAtB,EAA0B;QACzBuiC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBrC,KAAhB,EAAuBsC,EAAvB,EAA2Bl9B,IAA3B,EAAiCm9B,KAAjC;;QACI,OAAO71B,CAAP,KAAa,QAAjB,EAA2B;MACzB9M,OAAO,GAAG8M,CAAV;MACAA,CAAC,GAAG,IAAJ;;;IAGFA,CAAC,GAAG,CAACtH,IAAI,GAAGsH,CAAC,IAAI,IAAL,GAAYA,CAAZ,GAAgB9M,OAAO,CAAC8M,CAAhC,KAAsC,IAAtC,GAA6CtH,IAA7C,GAAoD,KAAKsH,CAA7D;IACA0a,CAAC,GAAG,CAACmb,KAAK,GAAGnb,CAAC,IAAI,IAAL,GAAYA,CAAZ,GAAgBxnB,OAAO,CAACwnB,CAAjC,KAAuC,IAAvC,GAA8Cmb,KAA9C,GAAsD,KAAKnb,CAA/D;;QAEI,OAAOsQ,GAAP,KAAe,QAAnB,EAA6B;MAC3BsI,KAAK,GAAG,KAAKiC,cAAL,CAAoBvK,GAApB,CAAR;;;QAGE,CAACsI,KAAL,EAAY;UACNtI,GAAG,CAAC3uB,KAAJ,IAAa2uB,GAAG,CAAC1uB,MAArB,EAA6B;QAC3Bg3B,KAAK,GAAGtI,GAAR;OADF,MAEO;QACLsI,KAAK,GAAG,KAAKwC,SAAL,CAAe9K,GAAf,CAAR;;;;QAIA,CAACsI,KAAK,CAACxtB,GAAX,EAAgB;MACdwtB,KAAK,CAAC3pB,KAAN,CAAY,IAAZ;;;QAGE,KAAKsB,IAAL,CAAU3N,QAAV,CAAmBg2B,KAAK,CAAC1C,KAAzB,KAAmC,IAAvC,EAA6C;WACtC3lB,IAAL,CAAU3N,QAAV,CAAmBg2B,KAAK,CAAC1C,KAAzB,IAAkC0C,KAAK,CAACxtB,GAAxC;;;QAGEoY,CAAC,GAAGhrB,OAAO,CAACmJ,KAAR,IAAiBi3B,KAAK,CAACj3B,KAA/B;QACI0c,CAAC,GAAG7lB,OAAO,CAACoJ,MAAR,IAAkBg3B,KAAK,CAACh3B,MAAhC;;QAEIpJ,OAAO,CAACmJ,KAAR,IAAiB,CAACnJ,OAAO,CAACoJ,MAA9B,EAAsC;YAC9By5B,EAAE,GAAG7X,CAAC,GAAGoV,KAAK,CAACj3B,KAArB;MACA6hB,CAAC,GAAGoV,KAAK,CAACj3B,KAAN,GAAc05B,EAAlB;MACAhd,CAAC,GAAGua,KAAK,CAACh3B,MAAN,GAAey5B,EAAnB;KAHF,MAIO,IAAI7iC,OAAO,CAACoJ,MAAR,IAAkB,CAACpJ,OAAO,CAACmJ,KAA/B,EAAsC;YACrC25B,EAAE,GAAGjd,CAAC,GAAGua,KAAK,CAACh3B,MAArB;MACA4hB,CAAC,GAAGoV,KAAK,CAACj3B,KAAN,GAAc25B,EAAlB;MACAjd,CAAC,GAAGua,KAAK,CAACh3B,MAAN,GAAe05B,EAAnB;KAHK,MAIA,IAAI9iC,OAAO,CAACsvB,KAAZ,EAAmB;MACxBtE,CAAC,GAAGoV,KAAK,CAACj3B,KAAN,GAAcnJ,OAAO,CAACsvB,KAA1B;MACAzJ,CAAC,GAAGua,KAAK,CAACh3B,MAAN,GAAepJ,OAAO,CAACsvB,KAA3B;KAFK,MAGA,IAAItvB,OAAO,CAAC+iC,GAAZ,EAAiB;OACrBN,EAAD,EAAKF,EAAL,IAAWviC,OAAO,CAAC+iC,GAAnB;MACAP,EAAE,GAAGC,EAAE,GAAGF,EAAV;MACAG,EAAE,GAAGtC,KAAK,CAACj3B,KAAN,GAAci3B,KAAK,CAACh3B,MAAzB;;UACIs5B,EAAE,GAAGF,EAAT,EAAa;QACXxX,CAAC,GAAGyX,EAAJ;QACA5c,CAAC,GAAG4c,EAAE,GAAGC,EAAT;OAFF,MAGO;QACL7c,CAAC,GAAG0c,EAAJ;QACAvX,CAAC,GAAGuX,EAAE,GAAGG,EAAT;;KATG,MAWA,IAAI1iC,OAAO,CAACgjC,KAAZ,EAAmB;OACvBP,EAAD,EAAKF,EAAL,IAAWviC,OAAO,CAACgjC,KAAnB;MACAR,EAAE,GAAGC,EAAE,GAAGF,EAAV;MACAG,EAAE,GAAGtC,KAAK,CAACj3B,KAAN,GAAci3B,KAAK,CAACh3B,MAAzB;;UACIs5B,EAAE,GAAGF,EAAT,EAAa;QACX3c,CAAC,GAAG0c,EAAJ;QACAvX,CAAC,GAAGuX,EAAE,GAAGG,EAAT;OAFF,MAGO;QACL1X,CAAC,GAAGyX,EAAJ;QACA5c,CAAC,GAAG4c,EAAE,GAAGC,EAAT;;;;QAIA1iC,OAAO,CAAC+iC,GAAR,IAAe/iC,OAAO,CAACgjC,KAA3B,EAAkC;UAC5BhjC,OAAO,CAAC65B,KAAR,KAAkB,QAAtB,EAAgC;QAC9B/sB,CAAC,GAAGA,CAAC,GAAG21B,EAAE,GAAG,CAAT,GAAazX,CAAC,GAAG,CAArB;OADF,MAEO,IAAIhrB,OAAO,CAAC65B,KAAR,KAAkB,OAAtB,EAA+B;QACpC/sB,CAAC,GAAGA,CAAC,GAAG21B,EAAJ,GAASzX,CAAb;;;UAGEhrB,OAAO,CAACijC,MAAR,KAAmB,QAAvB,EAAiC;QAC/Bzb,CAAC,GAAGA,CAAC,GAAG+a,EAAE,GAAG,CAAT,GAAa1c,CAAC,GAAG,CAArB;OADF,MAEO,IAAI7lB,OAAO,CAACijC,MAAR,KAAmB,QAAvB,EAAiC;QACtCzb,CAAC,GAAGA,CAAC,GAAG+a,EAAJ,GAAS1c,CAAb;;KA9EyB;;;QAmFzB7lB,OAAO,CAACy+B,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAU3xB,CAAV,EAAa0a,CAAb,EAAgBwD,CAAhB,EAAmBnF,CAAnB,EAAsB7lB,OAAO,CAACy+B,IAA9B;;;QAEEz+B,OAAO,CAAC0+B,IAAR,IAAgB,IAApB,EAA0B;WACnBA,IAAL,CAAU5xB,CAAV,EAAa0a,CAAb,EAAgBwD,CAAhB,EAAmBnF,CAAnB,EAAsB7lB,OAAO,CAAC0+B,IAA9B;;;QAEE1+B,OAAO,CAAC2+B,WAAR,IAAuB,IAA3B,EAAiC;WAC1BC,mBAAL,CAAyB5+B,OAAO,CAAC2+B,WAAjC,EAA8C,KAA9C,EAAqD7xB,CAArD,EAAwD0a,CAAxD,EAA2D,IAA3D;KA1F2B;;;QA8FzB,KAAKA,CAAL,KAAWA,CAAf,EAAkB;WACXA,CAAL,IAAU3B,CAAV;;;SAGG+E,IAAL;SACKlV,SAAL,CAAesV,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAACnF,CAAzB,EAA4B/Y,CAA5B,EAA+B0a,CAAC,GAAG3B,CAAnC;SACKpM,UAAL,CAAiB,IAAG2mB,KAAK,CAAC1C,KAAM,KAAhC;SACK7S,OAAL;WAEO,IAAP;GA7GW;;EAgHb+X,SAAS,CAAC9K,GAAD,EAAM;QACTsI,KAAJ;;QACI,OAAOtI,GAAP,KAAe,QAAnB,EAA6B;MAC3BsI,KAAK,GAAG,KAAKiC,cAAL,CAAoBvK,GAApB,CAAR;;;QAGE,CAACsI,KAAL,EAAY;MACVA,KAAK,GAAG8B,QAAQ,CAACrS,IAAT,CAAciI,GAAd,EAAoB,IAAG,EAAE,KAAKwK,WAAY,EAA1C,CAAR;;UACI,OAAOxK,GAAP,KAAe,QAAnB,EAA6B;aACtBuK,cAAL,CAAoBvK,GAApB,IAA2BsI,KAA3B;;;;WAIGA,KAAP;;;CA7HJ;;ACFA,uBAAe;EACb8C,QAAQ,CAACp2B,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa7lB,OAAb,EAAsB;IAC5BA,OAAO,CAAC0J,IAAR,GAAe,OAAf;IACA1J,OAAO,CAACmjC,IAAR,GAAe,KAAKC,YAAL,CAAkBt2B,CAAlB,EAAqB0a,CAArB,EAAwBwD,CAAxB,EAA2BnF,CAA3B,CAAf;IACA7lB,OAAO,CAACqjC,MAAR,GAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAjB;;QAEIrjC,OAAO,CAACiY,OAAR,KAAoB,MAApB,IAA8B,OAAOjY,OAAO,CAACsjC,CAAf,KAAqB,WAAvD,EAAoE;MAClEtjC,OAAO,CAACsjC,CAAR,GAAY,KAAK,CAAjB,CADkE;;;QAIhEtjC,OAAO,CAACiY,OAAR,KAAoB,MAAxB,EAAgC;UAC1BjY,OAAO,CAAC2lB,CAAR,IAAa,IAAjB,EAAuB;QACrB3lB,OAAO,CAAC2lB,CAAR,GAAY,KAAK5P,eAAL,CAAqB/V,OAAO,CAAC6V,KAAR,IAAiB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAtC,CAAZ;;KAXwB;;;WAcrB7V,OAAO,CAAC6V,KAAf;;QAEI,OAAO7V,OAAO,CAACujC,IAAf,KAAwB,QAA5B,EAAsC;MACpCvjC,OAAO,CAACujC,IAAR,GAAe,IAAIlhC,MAAJ,CAAWrC,OAAO,CAACujC,IAAnB,CAAf;KAjB0B;;;SAqBvB,IAAInjC,GAAT,IAAgBJ,OAAhB,EAAyB;YACjBK,GAAG,GAAGL,OAAO,CAACI,GAAD,CAAnB;MACAJ,OAAO,CAACI,GAAG,CAAC,CAAD,CAAH,CAAO8I,WAAP,KAAuB9I,GAAG,CAACuB,KAAJ,CAAU,CAAV,CAAxB,CAAP,GAA+CtB,GAA/C;;;UAGIiJ,GAAG,GAAG,KAAKA,GAAL,CAAStJ,OAAT,CAAZ;SACK+X,IAAL,CAAUrN,WAAV,CAAsBxJ,IAAtB,CAA2BoI,GAA3B;IACAA,GAAG,CAACpH,GAAJ;WACO,IAAP;GA9BW;;EAiCbshC,IAAI,CAAC12B,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAaoK,QAAb,EAAuBjwB,OAAO,GAAG,EAAjC,EAAqC;IACvCA,OAAO,CAACiY,OAAR,GAAkB,MAAlB;IACAjY,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,CAAW4tB,QAAX,CAAnB;IACAjwB,OAAO,CAACyjC,IAAR,GAAe,SAAf;;QACIzjC,OAAO,CAAC6V,KAAR,IAAiB,IAArB,EAA2B;MACzB7V,OAAO,CAAC6V,KAAR,GAAgB,CAAC,GAAD,EAAM,GAAN,EAAW,EAAX,CAAhB;;;WAEK,KAAKqtB,QAAL,CAAcp2B,CAAd,EAAiB0a,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B7lB,OAA1B,CAAP;GAxCW;;EA2Cb0+B,IAAI,CAAC5xB,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAalK,IAAb,EAAmB3b,OAAO,GAAG,EAA7B,EAAiC;IACnCA,OAAO,CAACiY,OAAR,GAAkB,MAAlB;IACAjY,OAAO,CAAC0lB,CAAR,GAAY,KAAKpc,GAAL,CAAS;MACnB+O,CAAC,EAAE,MADgB;MAEnBqrB,CAAC,EAAE,IAAIrhC,MAAJ,CAAWsZ,IAAX;KAFO,CAAZ;IAIA3b,OAAO,CAAC0lB,CAAR,CAAUxjB,GAAV;WACO,KAAKghC,QAAL,CAAcp2B,CAAd,EAAiB0a,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B7lB,OAA1B,CAAP;GAlDW;;EAqDby+B,IAAI,CAAC3xB,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa8d,GAAb,EAAkB3jC,OAAO,GAAG,EAA5B,EAAgC;IAClCA,OAAO,CAACiY,OAAR,GAAkB,MAAlB;;QAEI,OAAO0rB,GAAP,KAAe,QAAnB,EAA6B;;YAErBC,KAAK,GAAG,KAAKh6B,KAAL,CAAWzF,IAAX,CAAgB0F,KAAhB,CAAsB1F,IAApC;;UACIw/B,GAAG,IAAI,CAAP,IAAYA,GAAG,GAAGC,KAAK,CAACC,IAAN,CAAW9iC,MAAjC,EAAyC;QACvCf,OAAO,CAAC0lB,CAAR,GAAY,KAAKpc,GAAL,CAAS;UACnB+O,CAAC,EAAE,MADgB;UAEnBqrB,CAAC,EAAE,CAACE,KAAK,CAACC,IAAN,CAAWF,GAAX,CAAD,EAAkB,KAAlB,EAAyB,IAAzB,EAA+B,IAA/B,EAAqC,IAArC;SAFO,CAAZ;QAIA3jC,OAAO,CAAC0lB,CAAR,CAAUxjB,GAAV;OALF,MAMO;cACC,IAAIrC,KAAJ,CAAW,4BAA2B8jC,GAAI,EAA1C,CAAN;;KAVJ,MAYO;;MAEL3jC,OAAO,CAAC0lB,CAAR,GAAY,KAAKpc,GAAL,CAAS;QACnB+O,CAAC,EAAE,KADgB;QAEnByrB,GAAG,EAAE,IAAIzhC,MAAJ,CAAWshC,GAAX;OAFK,CAAZ;MAIA3jC,OAAO,CAAC0lB,CAAR,CAAUxjB,GAAV;;;WAGK,KAAKghC,QAAL,CAAcp2B,CAAd,EAAiB0a,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B7lB,OAA1B,CAAP;GA7EW;;EAgFb+jC,OAAO,CAACj3B,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa7lB,OAAO,GAAG,EAAvB,EAA2B;UAC1B,CAAC2Z,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,IAAmB,KAAKspB,YAAL,CAAkBt2B,CAAlB,EAAqB0a,CAArB,EAAwBwD,CAAxB,EAA2BnF,CAA3B,CAAzB;;IACA7lB,OAAO,CAACgkC,UAAR,GAAqB,CAACrqB,EAAD,EAAKG,EAAL,EAASD,EAAT,EAAaC,EAAb,EAAiBH,EAAjB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6BD,EAA7B,CAArB;IACA5Z,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,EAAnB;WACO,KAAK6gC,QAAL,CAAcp2B,CAAd,EAAiB0a,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B7lB,OAA1B,CAAP;GApFW;;EAuFbikC,SAAS,CAACn3B,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa7lB,OAAO,GAAG,EAAvB,EAA2B;IAClCA,OAAO,CAACiY,OAAR,GAAkB,WAAlB;;QACIjY,OAAO,CAAC6V,KAAR,IAAiB,IAArB,EAA2B;MACzB7V,OAAO,CAAC6V,KAAR,GAAgB,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAAhB;;;WAEK,KAAKkuB,OAAL,CAAaj3B,CAAb,EAAgB0a,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAyB7lB,OAAzB,CAAP;GA5FW;;EA+Fb6+B,SAAS,CAAC/xB,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa7lB,OAAO,GAAG,EAAvB,EAA2B;IAClCA,OAAO,CAACiY,OAAR,GAAkB,WAAlB;WACO,KAAK8rB,OAAL,CAAaj3B,CAAb,EAAgB0a,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAyB7lB,OAAzB,CAAP;GAjGW;;EAoGb++B,MAAM,CAACjyB,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa7lB,OAAO,GAAG,EAAvB,EAA2B;IAC/BA,OAAO,CAACiY,OAAR,GAAkB,WAAlB;WACO,KAAK8rB,OAAL,CAAaj3B,CAAb,EAAgB0a,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAyB7lB,OAAzB,CAAP;GAtGW;;EAyGbkkC,cAAc,CAACvqB,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiB9Z,OAAO,GAAG,EAA3B,EAA+B;IAC3CA,OAAO,CAACiY,OAAR,GAAkB,MAAlB;IACAjY,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,EAAnB;IACArC,OAAO,CAAC8lB,CAAR,GAAY,CAACnM,EAAD,EAAK,KAAK5B,IAAL,CAAU3O,MAAV,GAAmBwQ,EAAxB,EAA4BC,EAA5B,EAAgC,KAAK9B,IAAL,CAAU3O,MAAV,GAAmB0Q,EAAnD,CAAZ;WACO,KAAKopB,QAAL,CAAcvpB,EAAd,EAAkBC,EAAlB,EAAsBC,EAAtB,EAA0BC,EAA1B,EAA8B9Z,OAA9B,CAAP;GA7GW;;EAgHbmkC,cAAc,CAACr3B,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa7lB,OAAO,GAAG,EAAvB,EAA2B;IACvCA,OAAO,CAACiY,OAAR,GAAkB,QAAlB;IACAjY,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,EAAnB;WACO,KAAK6gC,QAAL,CAAcp2B,CAAd,EAAiB0a,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B7lB,OAA1B,CAAP;GAnHW;;EAsHbokC,iBAAiB,CAACt3B,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa7lB,OAAO,GAAG,EAAvB,EAA2B;IAC1CA,OAAO,CAACiY,OAAR,GAAkB,QAAlB;IACAjY,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,EAAnB;WACO,KAAK6gC,QAAL,CAAcp2B,CAAd,EAAiB0a,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B7lB,OAA1B,CAAP;GAzHW;;EA4HbqkC,cAAc,CAACv3B,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAaqL,IAAb,EAAmBlxB,OAAO,GAAG,EAA7B,EAAiC;IAC7CA,OAAO,CAACiY,OAAR,GAAkB,UAAlB;IACAjY,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,CAAW6uB,IAAX,CAAnB;IACAlxB,OAAO,CAACskC,EAAR,GAAa,IAAIjiC,MAAJ,EAAb;WACO,KAAK6gC,QAAL,CAAcp2B,CAAd,EAAiB0a,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B7lB,OAA1B,CAAP;GAhIW;;EAmIbukC,cAAc,CAACz3B,CAAD,EAAI0a,CAAJ,EAAOwD,CAAP,EAAUnF,CAAV,EAAa2e,IAAI,GAAG,EAApB,EAAwBxkC,OAAO,GAAG,EAAlC,EAAsC;;UAE5CykC,QAAQ,GAAG,KAAKD,IAAL,CACfA,IAAI,CAAC1M,GADU,EAEft3B,MAAM,CAAC29B,MAAP,CAAc;MAAEuG,MAAM,EAAE;KAAxB,EAAgCF,IAAhC,CAFe,CAAjB;IAKAxkC,OAAO,CAACiY,OAAR,GAAkB,gBAAlB;IACAjY,OAAO,CAAC2kC,EAAR,GAAaF,QAAb,CARkD;;QAW9CzkC,OAAO,CAAC+J,QAAZ,EAAsB;MACpB/J,OAAO,CAAC+J,QAAR,GAAmB,IAAI1H,MAAJ,CAAWrC,OAAO,CAAC+J,QAAnB,CAAnB;KADF,MAEO,IAAI06B,QAAQ,CAACtgC,IAAT,CAAcygC,IAAlB,EAAwB;MAC7B5kC,OAAO,CAAC+J,QAAR,GAAmB06B,QAAQ,CAACtgC,IAAT,CAAcygC,IAAjC;;;WAGK,KAAK1B,QAAL,CAAcp2B,CAAd,EAAiB0a,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0B7lB,OAA1B,CAAP;GApJW;;EAuJbojC,YAAY,CAACzpB,EAAD,EAAKC,EAAL,EAASoR,CAAT,EAAYnF,CAAZ,EAAe;;QAErB/L,EAAE,GAAGF,EAAT;IACAA,EAAE,IAAIiM,CAAN,CAHyB;;QAMrBhM,EAAE,GAAGF,EAAE,GAAGqR,CAAd,CANyB;;UASnB,CAAC9R,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiBC,EAAjB,EAAqBC,EAArB,IAA2B,KAAKC,IAAtC;IACAG,EAAE,GAAGT,EAAE,GAAGS,EAAL,GAAUP,EAAE,GAAGQ,EAAf,GAAoBN,EAAzB;IACAM,EAAE,GAAGT,EAAE,GAAGQ,EAAL,GAAUN,EAAE,GAAGO,EAAf,GAAoBL,EAAzB;IACAM,EAAE,GAAGX,EAAE,GAAGW,EAAL,GAAUT,EAAE,GAAGU,EAAf,GAAoBR,EAAzB;IACAQ,EAAE,GAAGX,EAAE,GAAGU,EAAL,GAAUR,EAAE,GAAGS,EAAf,GAAoBP,EAAzB;WAEO,CAACI,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,CAAP;;;CAtKJ;;ACAA,MAAM+qB,UAAN,CAAiB;EACf9kC,WAAW,CAACkE,QAAD,EAAW6gC,MAAX,EAAmBC,KAAnB,EAA0BC,IAA1B,EAAgChlC,OAAO,GAAG;IAAEilC,QAAQ,EAAE;GAAtD,EAA+D;SACnEhhC,QAAL,GAAgBA,QAAhB;SACKjE,OAAL,GAAeA,OAAf;SACKklC,WAAL,GAAmB,EAAnB;;QAEIF,IAAI,KAAK,IAAb,EAAmB;WACZE,WAAL,CAAiB,MAAjB,IAA2B,CAACF,IAAI,CAACv7B,UAAN,EAAkB,KAAlB,CAA3B;;;QAGEq7B,MAAM,KAAK,IAAf,EAAqB;WACdI,WAAL,CAAiB,QAAjB,IAA6BJ,MAA7B;;;QAGEC,KAAK,KAAK,IAAd,EAAoB;WACbG,WAAL,CAAiB,OAAjB,IAA4B,IAAI7iC,MAAJ,CAAW0iC,KAAX,CAA5B;;;SAGGt7B,UAAL,GAAkB,KAAKxF,QAAL,CAAcqF,GAAd,CAAkB,KAAK47B,WAAvB,CAAlB;SACKC,QAAL,GAAgB,EAAhB;;;EAGFC,OAAO,CAACL,KAAD,EAAQ/kC,OAAO,GAAG;IAAEilC,QAAQ,EAAE;GAA9B,EAAuC;UACtC/a,MAAM,GAAG,IAAI2a,UAAJ,CACb,KAAK5gC,QADQ,EAEb,KAAKwF,UAFQ,EAGbs7B,KAHa,EAIb,KAAK9gC,QAAL,CAAc8T,IAJD,EAKb/X,OALa,CAAf;SAOKmlC,QAAL,CAAcjkC,IAAd,CAAmBgpB,MAAnB;WAEOA,MAAP;;;EAGFmb,UAAU,GAAG;QACP,KAAKF,QAAL,CAAcpkC,MAAd,GAAuB,CAA3B,EAA8B;UACxB,KAAKf,OAAL,CAAailC,QAAjB,EAA2B;aACpBC,WAAL,CAAiBI,KAAjB,GAAyB,KAAKH,QAAL,CAAcpkC,MAAvC;;;YAGIC,KAAK,GAAG,KAAKmkC,QAAL,CAAc,CAAd,CAAd;YACElkC,IAAI,GAAG,KAAKkkC,QAAL,CAAc,KAAKA,QAAL,CAAcpkC,MAAd,GAAuB,CAArC,CADT;WAEKmkC,WAAL,CAAiBK,KAAjB,GAAyBvkC,KAAK,CAACyI,UAA/B;WACKy7B,WAAL,CAAiBM,IAAjB,GAAwBvkC,IAAI,CAACwI,UAA7B;;WAEK,IAAIxH,CAAC,GAAG,CAAR,EAAWmvB,GAAG,GAAG,KAAK+T,QAAL,CAAcpkC,MAApC,EAA4CkB,CAAC,GAAGmvB,GAAhD,EAAqDnvB,CAAC,EAAtD,EAA0D;cAClDwjC,KAAK,GAAG,KAAKN,QAAL,CAAcljC,CAAd,CAAd;;YACIA,CAAC,GAAG,CAAR,EAAW;UACTwjC,KAAK,CAACP,WAAN,CAAkBQ,IAAlB,GAAyB,KAAKP,QAAL,CAAcljC,CAAC,GAAG,CAAlB,EAAqBwH,UAA9C;;;YAEExH,CAAC,GAAG,KAAKkjC,QAAL,CAAcpkC,MAAd,GAAuB,CAA/B,EAAkC;UAChC0kC,KAAK,CAACP,WAAN,CAAkBS,IAAlB,GAAyB,KAAKR,QAAL,CAAcljC,CAAC,GAAG,CAAlB,EAAqBwH,UAA9C;;;QAEFg8B,KAAK,CAACJ,UAAN;;;;WAIG,KAAK57B,UAAL,CAAgBvH,GAAhB,EAAP;;;;;ACxDJ,mBAAe;EACb0jC,WAAW,GAAG;WACJ,KAAKC,OAAL,GAAe,IAAIhB,UAAJ,CAAe,IAAf,EAAqB,IAArB,EAA2B,IAA3B,EAAiC,IAAjC,CAAvB;GAFW;;EAKbQ,UAAU,GAAG;SACNQ,OAAL,CAAaR,UAAb;;QACI,KAAKQ,OAAL,CAAaV,QAAb,CAAsBpkC,MAAtB,GAA+B,CAAnC,EAAsC;WAC/B6I,KAAL,CAAWzF,IAAX,CAAgB2hC,QAAhB,GAA2B,KAAKD,OAAL,CAAap8B,UAAxC;aACQ,KAAKG,KAAL,CAAWzF,IAAX,CAAgB4hC,QAAhB,GAA2B,aAAnC;;;;CATN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACFA;;;;AAKA,MAAMC,mBAAN,CAA0B;EACxBjmC,WAAW,CAACkmC,OAAD,EAAUC,IAAV,EAAgB;SACpBC,IAAL,GAAY,CAAC;MAAEF,OAAF;MAAWC;KAAZ,CAAZ;;;EAGFhlC,IAAI,CAACklC,aAAD,EAAgB;IAClBA,aAAa,CAACD,IAAd,CAAmBE,OAAnB,CAA4B/8B,GAAD,IAAS,KAAK68B,IAAL,CAAUjlC,IAAV,CAAeoI,GAAf,CAApC;;;;;ACXJ;;;;AAKA;AAEA,MAAMg9B,mBAAN,CAA0B;EACxBvmC,WAAW,CAACkE,QAAD,EAAWsiC,IAAX,EAAiBvmC,OAAO,GAAG,EAA3B,EAA+BmlC,QAAQ,GAAG,IAA1C,EAAgD;SACpDlhC,QAAL,GAAgBA,QAAhB;SAEKuiC,SAAL,GAAiB,KAAjB;SACKC,MAAL,GAAc,KAAd;SACKC,QAAL,GAAgB,KAAhB;SACKj9B,UAAL,GAAkBxF,QAAQ,CAACqF,GAAT,CAAa;;MAE7B+O,CAAC,EAAEkuB;KAFa,CAAlB;UAKMpiC,IAAI,GAAG,KAAKsF,UAAL,CAAgBtF,IAA7B;;QAEIzC,KAAK,CAAC6B,OAAN,CAAcvD,OAAd,KAA0B,KAAK2mC,aAAL,CAAmB3mC,OAAnB,CAA9B,EAA2D;MACzDmlC,QAAQ,GAAGnlC,OAAX;MACAA,OAAO,GAAG,EAAV;;;QAGE,OAAOA,OAAO,CAAC+kC,KAAf,KAAyB,WAA7B,EAA0C;MACxC5gC,IAAI,CAACgiB,CAAL,GAAS,IAAI9jB,MAAJ,CAAWrC,OAAO,CAAC+kC,KAAnB,CAAT;;;QAEE,OAAO/kC,OAAO,CAAC4mC,IAAf,KAAwB,WAA5B,EAAyC;MACvCziC,IAAI,CAAC0iC,IAAL,GAAY,IAAIxkC,MAAJ,CAAWrC,OAAO,CAAC4mC,IAAnB,CAAZ;;;QAEE,OAAO5mC,OAAO,CAAC8mC,GAAf,KAAuB,WAA3B,EAAwC;MACtC3iC,IAAI,CAAC4iC,GAAL,GAAW,IAAI1kC,MAAJ,CAAWrC,OAAO,CAAC8mC,GAAnB,CAAX;;;QAEE,OAAO9mC,OAAO,CAACilC,QAAf,KAA4B,WAAhC,EAA6C;MAC3C9gC,IAAI,CAAC6iC,CAAL,GAAS,IAAI3kC,MAAJ,CAAWrC,OAAO,CAACilC,QAAnB,CAAT;;;QAEE,OAAOjlC,OAAO,CAACinC,MAAf,KAA0B,WAA9B,EAA2C;MACzC9iC,IAAI,CAAC+iC,UAAL,GAAkB,IAAI7kC,MAAJ,CAAWrC,OAAO,CAACinC,MAAnB,CAAlB;;;SAGGE,SAAL,GAAiB,EAAjB;;QAEIhC,QAAJ,EAAc;UACR,CAACzjC,KAAK,CAAC6B,OAAN,CAAc4hC,QAAd,CAAL,EAA8B;QAC5BA,QAAQ,GAAG,CAACA,QAAD,CAAX;;;MAEFA,QAAQ,CAACkB,OAAT,CAAkBZ,KAAD,IAAW,KAAKtlC,GAAL,CAASslC,KAAT,CAA5B;WACKvjC,GAAL;;;;EAIJ/B,GAAG,CAACslC,KAAD,EAAQ;QACL,KAAKgB,MAAT,EAAiB;YACT,IAAI5mC,KAAJ,CAAW,qDAAX,CAAN;;;QAGE,CAAC,KAAK8mC,aAAL,CAAmBlB,KAAnB,CAAL,EAAgC;YACxB,IAAI5lC,KAAJ,CAAW,iCAAX,CAAN;;;QAGE4lC,KAAK,YAAYa,mBAArB,EAA0C;MACxCb,KAAK,CAAC2B,SAAN,CAAgB,KAAK39B,UAArB;;UACI,KAAK+8B,SAAT,EAAoB;QAClBf,KAAK,CAAC4B,WAAN;;;;QAIA5B,KAAK,YAAYO,mBAArB,EAA0C;WACnCsB,uBAAL,CAA6B7B,KAA7B;;;QAGE,OAAOA,KAAP,KAAiB,UAAjB,IAA+B,KAAKe,SAAxC,EAAmD;;MAEjDf,KAAK,GAAG,KAAK8B,kBAAL,CAAwB9B,KAAxB,CAAR;;;SAGG0B,SAAL,CAAejmC,IAAf,CAAoBukC,KAApB;;WAEO,IAAP;;;EAGF6B,uBAAuB,CAACj+B,OAAD,EAAU;IAC/BA,OAAO,CAAC88B,IAAR,CAAaE,OAAb,CAAqB,CAAC;MAAEJ,OAAF;MAAWC;KAAZ,KAAuB;YACpCsB,iBAAiB,GAAG,KAAKvjC,QAAL,CAAcwjC,mBAAd,GACvBnnC,GADuB,CACnB2lC,OAAO,CAAC9hC,IAAR,CAAa0G,aADM,CAA1B;MAEA28B,iBAAiB,CAACtB,IAAD,CAAjB,GAA0B,KAAKz8B,UAA/B;KAHF;;;EAOF29B,SAAS,CAACM,SAAD,EAAY;QACf,KAAKj+B,UAAL,CAAgBtF,IAAhB,CAAqBsN,CAAzB,EAA4B;YACpB,IAAI5R,KAAJ,CAAW,iDAAX,CAAN;;;SAGG4J,UAAL,CAAgBtF,IAAhB,CAAqBsN,CAArB,GAAyBi2B,SAAzB;;SAEKC,MAAL;;;EAGFN,WAAW,GAAG;QACR,KAAKb,SAAT,EAAoB;;;;SAIfW,SAAL,CAAed,OAAf,CAAuB,CAACZ,KAAD,EAAQ1wB,KAAR,KAAkB;UACnC0wB,KAAK,YAAYa,mBAArB,EAA0C;QACxCb,KAAK,CAAC4B,WAAN;;;UAEE,OAAO5B,KAAP,KAAiB,UAArB,EAAiC;aAC1B0B,SAAL,CAAepyB,KAAf,IAAwB,KAAKwyB,kBAAL,CAAwB9B,KAAxB,CAAxB;;KALJ;;SASKe,SAAL,GAAiB,IAAjB;;SAEKmB,MAAL;;;EAGFzlC,GAAG,GAAG;QACA,KAAKukC,MAAT,EAAiB;;;;SAIZU,SAAL,CACG35B,MADH,CACWi4B,KAAD,IAAWA,KAAK,YAAYa,mBADtC,EAEGD,OAFH,CAEYZ,KAAD,IAAWA,KAAK,CAACvjC,GAAN,EAFtB;;SAIKukC,MAAL,GAAc,IAAd;;SAEKkB,MAAL;;;EAGFhB,aAAa,CAAClB,KAAD,EAAQ;WACZA,KAAK,YAAYa,mBAAjB,IACHb,KAAK,YAAYO,mBADd,IAEH,OAAOP,KAAP,KAAiB,UAFrB;;;EAKF8B,kBAAkB,CAACK,OAAD,EAAU;UACpBv+B,OAAO,GAAG,KAAKpF,QAAL,CAAcs4B,oBAAd,CAAmC,KAAK9yB,UAAL,CAAgBtF,IAAhB,CAAqBkU,CAAxD,CAAhB;IACAuvB,OAAO;SACF3jC,QAAL,CAAc4jC,gBAAd;;SAEKP,uBAAL,CAA6Bj+B,OAA7B;;WAEOA,OAAP;;;EAGFy+B,YAAY,GAAG;QACT,CAAC,KAAKr+B,UAAL,CAAgBtF,IAAhB,CAAqBsN,CAAtB,IAA2B,CAAC,KAAKg1B,MAArC,EAA6C;aACpC,KAAP;;;WAGK,KAAKU,SAAL,CAAerb,KAAf,CAAsB2Z,KAAD,IAAW;UACjC,OAAOA,KAAP,KAAiB,UAArB,EAAiC;eACxB,KAAP;;;UAEEA,KAAK,YAAYa,mBAArB,EAA0C;eACjCb,KAAK,CAACqC,YAAN,EAAP;;;aAEK,IAAP;KAPK,CAAP;;;EAWFH,MAAM,GAAG;QACH,KAAKjB,QAAL,IAAiB,CAAC,KAAKoB,YAAL,EAAtB,EAA2C;;;;SAItCr+B,UAAL,CAAgBtF,IAAhB,CAAqB4jC,CAArB,GAAyB,EAAzB;;SAEKZ,SAAL,CAAed,OAAf,CAAwBZ,KAAD,IAAW,KAAKuC,WAAL,CAAiBvC,KAAjB,CAAlC;;SAEKh8B,UAAL,CAAgBvH,GAAhB,GATO;;;;SAcFilC,SAAL,GAAiB,EAAjB;SACK19B,UAAL,CAAgBtF,IAAhB,CAAqB4jC,CAArB,GAAyB,IAAzB;SAEKrB,QAAL,GAAgB,IAAhB;;;EAGFsB,WAAW,CAACvC,KAAD,EAAQ;QACbA,KAAK,YAAYa,mBAArB,EAA0C;WACnC78B,UAAL,CAAgBtF,IAAhB,CAAqB4jC,CAArB,CAAuB7mC,IAAvB,CAA4BukC,KAAK,CAACh8B,UAAlC;;;QAGEg8B,KAAK,YAAYO,mBAArB,EAA0C;MACxCP,KAAK,CAACU,IAAN,CAAWE,OAAX,CAAmB,CAAC;QAAEJ,OAAF;QAAWC;OAAZ,KAAuB;YACpC,CAAC,KAAKz8B,UAAL,CAAgBtF,IAAhB,CAAqB8jC,EAA1B,EAA8B;eACvBx+B,UAAL,CAAgBtF,IAAhB,CAAqB8jC,EAArB,GAA0BhC,OAA1B;;;YAGE,KAAKx8B,UAAL,CAAgBtF,IAAhB,CAAqB8jC,EAArB,KAA4BhC,OAAhC,EAAyC;eAClCx8B,UAAL,CAAgBtF,IAAhB,CAAqB4jC,CAArB,CAAuB7mC,IAAvB,CAA4BglC,IAA5B;SADF,MAEO;eACAz8B,UAAL,CAAgBtF,IAAhB,CAAqB4jC,CAArB,CAAuB7mC,IAAvB,CAA4B;YAC1BwI,IAAI,EAAE,KADoB;YAE1Bu+B,EAAE,EAAEhC,OAFsB;YAG1BiC,IAAI,EAAEhC;WAHR;;OARJ;;;;;;AChMN;;;AAIA;AAEA,MAAMiC,aAAN,SAA4BroC,OAA5B,CAAoC;EAClCe,YAAY,CAACF,CAAD,EAAIC,CAAJ,EAAO;WACVia,QAAQ,CAACla,CAAD,CAAR,GAAcka,QAAQ,CAACja,CAAD,CAA7B;;;EAGFU,SAAS,GAAG;WACH,MAAP;;;EAGFD,WAAW,CAAC6J,CAAD,EAAI;WACN2P,QAAQ,CAAC3P,CAAD,CAAf;;;;;ACNJ,oBAAe;EAEbk9B,YAAY,CAACpoC,OAAD,EAAU;SACfqoC,cAAL,GAAsB,EAAtB;;QAEIroC,OAAO,CAACsoC,MAAZ,EAAoB;WACbC,qBAAL,GAA6BpkC,IAA7B,CAAkCqkC,MAAlC,GAA2C,IAA3C;WACKC,iBAAL;;GAPS;;EAWbC,WAAW,CAAC7S,GAAD,EAAM71B,OAAO,GAAG,IAAhB,EAAsB;QAC3B61B,GAAG,KAAK,UAAR,IAAuB71B,OAAO,IAAIA,OAAO,CAACkmC,IAA9C,EAAqD;UAC/CyC,OAAO,GAAG,CAAd;WACK5wB,IAAL,CAAU9N,QAAV,CAAmBo8B,OAAnB,CAA4BuC,OAAD,IAAa;YAClCD,OAAO,IAAIC,OAAO,CAACxC,aAAnB,IAAoCwC,OAAO,CAAC/S,GAAR,KAAgB,UAAxD,EAAoE;UAClE8S,OAAO;;OAFX;;aAKOA,OAAO,EAAd,EAAkB;aACXd,gBAAL;;;;QAIA,CAAC7nC,OAAL,EAAc;WACP+X,IAAL,CAAU9N,QAAV,CAAmB/I,IAAnB,CAAwB;QAAE20B;OAA1B;WACKpc,UAAL,CAAiB,IAAGoc,GAAI,MAAxB;aACO,IAAP;;;SAGG9d,IAAL,CAAU9N,QAAV,CAAmB/I,IAAnB,CAAwB;MAAE20B,GAAF;MAAO71B;KAA/B;UAEMyJ,UAAU,GAAG,EAAnB;;QAEI,OAAOzJ,OAAO,CAACkmC,IAAf,KAAwB,WAA5B,EAAyC;MACvCz8B,UAAU,CAACy+B,IAAX,GAAkBloC,OAAO,CAACkmC,IAA1B;;;QAEErQ,GAAG,KAAK,UAAZ,EAAwB;UAClB,OAAO71B,OAAO,CAACumC,IAAf,KAAwB,QAA5B,EAAsC;QACpC98B,UAAU,CAACC,IAAX,GAAkB1J,OAAO,CAACumC,IAA1B;;;UAEE7kC,KAAK,CAAC6B,OAAN,CAAcvD,OAAO,CAACwwB,IAAtB,CAAJ,EAAiC;QAC/B/mB,UAAU,CAAC0O,IAAX,GAAkB,CAACnY,OAAO,CAACwwB,IAAR,CAAa,CAAb,CAAD,EAAkB,KAAKzY,IAAL,CAAU3O,MAAV,GAAmBpJ,OAAO,CAACwwB,IAAR,CAAa,CAAb,CAArC,EAChBxwB,OAAO,CAACwwB,IAAR,CAAa,CAAb,CADgB,EACC,KAAKzY,IAAL,CAAU3O,MAAV,GAAmBpJ,OAAO,CAACwwB,IAAR,CAAa,CAAb,CADpB,CAAlB;;;UAGE9uB,KAAK,CAAC6B,OAAN,CAAcvD,OAAO,CAAC6oC,QAAtB,KACF7oC,OAAO,CAAC6oC,QAAR,CAAiB/c,KAAjB,CAAuBzrB,GAAG,IAAI,OAAOA,GAAP,KAAe,QAA7C,CADF,EAC0D;QACxDoJ,UAAU,CAACq/B,QAAX,GAAsB9oC,OAAO,CAAC6oC,QAA9B;;;;QAGAhT,GAAG,KAAK,MAAZ,EAAoB;UACd71B,OAAO,CAAC4mC,IAAZ,EAAkB;QAChBn9B,UAAU,CAACo9B,IAAX,GAAkB,IAAIxkC,MAAJ,CAAWrC,OAAO,CAAC4mC,IAAnB,CAAlB;;;UAEE5mC,OAAO,CAAC8mC,GAAZ,EAAiB;QACfr9B,UAAU,CAACs9B,GAAX,GAAiB,IAAI1kC,MAAJ,CAAWrC,OAAO,CAAC8mC,GAAnB,CAAjB;;;UAEE9mC,OAAO,CAACilC,QAAZ,EAAsB;QACpBx7B,UAAU,CAACu9B,CAAX,GAAe,IAAI3kC,MAAJ,CAAWrC,OAAO,CAACilC,QAAnB,CAAf;;;UAEEjlC,OAAO,CAACinC,MAAZ,EAAoB;QAClBx9B,UAAU,CAACy9B,UAAX,GAAwB,IAAI7kC,MAAJ,CAAWrC,OAAO,CAACinC,MAAnB,CAAxB;;;;SAICxtB,UAAL,CAAiB,IAAGoc,GAAI,IAAG10B,SAAS,CAACC,OAAV,CAAkBqI,UAAlB,CAA8B,MAAzD;WACO,IAAP;GAlEW;;EAqEb8yB,oBAAoB,CAAC1G,GAAD,EAAM71B,OAAO,GAAG,EAAhB,EAAoB;UAChCwnC,iBAAiB,GAAG,KAAKC,mBAAL,GAA2BnnC,GAA3B,CAA+B,KAAKyX,IAAL,CAAUnN,mBAAzC,CAA1B;UACMs7B,IAAI,GAAGsB,iBAAiB,CAACzmC,MAA/B;IACAymC,iBAAiB,CAACtmC,IAAlB,CAAuB,IAAvB;SAEKwnC,WAAL,CAAiB7S,GAAjB,oCAA2B71B,OAA3B;MAAoCkmC;;UAE9BE,aAAa,GAAG,IAAIJ,mBAAJ,CAAwB,KAAKjuB,IAAL,CAAUtO,UAAlC,EAA8Cy8B,IAA9C,CAAtB;SACKnuB,IAAL,CAAU9N,QAAV,CAAmBtI,KAAnB,CAAyB,CAAC,CAA1B,EAA6B,CAA7B,EAAgCykC,aAAhC,GAAgDA,aAAhD;WACOA,aAAP;GA9EW;;EAiFbyB,gBAAgB,GAAG;SACZ9vB,IAAL,CAAU9N,QAAV,CAAmB6gB,GAAnB;SACKrR,UAAL,CAAgB,KAAhB;WACO,IAAP;GApFW;;EAuFb4iB,MAAM,CAACkK,IAAD,EAAOvmC,OAAO,GAAG,EAAjB,EAAqBmlC,QAAQ,GAAG,IAAhC,EAAsC;WACnC,IAAImB,mBAAJ,CAAwB,IAAxB,EAA8BC,IAA9B,EAAoCvmC,OAApC,EAA6CmlC,QAA7C,CAAP;GAxFW;;EA2FbhJ,YAAY,CAAC4M,UAAD,EAAa;UACjBC,cAAc,GAAG,KAAKP,iBAAL,EAAvB;IACAM,UAAU,CAAC3B,SAAX,CAAqB4B,cAArB;IACAD,UAAU,CAAC1B,WAAX;SACKgB,cAAL,CAAoBnnC,IAApB,CAAyB6nC,UAAzB;;QACI,CAACC,cAAc,CAAC7kC,IAAf,CAAoB4jC,CAAzB,EAA4B;MAC1BiB,cAAc,CAAC7kC,IAAf,CAAoB4jC,CAApB,GAAwB,EAAxB;;;IAEFiB,cAAc,CAAC7kC,IAAf,CAAoB4jC,CAApB,CAAsB7mC,IAAtB,CAA2B6nC,UAAU,CAACt/B,UAAtC;WACO,IAAP;GApGW;;EAuGbw/B,gBAAgB,CAACC,YAAD,EAAe;IAC7BA,YAAY,CAAC7C,OAAb,CAAsBuC,OAAD,IAAa;UAC5BA,OAAO,CAACxC,aAAZ,EAA2B;cACnBA,aAAa,GAAGwC,OAAO,CAACxC,aAA9B;cACM+C,gBAAgB,GAAG,KAAK5M,oBAAL,CAA0BqM,OAAO,CAAC/S,GAAlC,EAAuC+S,OAAO,CAAC5oC,OAA/C,CAAzB;QACAomC,aAAa,CAACllC,IAAd,CAAmBioC,gBAAnB;aACKpxB,IAAL,CAAU9N,QAAV,CAAmBtI,KAAnB,CAAyB,CAAC,CAA1B,EAA6B,CAA7B,EAAgCykC,aAAhC,GAAgDA,aAAhD;OAJF,MAKO;aACAsC,WAAL,CAAiBE,OAAO,CAAC/S,GAAzB,EAA8B+S,OAAO,CAAC5oC,OAAtC;;KAPJ;GAxGW;;EAoHbopC,eAAe,CAACrxB,IAAD,EAAO;UACdmxB,YAAY,GAAGnxB,IAAI,CAAC9N,QAA1B;IACAi/B,YAAY,CAAC7C,OAAb,CAAqB,MAAMtuB,IAAI,CAACtT,KAAL,CAAW,KAAX,CAA3B;IACAsT,IAAI,CAAC9N,QAAL,GAAgB,EAAhB;WACOi/B,YAAP;GAxHW;;EA2HbX,qBAAqB,GAAG;QAClB,CAAC,KAAK3+B,KAAL,CAAWzF,IAAX,CAAgBklC,QAArB,EAA+B;WACxBz/B,KAAL,CAAWzF,IAAX,CAAgBklC,QAAhB,GAA2B,KAAK//B,GAAL,CAAS,EAAT,CAA3B;;;WAEK,KAAKM,KAAL,CAAWzF,IAAX,CAAgBklC,QAAvB;GA/HW;;EAkIbZ,iBAAiB,GAAG;QACd,CAAC,KAAK7+B,KAAL,CAAWzF,IAAX,CAAgBmlC,cAArB,EAAqC;WAC9B1/B,KAAL,CAAWzF,IAAX,CAAgBmlC,cAAhB,GAAiC,KAAKhgC,GAAL,CAAS;QACxCI,IAAI,EAAE,gBADkC;QAExC6/B,UAAU,EAAE,IAAIpB,aAAJ,EAF4B;QAGxCqB,iBAAiB,EAAE;OAHY,CAAjC;;;WAMK,KAAK5/B,KAAL,CAAWzF,IAAX,CAAgBmlC,cAAvB;GA1IW;;EA6Ib7B,mBAAmB,GAAG;WACb,KAAKgB,iBAAL,GAAyBtkC,IAAzB,CAA8BolC,UAArC;GA9IW;;EAiJbz+B,6BAA6B,GAAG;;SAEzBy9B,qBAAL;UAEMS,cAAc,GAAG,KAAKP,iBAAL,EAAvB;UACMroC,GAAG,GAAG4oC,cAAc,CAAC7kC,IAAf,CAAoBqlC,iBAApB,EAAZ;IACAR,cAAc,CAAC7kC,IAAf,CAAoBolC,UAApB,CAA+BppC,GAA/B,CAAmCC,GAAnC,EAAwC,EAAxC;WACOA,GAAP;GAxJW;;EA2JbqpC,WAAW,GAAG;UACNT,cAAc,GAAG,KAAKp/B,KAAL,CAAWzF,IAAX,CAAgBmlC,cAAvC;;QACIN,cAAJ,EAAoB;MAClBA,cAAc,CAAC9mC,GAAf;WACKmmC,cAAL,CAAoBhC,OAApB,CAA6B0C,UAAD,IAAgBA,UAAU,CAAC7mC,GAAX,EAA5C;;;QAEE,KAAK0H,KAAL,CAAWzF,IAAX,CAAgBklC,QAApB,EAA8B;WACvBz/B,KAAL,CAAWzF,IAAX,CAAgBklC,QAAhB,CAAyBnnC,GAAzB;;;;CAlKN;;ACVA,MAAMwnC,WAAW,GAAG;EAClBC,QAAQ,EAAE,CADQ;EAElB9O,QAAQ,EAAE,CAFQ;EAGlB+O,QAAQ,EAAE,CAHQ;EAIlBC,SAAS,EAAE,MAJO;EAKlBh1B,QAAQ,EAAE,MALQ;EAMlBi1B,iBAAiB,EAAE,MAND;EAOlBC,WAAW,EAAE,MAPK;EAQlBC,UAAU,EAAE,OARM;EASlBC,KAAK,EAAE,OATW;EAUlBC,IAAI,EAAE,OAVY;EAWlBxpC,IAAI,EAAE,OAXY;EAYlBypC,WAAW,EAAE,QAZK;EAalBC,OAAO,EAAE;CAbX;AAeA,MAAMC,aAAa,GAAG;EACpB7kC,IAAI,EAAE,CADc;EAEpB8kC,MAAM,EAAE,CAFY;EAGpB5kC,KAAK,EAAE;CAHT;AAKA,MAAM6kC,SAAS,GAAG;EAAEn/B,KAAK,EAAE,GAAT;EAAco/B,YAAY,EAAE;CAA9C;AACA,MAAMC,cAAc,GAAG;EACrBC,GAAG,EAAE,GADgB;EAErBC,QAAQ,EAAE,GAFW;EAGrBC,IAAI,EAAE,GAHe;EAIrBC,KAAK,EAAE,GAJc;EAKrBC,GAAG,EAAE;CALP;AAOA,MAAMC,cAAc,GAAG;EACrBnnC,MAAM,EAAE;IACNonC,IAAI,EAAE,CADA;IAENC,QAAQ,EAAE,KAFJ;IAGNC,QAAQ,EAAE,YAHJ;IAINC,QAAQ,EAAE,EAJJ;IAKNC,eAAe,EAAE;GANE;EAQrBC,OAAO,EAAE;IACPL,IAAI,EAAE,CADC;IAEPC,QAAQ,EAAE;;CAVd;AAcA,oBAAe;;;;;EAKbK,QAAQ,GAAG;QACL,CAAC,KAAK9S,KAAV,EAAiB;YACT,IAAI34B,KAAJ,CAAU,gDAAV,CAAN;;;SAEG0rC,SAAL,GAAiB;MACfrhC,KAAK,EAAE,EADQ;MAEfkuB,WAAW,EAAE,KAAKI,KAAL,CAAW7c;KAF1B;SAIK4vB,SAAL,CAAerhC,KAAf,CAAqB,KAAKsuB,KAAL,CAAWt0B,EAAhC,IAAsC,KAAKs0B,KAAL,CAAWlvB,GAAX,EAAtC;QAEInF,IAAI,GAAG;MACTqnC,MAAM,EAAE,EADC;MAETC,eAAe,EAAE,IAFR;MAGTnH,EAAE,EAAE,IAAIjiC,MAAJ,CAAY,IAAG,KAAKm2B,KAAL,CAAWt0B,EAAG,WAA7B,CAHK;MAITwnC,EAAE,EAAE;QACFvhC,IAAI,EAAE;;KALV;IAQAhG,IAAI,CAACunC,EAAL,CAAQvhC,IAAR,CAAa,KAAKquB,KAAL,CAAWt0B,EAAxB,IAA8B,KAAKs0B,KAAL,CAAWlvB,GAAX,EAA9B;UACMqiC,QAAQ,GAAG,KAAKriC,GAAL,CAASnF,IAAT,CAAjB;SACKyF,KAAL,CAAWzF,IAAX,CAAgBwnC,QAAhB,GAA2BA,QAA3B;WACO,IAAP;GA1BW;;;;;EAgCbC,WAAW,GAAG;QACR,KAAKhiC,KAAL,CAAWzF,IAAX,CAAgBwnC,QAApB,EAA8B;UAE1B,CAACnrC,MAAM,CAACC,IAAP,CAAY,KAAK8qC,SAAL,CAAerhC,KAA3B,EAAkCnJ,MAAnC,IACA,CAAC,KAAKwqC,SAAL,CAAenT,WAFlB,EAGE;cACM,IAAIv4B,KAAJ,CAAU,iCAAV,CAAN;;;UAEEgsC,QAAQ,GAAG,KAAKjiC,KAAL,CAAWzF,IAAX,CAAgBwnC,QAAhB,CAAyBxnC,IAAzB,CAA8BunC,EAA9B,CAAiCvhC,IAAhD;MACA3J,MAAM,CAACC,IAAP,CAAY,KAAK8qC,SAAL,CAAerhC,KAA3B,EAAkCm8B,OAAlC,CAA0C1qB,IAAI,IAAI;QAChDkwB,QAAQ,CAAClwB,IAAD,CAAR,GAAiB,KAAK4vB,SAAL,CAAerhC,KAAf,CAAqByR,IAArB,CAAjB;OADF;;WAGK/R,KAAL,CAAWzF,IAAX,CAAgBwnC,QAAhB,CAAyBxnC,IAAzB,CAA8BqnC,MAA9B,CAAqCnF,OAArC,CAA6CyF,QAAQ,IAAI;aAClDC,SAAL,CAAeD,QAAf;OADF;;WAGKliC,KAAL,CAAWzF,IAAX,CAAgBwnC,QAAhB,CAAyBzpC,GAAzB;;;WAEK,IAAP;GAjDW;;EAoDb6pC,SAAS,CAACziC,GAAD,EAAM;QACT5H,KAAK,CAAC6B,OAAN,CAAc+F,GAAG,CAACnF,IAAJ,CAAS0/B,IAAvB,CAAJ,EAAkC;MAChCv6B,GAAG,CAACnF,IAAJ,CAAS0/B,IAAT,CAAcwC,OAAd,CAAsB2F,QAAQ,IAAI;aAC3BD,SAAL,CAAeC,QAAf;OADF;MAGA1iC,GAAG,CAACpH,GAAJ;;;WAEK,IAAP;GA3DW;;;;;;;;;EAqEb+pC,SAAS,CAACtwB,IAAD,EAAO3b,OAAO,GAAG,EAAjB,EAAqB;QACxBksC,SAAS,GAAG,KAAKC,UAAL,CAAgBxwB,IAAhB,EAAsB,IAAtB,EAA4B3b,OAA5B,CAAhB;;QACI8rC,QAAQ,GAAG,KAAKxiC,GAAL,CAAS4iC,SAAT,CAAf;;SACKE,YAAL,CAAkBN,QAAlB;;WACOA,QAAP;GAzEW;;;;;;;;;;;;;EAuFbO,cAAc,CAAC1wB,IAAD,EAAO4qB,IAAP,EAAaz5B,CAAb,EAAgB0a,CAAhB,EAAmBwD,CAAnB,EAAsBnF,CAAtB,EAAyB7lB,OAAO,GAAG,EAAnC,EAAuC;QAC/CksC,SAAS,GAAG,KAAKC,UAAL,CAAgBxwB,IAAhB,EAAsB4qB,IAAtB,EAA4BvmC,OAA5B,CAAhB;;IACAksC,SAAS,CAACj0B,OAAV,GAAoB,QAApB;;QACIi0B,SAAS,CAAC5I,CAAV,KAAgBhO,SAApB,EAA+B;MAC7B4W,SAAS,CAAC5I,CAAV,GAAc,CAAd,CAD6B;KAHoB;;;SAQ9CJ,QAAL,CAAcp2B,CAAd,EAAiB0a,CAAjB,EAAoBwD,CAApB,EAAuBnF,CAAvB,EAA0BqmB,SAA1B;QACII,QAAQ,GAAG,KAAKv0B,IAAL,CAAUrN,WAAV,CAAsB,KAAKqN,IAAL,CAAUrN,WAAV,CAAsB3J,MAAtB,GAA+B,CAArD,CAAf;WAEO,KAAKqrC,YAAL,CAAkBE,QAAlB,CAAP;GAlGW;;EAqGbC,QAAQ,CAAC5wB,IAAD,EAAO7O,CAAP,EAAU0a,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAmB7lB,OAAO,GAAG,EAA7B,EAAiC;WAChC,KAAKqsC,cAAL,CAAoB1wB,IAApB,EAA0B,MAA1B,EAAkC7O,CAAlC,EAAqC0a,CAArC,EAAwCwD,CAAxC,EAA2CnF,CAA3C,EAA8C7lB,OAA9C,CAAP;GAtGW;;EAyGbwsC,cAAc,CAAC7wB,IAAD,EAAO7O,CAAP,EAAU0a,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAmB7lB,OAAO,GAAG,EAA7B,EAAiC;WACtC,KAAKqsC,cAAL,CAAoB1wB,IAApB,EAA0B,YAA1B,EAAwC7O,CAAxC,EAA2C0a,CAA3C,EAA8CwD,CAA9C,EAAiDnF,CAAjD,EAAoD7lB,OAApD,CAAP;GA1GW;;EA6GbysC,SAAS,CAAC9wB,IAAD,EAAO7O,CAAP,EAAU0a,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAmB7lB,OAAO,GAAG,EAA7B,EAAiC;WACjC,KAAKqsC,cAAL,CAAoB1wB,IAApB,EAA0B,OAA1B,EAAmC7O,CAAnC,EAAsC0a,CAAtC,EAAyCwD,CAAzC,EAA4CnF,CAA5C,EAA+C7lB,OAA/C,CAAP;GA9GW;;EAiHb0sC,QAAQ,CAAC/wB,IAAD,EAAO7O,CAAP,EAAU0a,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAmB7lB,OAAO,GAAG,EAA7B,EAAiC;WAChC,KAAKqsC,cAAL,CAAoB1wB,IAApB,EAA0B,MAA1B,EAAkC7O,CAAlC,EAAqC0a,CAArC,EAAwCwD,CAAxC,EAA2CnF,CAA3C,EAA8C7lB,OAA9C,CAAP;GAlHW;;EAqHb2sC,eAAe,CAAChxB,IAAD,EAAO7O,CAAP,EAAU0a,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAmB7lB,OAAO,GAAG,EAA7B,EAAiC;WACvC,KAAKqsC,cAAL,CAAoB1wB,IAApB,EAA0B,aAA1B,EAAyC7O,CAAzC,EAA4C0a,CAA5C,EAA+CwD,CAA/C,EAAkDnF,CAAlD,EAAqD7lB,OAArD,CAAP;GAtHW;;EAyHb4sC,YAAY,CAACjxB,IAAD,EAAO7O,CAAP,EAAU0a,CAAV,EAAawD,CAAb,EAAgBnF,CAAhB,EAAmB7lB,OAAO,GAAG,EAA7B,EAAiC;WACpC,KAAKqsC,cAAL,CAAoB1wB,IAApB,EAA0B,UAA1B,EAAsC7O,CAAtC,EAAyC0a,CAAzC,EAA4CwD,CAA5C,EAA+CnF,CAA/C,EAAkD7lB,OAAlD,CAAP;GA1HW;;EA6HbosC,YAAY,CAACN,QAAD,EAAW;QACjBhH,MAAM,GAAGgH,QAAQ,CAAC3nC,IAAT,CAAcwF,MAA3B;;QACIm7B,MAAJ,EAAY;UACN,CAACA,MAAM,CAAC3gC,IAAP,CAAY0/B,IAAjB,EAAuB;QACrBiB,MAAM,CAAC3gC,IAAP,CAAY0/B,IAAZ,GAAmB,EAAnB;;;MAEFiB,MAAM,CAAC3gC,IAAP,CAAY0/B,IAAZ,CAAiB3iC,IAAjB,CAAsB4qC,QAAtB;KAJF,MAKO;WACAliC,KAAL,CAAWzF,IAAX,CAAgBwnC,QAAhB,CAAyBxnC,IAAzB,CAA8BqnC,MAA9B,CAAqCtqC,IAArC,CAA0C4qC,QAA1C;;;WAEK,IAAP;GAvIW;;EA0IbK,UAAU,CAACxwB,IAAD,EAAO4qB,IAAP,EAAavmC,OAAO,GAAG,EAAvB,EAA2B;QAC/B,CAAC,KAAKurC,SAAV,EAAqB;YACb,IAAI1rC,KAAJ,CACJ,0EADI,CAAN;;;QAIEwN,IAAI,GAAG7M,MAAM,CAAC29B,MAAP,CAAc,EAAd,EAAkBn+B,OAAlB,CAAX;;QACIumC,IAAI,KAAK,IAAb,EAAmB;MACjBl5B,IAAI,GAAG,KAAKw/B,YAAL,CAAkBtG,IAAlB,EAAwBvmC,OAAxB,CAAP;;;IAEFqN,IAAI,GAAG,KAAKy/B,aAAL,CAAmBz/B,IAAnB,CAAP;IACAA,IAAI,GAAG,KAAK0/B,eAAL,CAAqB1/B,IAArB,CAAP;IACAA,IAAI,GAAG,KAAK2/B,YAAL,CAAkB3/B,IAAlB,CAAP;IACAA,IAAI,GAAG,KAAK4/B,eAAL,CAAqB5/B,IAArB,CAAP;IACAA,IAAI,GAAG,KAAK6/B,cAAL,CAAoB7/B,IAApB,CAAP;IACAA,IAAI,GAAG,KAAK8/B,cAAL,CAAoB9/B,IAApB,CAAP;IACAA,IAAI,CAAC8Y,CAAL,GAAS,IAAI9jB,MAAJ,CAAWsZ,IAAX,CAAT;;QACItO,IAAI,CAACy3B,MAAT,EAAiB;MACfz3B,IAAI,CAAC1D,MAAL,GAAc0D,IAAI,CAACy3B,MAAnB;aACOz3B,IAAI,CAACy3B,MAAZ;;;WAEKz3B,IAAP;GA/JW;;EAkKbw/B,YAAY,CAACtG,IAAD,EAAOl5B,IAAP,EAAa;QACnBk5B,IAAI,KAAK,MAAb,EAAqB;MACnBl5B,IAAI,CAAC+/B,EAAL,GAAU,IAAV;KADF,MAEO,IAAI7G,IAAI,KAAK,YAAb,EAA2B;MAChCl5B,IAAI,CAAC+/B,EAAL,GAAU,KAAV;MACA//B,IAAI,CAAC28B,UAAL,GAAkB,IAAlB;KAFK,MAGA,IAAIzD,IAAI,KAAK,aAAb,EAA4B;MACjCl5B,IAAI,CAAC+/B,EAAL,GAAU,KAAV;MACA//B,IAAI,CAAC08B,WAAL,GAAmB,IAAnB;KAFK,MAGA,IAAIxD,IAAI,KAAK,UAAb,EAAyB;MAC9Bl5B,IAAI,CAAC+/B,EAAL,GAAU,KAAV;KADK,MAEA,IAAI7G,IAAI,KAAK,OAAb,EAAsB;MAC3Bl5B,IAAI,CAAC+/B,EAAL,GAAU,IAAV;MACA//B,IAAI,CAAC48B,KAAL,GAAa,IAAb;KAFK,MAGA,IAAI1D,IAAI,KAAK,MAAb,EAAqB;MAC1Bl5B,IAAI,CAAC+/B,EAAL,GAAU,IAAV;KADK,MAEA;YACC,IAAIvtC,KAAJ,CAAW,iCAAgC0mC,IAAK,GAAhD,CAAN;;;WAEKl5B,IAAP;GArLW;;EAwLb8/B,cAAc,CAAC9/B,IAAD,EAAO;UACbggC,CAAC,GAAGhgC,IAAI,CAACigC,MAAf;;QACID,CAAC,IAAIA,CAAC,CAAC9G,IAAX,EAAiB;UACXgH,WAAJ;UACIC,QAAJ;UACI1mB,MAAM,GAAG,EAAb;;UACI2jB,cAAc,CAAC4C,CAAC,CAAC9G,IAAH,CAAd,KAA2BjR,SAA/B,EAA0C;QACxCiY,WAAW,GAAI,qBAAf;QACAC,QAAQ,GAAI,kBAAZ;QACA1mB,MAAM,GAAG2jB,cAAc,CAAC4C,CAAC,CAAC9G,IAAH,CAAvB;OAHF,MAIO;YACD+G,MAAM,GAAGD,CAAC,CAAC9G,IAAF,CAAO5rB,MAAP,CAAc,CAAd,EAAiBzR,WAAjB,KAAiCmkC,CAAC,CAAC9G,IAAF,CAAO5kC,KAAP,CAAa,CAAb,CAA9C;QACA4rC,WAAW,GAAI,KAAID,MAAO,YAA1B;QACAE,QAAQ,GAAI,KAAIF,MAAO,SAAvB;;YAEID,CAAC,CAAC9G,IAAF,KAAW,MAAf,EAAuB;UACrBgH,WAAW,IAAI,IAAf;UACAzmB,MAAM,GAAGzkB,MAAM,CAACgrC,CAAC,CAACI,KAAH,CAAf;SAFF,MAGO,IAAIJ,CAAC,CAAC9G,IAAF,KAAW,MAAf,EAAuB;UAC5Bzf,MAAM,GAAGzkB,MAAM,CAACgrC,CAAC,CAACI,KAAH,CAAf;SADK,MAEA,IAAIJ,CAAC,CAAC9G,IAAF,KAAW,QAAf,EAAyB;cAC1B1E,CAAC,GAAGrhC,MAAM,CAAC29B,MAAP,CAAc,EAAd,EAAkB4M,cAAc,CAACnnC,MAAjC,EAAyCypC,CAAzC,CAAR;UACAvmB,MAAM,GAAGzkB,MAAM,CACb,CACEA,MAAM,CAACw/B,CAAC,CAACmJ,IAAH,CADR,EAEEnJ,CAAC,CAACoJ,QAAF,GAAa,GAAb,GAAmB,GAFrB,EAGE,MAAMpJ,CAAC,CAACqJ,QAAR,GAAmB,GAHrB,EAIE,MAJF,EAKE,MAAMrJ,CAAC,CAACsJ,QAAR,GAAmB,GALrB,EAME9oC,MAAM,CAACw/B,CAAC,CAACuJ,eAAH,CANR,EAOE7pC,IAPF,CAOO,GAPP,CADa,CAAf;SAFK,MAYA,IAAI8rC,CAAC,CAAC9G,IAAF,KAAW,SAAf,EAA0B;cAC3B1E,CAAC,GAAGrhC,MAAM,CAAC29B,MAAP,CAAc,EAAd,EAAkB4M,cAAc,CAACM,OAAjC,EAA0CgC,CAA1C,CAAR;UACAvmB,MAAM,GAAGzkB,MAAM,CAAC,CAACA,MAAM,CAACw/B,CAAC,CAACmJ,IAAH,CAAP,EAAiBnJ,CAAC,CAACoJ,QAAF,GAAa,GAAb,GAAmB,GAApC,EAAyC1pC,IAAzC,CAA8C,GAA9C,CAAD,CAAf;;;;MAGJ8L,IAAI,CAACqgC,EAAL,GAAUrgC,IAAI,CAACqgC,EAAL,GAAUrgC,IAAI,CAACqgC,EAAf,GAAoB,EAA9B;MACArgC,IAAI,CAACqgC,EAAL,CAAQ3F,CAAR,GAAY;QACV1vB,CAAC,EAAE,YADO;QAEVs1B,EAAE,EAAE,IAAItrC,MAAJ,CAAY,GAAEkrC,WAAY,IAAGzmB,MAAO,IAApC;OAFN;MAIAzZ,IAAI,CAACqgC,EAAL,CAAQpK,CAAR,GAAY;QACVjrB,CAAC,EAAE,YADO;QAEVs1B,EAAE,EAAE,IAAItrC,MAAJ,CAAY,GAAEmrC,QAAS,IAAG1mB,MAAO,IAAjC;OAFN;;;WAKKzZ,IAAI,CAACigC,MAAZ;WACOjgC,IAAP;GAxOW;;EA2Ob6/B,cAAc,CAAC7/B,IAAD,EAAO;QACfwI,KAAK,GAAG,KAAKE,eAAL,CAAqB1I,IAAI,CAACugC,eAA1B,CAAZ;;QACI/3B,KAAJ,EAAW;UACL,CAACxI,IAAI,CAACwgC,EAAV,EAAc;QACZxgC,IAAI,CAACwgC,EAAL,GAAU,EAAV;;;MAEFxgC,IAAI,CAACwgC,EAAL,CAAQC,EAAR,GAAaj4B,KAAb;;;IAEFA,KAAK,GAAG,KAAKE,eAAL,CAAqB1I,IAAI,CAAC0gC,WAA1B,CAAR;;QACIl4B,KAAJ,EAAW;UACL,CAACxI,IAAI,CAACwgC,EAAV,EAAc;QACZxgC,IAAI,CAACwgC,EAAL,GAAU,EAAV;;;MAEFxgC,IAAI,CAACwgC,EAAL,CAAQG,EAAR,GAAan4B,KAAb;;;WAEKxI,IAAI,CAACugC,eAAZ;WACOvgC,IAAI,CAAC0gC,WAAZ;WACO1gC,IAAP;GA5PW;;EA+Pby/B,aAAa,CAAC9sC,OAAD,EAAU;QACjBkqB,MAAM,GAAG,CAAb;IACA1pB,MAAM,CAACC,IAAP,CAAYT,OAAZ,EAAqBqmC,OAArB,CAA6BjmC,GAAG,IAAI;UAC9BspC,WAAW,CAACtpC,GAAD,CAAf,EAAsB;QACpB8pB,MAAM,IAAIwf,WAAW,CAACtpC,GAAD,CAArB;eACOJ,OAAO,CAACI,GAAD,CAAd;;KAHJ;;QAMI8pB,MAAM,KAAK,CAAf,EAAkB;MAChBlqB,OAAO,CAACiuC,EAAR,GAAajuC,OAAO,CAACiuC,EAAR,GAAajuC,OAAO,CAACiuC,EAArB,GAA0B,CAAvC;MACAjuC,OAAO,CAACiuC,EAAR,IAAc/jB,MAAd;;;WAEKlqB,OAAP;GA3QW;;EA8Qb+sC,eAAe,CAAC/sC,OAAD,EAAU;QACnBkqB,MAAM,GAAG,CAAb;;QACIlqB,OAAO,CAAC65B,KAAR,KAAkBvE,SAAtB,EAAiC;UAC3B,OAAO+U,aAAa,CAACrqC,OAAO,CAAC65B,KAAT,CAApB,KAAwC,QAA5C,EAAsD;QACpD3P,MAAM,GAAGmgB,aAAa,CAACrqC,OAAO,CAAC65B,KAAT,CAAtB;;;aAEK75B,OAAO,CAAC65B,KAAf;;;QAEE3P,MAAM,KAAK,CAAf,EAAkB;MAChBlqB,OAAO,CAACgmB,CAAR,GAAYkE,MAAZ,CADgB;;;WAGXlqB,OAAP;GAzRW;;EA4RbgtC,YAAY,CAAChtC,OAAD,EAAU;;QAEhB,KAAKurC,SAAL,CAAerhC,KAAf,CAAqB,KAAKsuB,KAAL,CAAWt0B,EAAhC,MAAwC,IAA5C,EAAkD;WAC3CqnC,SAAL,CAAerhC,KAAf,CAAqB,KAAKsuB,KAAL,CAAWt0B,EAAhC,IAAsC,KAAKs0B,KAAL,CAAWlvB,GAAX,EAAtC;KAHkB;;;QAOhB,KAAKiiC,SAAL,CAAenT,WAAf,KAA+B,KAAKI,KAAL,CAAW7c,IAA9C,EAAoD;MAClD3b,OAAO,CAAC0rC,EAAR,GAAa;QAAEvhC,IAAI,EAAE;OAArB,CADkD;;YAI5CwuB,QAAQ,GAAG34B,OAAO,CAAC24B,QAAR,IAAoB,CAArC;MAEA34B,OAAO,CAAC0rC,EAAR,CAAWvhC,IAAX,CAAgB,KAAKquB,KAAL,CAAWt0B,EAA3B,IAAiC,KAAKs0B,KAAL,CAAWlvB,GAAX,EAAjC;MACAtJ,OAAO,CAACskC,EAAR,GAAa,IAAIjiC,MAAJ,CAAY,IAAG,KAAKm2B,KAAL,CAAWt0B,EAAG,IAAGy0B,QAAS,SAAzC,CAAb;;;WAEK34B,OAAP;GA5SW;;EA+SbitC,eAAe,CAACjtC,OAAD,EAAU;QACnBkuC,MAAM,GAAG,EAAb;;aACSC,aAAT,CAAuBxtC,CAAvB,EAA0B;UACpBe,KAAK,CAAC6B,OAAN,CAAc5C,CAAd,CAAJ,EAAsB;aACf,IAAIytC,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAGztC,CAAC,CAACI,MAA1B,EAAkCqtC,GAAG,EAArC,EAAyC;cACnC,OAAOztC,CAAC,CAACytC,GAAD,CAAR,KAAkB,QAAtB,EAAgC;YAC9BF,MAAM,CAAChtC,IAAP,CAAY,IAAImB,MAAJ,CAAW1B,CAAC,CAACytC,GAAD,CAAZ,CAAZ;WADF,MAEO;YACLF,MAAM,CAAChtC,IAAP,CAAYP,CAAC,CAACytC,GAAD,CAAb;;;;;;IAKRD,aAAa,CAACnuC,OAAO,CAACquC,GAAT,CAAb;;QACIruC,OAAO,CAACkuC,MAAZ,EAAoB;MAClBC,aAAa,CAACnuC,OAAO,CAACkuC,MAAT,CAAb;aACOluC,OAAO,CAACkuC,MAAf;;;QAEEA,MAAM,CAACntC,MAAX,EAAmB;MACjBf,OAAO,CAACquC,GAAR,GAAcH,MAAd;;;IAGF1tC,MAAM,CAACC,IAAP,CAAY8pC,SAAZ,EAAuBlE,OAAvB,CAA+BjmC,GAAG,IAAI;UAChCJ,OAAO,CAACI,GAAD,CAAP,KAAiBk1B,SAArB,EAAgC;QAC9Bt1B,OAAO,CAACuqC,SAAS,CAACnqC,GAAD,CAAV,CAAP,GAA0BJ,OAAO,CAACI,GAAD,CAAjC;eACOJ,OAAO,CAACI,GAAD,CAAd;;KAHJ;KAMC,GAAD,EAAM,IAAN,EAAYimC,OAAZ,CAAoBjmC,GAAG,IAAI;UACrB,OAAOJ,OAAO,CAACI,GAAD,CAAd,KAAwB,QAA5B,EAAsC;QACpCJ,OAAO,CAACI,GAAD,CAAP,GAAe,IAAIiC,MAAJ,CAAWrC,OAAO,CAACI,GAAD,CAAlB,CAAf;;KAFJ;;QAMIJ,OAAO,CAAC6tC,EAAR,IAAc7tC,OAAO,CAAC6tC,EAAR,CAAWhyB,EAA7B,EAAiC;MAC/B7b,OAAO,CAAC6tC,EAAR,CAAWhyB,EAAX,GAAgB,IAAIxZ,MAAJ,CAAWrC,OAAO,CAAC6tC,EAAR,CAAWhyB,EAAtB,CAAhB;;;QAEE7b,OAAO,CAAC09B,KAAZ,EAAmB;MACjB19B,OAAO,CAAC6tC,EAAR,GAAa7tC,OAAO,CAAC6tC,EAAR,GAAa7tC,OAAO,CAAC6tC,EAArB,GAA0B,EAAvC;MACA7tC,OAAO,CAAC6tC,EAAR,CAAWhyB,EAAX,GAAgB,IAAIxZ,MAAJ,CAAWrC,OAAO,CAAC09B,KAAnB,CAAhB;aACO19B,OAAO,CAAC09B,KAAf;;;WAEK19B,OAAP;;;CAzVJ;;ACvCA,uBAAe;;;;;;;;;;;;;EAabwkC,IAAI,CAAC1M,GAAD,EAAM93B,OAAO,GAAG,EAAhB,EAAoB;IACtBA,OAAO,CAAC2b,IAAR,GAAe3b,OAAO,CAAC2b,IAAR,IAAgBmc,GAA/B;UAEMwW,OAAO,GAAG;MACd5kC,IAAI,EAAE,cADQ;MAEd6kC,MAAM,EAAE;KAFV;QAIIpqC,IAAJ;;QAEI,CAAC2zB,GAAL,EAAU;YACF,IAAIj4B,KAAJ,CAAU,kBAAV,CAAN;;;QAEE6C,MAAM,CAACK,QAAP,CAAgB+0B,GAAhB,CAAJ,EAA0B;MACxB3zB,IAAI,GAAG2zB,GAAP;KADF,MAEO,IAAIA,GAAG,YAAYI,WAAnB,EAAgC;MACrC/zB,IAAI,GAAGzB,MAAM,CAACC,IAAP,CAAY,IAAIs1B,UAAJ,CAAeH,GAAf,CAAZ,CAAP;KADK,MAEA;UACD9G,KAAJ;;UACKA,KAAK,GAAG,0BAA0BmR,IAA1B,CAA+BrK,GAA/B,CAAb,EAAmD;YAC7C9G,KAAK,CAAC,CAAD,CAAT,EAAc;UACZsd,OAAO,CAACr2B,OAAR,GAAkB+Y,KAAK,CAAC,CAAD,CAAL,CAASnuB,OAAT,CAAiB,GAAjB,EAAsB,KAAtB,CAAlB;;;QAEFsB,IAAI,GAAGzB,MAAM,CAACC,IAAP,CAAYquB,KAAK,CAAC,CAAD,CAAjB,EAAsB,QAAtB,CAAP;OAJF,MAKO;QACL7sB,IAAI,GAAG4rB,EAAE,CAACC,YAAH,CAAgB8H,GAAhB,CAAP;;YACI,CAAC3zB,IAAL,EAAW;gBACH,IAAItE,KAAJ,CAAW,+CAA8Ci4B,GAAI,EAA7D,CAAN;SAHG;;;cAOC;UAAE0W,SAAF;UAAaC;YAAU1e,EAAE,CAAC2e,QAAH,CAAY5W,GAAZ,CAA7B;QACAwW,OAAO,CAACC,MAAR,CAAe7/B,YAAf,GAA8B8/B,SAA9B;QACAF,OAAO,CAACC,MAAR,CAAeI,OAAf,GAAyBF,KAAzB;;KAhCkB;;;QAqClBzuC,OAAO,CAAC4uC,YAAR,YAAgC5rC,IAApC,EAA0C;MACxCsrC,OAAO,CAACC,MAAR,CAAe7/B,YAAf,GAA8B1O,OAAO,CAAC4uC,YAAtC;;;QAEE5uC,OAAO,CAAC6uC,YAAR,YAAgC7rC,IAApC,EAA0C;MACxCsrC,OAAO,CAACC,MAAR,CAAeI,OAAf,GAAyB3uC,OAAO,CAAC6uC,YAAjC;KAzCoB;;;QA4ClB7uC,OAAO,CAACumC,IAAZ,EAAkB;MAChB+H,OAAO,CAACr2B,OAAR,GAAkBjY,OAAO,CAACumC,IAAR,CAAa1jC,OAAb,CAAqB,GAArB,EAA0B,KAA1B,CAAlB;KA7CoB;;;UAiDhBisC,QAAQ,GAAGhgC,QAAQ,CAACC,GAAT,CACfD,QAAQ,CAACI,GAAT,CAAaC,SAAb,CAAuBE,MAAvB,CAA8B,IAAI4oB,UAAJ,CAAe9zB,IAAf,CAA9B,CADe,CAAjB;IAGAmqC,OAAO,CAACC,MAAR,CAAeQ,QAAf,GAA0B,IAAI1sC,MAAJ,CAAWysC,QAAX,CAA1B;IACAR,OAAO,CAACC,MAAR,CAAeS,IAAf,GAAsB7qC,IAAI,CAAC8qC,UAA3B,CArDsB;;;QAyDlB3lC,GAAJ;QACI,CAAC,KAAK4lC,aAAV,EAAyB,KAAKA,aAAL,GAAqB,EAArB;QACrB1K,IAAI,GAAG,KAAK0K,aAAL,CAAmBlvC,OAAO,CAAC2b,IAA3B,CAAX;;QACI6oB,IAAI,IAAI2K,OAAO,CAACb,OAAD,EAAU9J,IAAV,CAAnB,EAAoC;MAClCl7B,GAAG,GAAGk7B,IAAI,CAACl7B,GAAX;KADF,MAEO;MACLA,GAAG,GAAG,KAAKA,GAAL,CAASglC,OAAT,CAAN;MACAhlC,GAAG,CAACpH,GAAJ,CAAQiC,IAAR;WAEK+qC,aAAL,CAAmBlvC,OAAO,CAAC2b,IAA3B,sCAAwC2yB,OAAxC;QAAiDhlC;;KAlE7B;;;UAqEhB8lC,YAAY,GAAG;MACnB1lC,IAAI,EAAE,UADa;MAEnB45B,CAAC,EAAE,IAAIjhC,MAAJ,CAAWrC,OAAO,CAAC2b,IAAnB,CAFgB;MAGnB0zB,EAAE,EAAE;QAAE/L,CAAC,EAAEh6B;OAHU;MAInBgmC,EAAE,EAAE,IAAIjtC,MAAJ,CAAWrC,OAAO,CAAC2b,IAAnB;KAJN;;QAMI3b,OAAO,CAACuvC,WAAZ,EAAyB;MACvBH,YAAY,CAACxK,IAAb,GAAoB,IAAIviC,MAAJ,CAAWrC,OAAO,CAACuvC,WAAnB,CAApB;;;UAEI9K,QAAQ,GAAG,KAAKn7B,GAAL,CAAS8lC,YAAT,CAAjB;IACA3K,QAAQ,CAACviC,GAAT;;QAEI,CAAClC,OAAO,CAAC0kC,MAAb,EAAqB;WACd8K,oBAAL,CAA0BxvC,OAAO,CAAC2b,IAAlC,EAAwC8oB,QAAxC;;;WAGKA,QAAP;;;CAlGJ;;;AAuGA,SAAS0K,OAAT,CAAiBxuC,CAAjB,EAAoBC,CAApB,EAAuB;SAEnBD,CAAC,CAACsX,OAAF,KAAcrX,CAAC,CAACqX,OAAhB,IACAtX,CAAC,CAAC4tC,MAAF,CAASQ,QAAT,CAAkBnvC,QAAlB,OAAiCgB,CAAC,CAAC2tC,MAAF,CAASQ,QAAT,CAAkBnvC,QAAlB,EADjC,IAEAe,CAAC,CAAC4tC,MAAF,CAASS,IAAT,KAAkBpuC,CAAC,CAAC2tC,MAAF,CAASS,IAF3B,IAGAruC,CAAC,CAAC4tC,MAAF,CAAS7/B,YAAT,KAA0B9N,CAAC,CAAC2tC,MAAF,CAAS7/B,YAHnC,IAIA/N,CAAC,CAAC4tC,MAAF,CAASI,OAAT,KAAqB/tC,CAAC,CAAC2tC,MAAF,CAASI,OALhC;;;AC3GF;;;;AAKA;AAmBA,MAAMc,WAAN,SAA0BC,MAAM,CAACC,QAAjC,CAA0C;EACxC5vC,WAAW,CAACC,OAAO,GAAG,EAAX,EAAe;UAClBA,OAAN;SACKA,OAAL,GAAeA,OAAf,CAFwB;;YAKhBA,OAAO,CAACyP,UAAhB;WACO,KAAL;aACOC,OAAL,GAAe,GAAf;;;WAEG,KAAL;aACOA,OAAL,GAAe,GAAf;;;WAEG,KAAL;aACOA,OAAL,GAAe,GAAf;;;WAEG,KAAL;WACK,SAAL;aACOA,OAAL,GAAe,GAAf;;;;aAGKA,OAAL,GAAe,GAAf;;KApBoB;;;SAyBnBrL,QAAL,GACE,KAAKrE,OAAL,CAAaqE,QAAb,IAAyB,IAAzB,GAAgC,KAAKrE,OAAL,CAAaqE,QAA7C,GAAwD,IAD1D;SAGKurC,WAAL,GAAmB,EAAnB;SACKC,gBAAL,GAAwB,CAAxB,CA7BwB;;SAgCnBC,QAAL,GAAgB,EAAhB;SACKC,QAAL,GAAgB,CAAhB;SACKtJ,MAAL,GAAc,KAAd;SACK3hC,OAAL,GAAe,CAAf;UACM+E,KAAK,GAAG,KAAKP,GAAL,CAAS;MACrBI,IAAI,EAAE,OADe;MAErB47B,KAAK,EAAE,CAFc;MAGrBzB,IAAI,EAAE;KAHM,CAAd;UAMMmM,KAAK,GAAG,KAAK1mC,GAAL,CAAS;MACrB2mC,KAAK,EAAE,IAAIjlC,WAAJ;KADK,CAAd;SAIKpB,KAAL,GAAa,KAAKN,GAAL,CAAS;MACpBI,IAAI,EAAE,SADc;MAEpBG,KAFoB;MAGpBmmC;KAHW,CAAb;;QAMI,KAAKhwC,OAAL,CAAa4mC,IAAjB,EAAuB;WAChBh9B,KAAL,CAAWzF,IAAX,CAAgB0iC,IAAhB,GAAuB,IAAIxkC,MAAJ,CAAW,KAAKrC,OAAL,CAAa4mC,IAAxB,CAAvB;KArDsB;;;SAyDnB7uB,IAAL,GAAY,IAAZ,CAzDwB;;SA4DnByC,SAAL;SACKkQ,UAAL;SACKyN,SAAL,CAAen4B,OAAO,CAAC0yB,IAAvB;SACK+I,QAAL;SACK2G,UAAL;SACKwD,WAAL;SACKwC,YAAL,CAAkBpoC,OAAlB,EAlEwB;;SAqEnBwO,IAAL,GAAY;MACV0hC,QAAQ,EAAE,QADA;MAEVC,OAAO,EAAE,QAFC;MAGVzhC,YAAY,EAAE,IAAI1L,IAAJ;KAHhB;;QAMI,KAAKhD,OAAL,CAAawO,IAAjB,EAAuB;WAChB,IAAIpO,GAAT,IAAgB,KAAKJ,OAAL,CAAawO,IAA7B,EAAmC;cAC3BnO,GAAG,GAAG,KAAKL,OAAL,CAAawO,IAAb,CAAkBpO,GAAlB,CAAZ;aACKoO,IAAL,CAAUpO,GAAV,IAAiBC,GAAjB;;;;QAIA,KAAKL,OAAL,CAAaowC,YAAjB,EAA+B;WACxBxmC,KAAL,CAAWzF,IAAX,CAAgBksC,iBAAhB,GAAoC,KAAK/mC,GAAL,CAAS;QAC3CgnC,eAAe,EAAE;OADiB,CAApC;KAnFsB;;;SAyFnB3/B,GAAL,GAAWrC,WAAW,CAACC,cAAZ,CAA2B,KAAKC,IAAhC,CAAX,CAzFwB;;SA4FnBzJ,SAAL,GAAiBuJ,WAAW,CAACe,MAAZ,CAAmB,IAAnB,EAAyBrP,OAAzB,CAAjB,CA5FwB;;;SAgGnBoF,MAAL,CAAa,QAAO,KAAKsK,OAAQ,EAAjC,EAhGwB;;;SAmGnBtK,MAAL,CAAY,mBAAZ,EAnGwB;;;QAsGpB,KAAKpF,OAAL,CAAauwC,aAAb,KAA+B,KAAnC,EAA0C;WACnCC,OAAL;;;;EAIJA,OAAO,CAACxwC,OAAD,EAAU;QACXA,OAAO,IAAI,IAAf,EAAqB;OAClB;QAAEA;UAAY,IAAf;KAFa;;;QAMX,CAAC,KAAKA,OAAL,CAAaywC,WAAlB,EAA+B;WACxBC,UAAL;KAPa;;;SAWV34B,IAAL,GAAY,IAAInP,OAAJ,CAAY,IAAZ,EAAkB5I,OAAlB,CAAZ;;SACK4vC,WAAL,CAAiB1uC,IAAjB,CAAsB,KAAK6W,IAA3B,EAZe;;;UAeT6rB,KAAK,GAAG,KAAKh6B,KAAL,CAAWzF,IAAX,CAAgB0F,KAAhB,CAAsB1F,IAApC;IACAy/B,KAAK,CAACC,IAAN,CAAW3iC,IAAX,CAAgB,KAAK6W,IAAL,CAAUtO,UAA1B;IACAm6B,KAAK,CAAC0B,KAAN,GAjBe;;SAoBVx4B,CAAL,GAAS,KAAKiL,IAAL,CAAU/O,OAAV,CAAkBxD,IAA3B;SACKgiB,CAAL,GAAS,KAAKzP,IAAL,CAAU/O,OAAV,CAAkBzD,GAA3B,CArBe;;;SAyBViU,IAAL,GAAY,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAAZ;SACK9D,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,CAArB,EAAwB,CAAC,CAAzB,EAA4B,CAA5B,EAA+B,KAAKqC,IAAL,CAAU3O,MAAzC;SAEKkyB,IAAL,CAAU,WAAV;WAEO,IAAP;;;EAGFE,iBAAiB,CAACx7B,OAAD,EAAU;UACnBkpC,YAAY,GAAG,KAAKE,eAAL,CAAqB,KAAKrxB,IAA1B,CAArB;SAEKy4B,OAAL,CAAaxwC,OAAb;SAEKipC,gBAAL,CAAsBC,YAAtB;WAEO,IAAP;;;EAGFyH,iBAAiB,GAAG;WACX;MAAEC,KAAK,EAAE,KAAKf,gBAAd;MAAgCgB,KAAK,EAAE,KAAKjB,WAAL,CAAiB7uC;KAA/D;;;EAGF+vC,YAAY,CAACjtC,CAAD,EAAI;QACVkU,IAAJ;;QACI,EAAEA,IAAI,GAAG,KAAK63B,WAAL,CAAiB/rC,CAAC,GAAG,KAAKgsC,gBAA1B,CAAT,CAAJ,EAA2D;YACnD,IAAIhwC,KAAJ,CACH,gBAAegE,CAAE,gDAChB,KAAKgsC,gBACN,OAAM,KAAKA,gBAAL,GAAwB,KAAKD,WAAL,CAAiB7uC,MAAzC,GAAkD,CAAE,EAHvD,CAAN;;;WAOM,KAAKgX,IAAL,GAAYA,IAApB;;;EAGF24B,UAAU,GAAG;;;UAGL9M,KAAK,GAAG,KAAKgM,WAAnB;SACKA,WAAL,GAAmB,EAAnB;SACKC,gBAAL,IAAyBjM,KAAK,CAAC7iC,MAA/B;;SACK,IAAIgX,IAAT,IAAiB6rB,KAAjB,EAAwB;WACjBwF,eAAL,CAAqBrxB,IAArB;MACAA,IAAI,CAAC7V,GAAL;;;;EAIJ08B,mBAAmB,CAACjjB,IAAD,EAAO,GAAGgL,IAAV,EAAgB;QAC7BA,IAAI,CAAC5lB,MAAL,KAAgB,CAApB,EAAuB;MACrB4lB,IAAI,GAAG,CAAC,KAAD,EAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB,CAAP;;;QAEEA,IAAI,CAAC,CAAD,CAAJ,KAAY,KAAZ,IAAqBA,IAAI,CAAC,CAAD,CAAJ,KAAY,IAArC,EAA2C;MACzCA,IAAI,CAAC,CAAD,CAAJ,GAAU,KAAK5O,IAAL,CAAU3O,MAAV,GAAmBud,IAAI,CAAC,CAAD,CAAjC;;;IAEFA,IAAI,CAACoqB,OAAL,CAAa,KAAKh5B,IAAL,CAAUtO,UAAvB;;SACKG,KAAL,CAAWzF,IAAX,CAAgB6rC,KAAhB,CAAsB7rC,IAAtB,CAA2B8rC,KAA3B,CAAiC9vC,GAAjC,CAAqCwb,IAArC,EAA2CgL,IAA3C;;;EAGF6oB,oBAAoB,CAAC7zB,IAAD,EAAOrS,GAAP,EAAY;QAC1B,CAAC,KAAKM,KAAL,CAAWzF,IAAX,CAAgB6rC,KAAhB,CAAsB7rC,IAAtB,CAA2B6sC,aAAhC,EAA+C;;WAExCpnC,KAAL,CAAWzF,IAAX,CAAgB6rC,KAAhB,CAAsB7rC,IAAtB,CAA2B6sC,aAA3B,GAA2C,IAAIhmC,WAAJ,CAAgB;QAAE9K,MAAM,EAAE;OAA1B,CAA3C;KAH4B;;;SAOzB0J,KAAL,CAAWzF,IAAX,CAAgB6rC,KAAhB,CAAsB7rC,IAAtB,CAA2B6sC,aAA3B,CAAyC7wC,GAAzC,CAA6Cwb,IAA7C,EAAmDrS,GAAnD;;;EAGF2nC,kBAAkB,CAACt1B,IAAD,EAAOu1B,EAAP,EAAW;QACvB,CAAC,KAAKtnC,KAAL,CAAWzF,IAAX,CAAgB6rC,KAAhB,CAAsB7rC,IAAtB,CAA2BgtC,UAAhC,EAA4C;WACrCvnC,KAAL,CAAWzF,IAAX,CAAgB6rC,KAAhB,CAAsB7rC,IAAtB,CAA2BgtC,UAA3B,GAAwC,IAAInmC,WAAJ,EAAxC;;;QAEE7G,IAAI,GAAG;MACTwpC,EAAE,EAAE,IAAItrC,MAAJ,CAAW6uC,EAAX,CADK;MAET74B,CAAC,EAAE;KAFL;;SAIKzO,KAAL,CAAWzF,IAAX,CAAgB6rC,KAAhB,CAAsB7rC,IAAtB,CAA2BgtC,UAA3B,CAAsChxC,GAAtC,CAA0Cwb,IAA1C,EAAgDxX,IAAhD;;;EAGFmF,GAAG,CAACnF,IAAD,EAAO;UACFmF,GAAG,GAAG,IAAItF,YAAJ,CAAiB,IAAjB,EAAuB,KAAK8rC,QAAL,CAAc/uC,MAAd,GAAuB,CAA9C,EAAiDoD,IAAjD,CAAZ;;SACK2rC,QAAL,CAAc5uC,IAAd,CAAmB,IAAnB,EAFQ;;;SAGH6uC,QAAL;WACOzmC,GAAP;;;EAGF8nC,KAAK,GAAG,EA3NgC;;;EA8NxChsC,MAAM,CAACjB,IAAD,EAAO;QACP,CAACzB,MAAM,CAACK,QAAP,CAAgBoB,IAAhB,CAAL,EAA4B;MAC1BA,IAAI,GAAGzB,MAAM,CAACC,IAAP,CAAYwB,IAAI,GAAG,IAAnB,EAAyB,QAAzB,CAAP;;;SAGGjD,IAAL,CAAUiD,IAAV;WACQ,KAAKW,OAAL,IAAgBX,IAAI,CAACpD,MAA7B;;;EAGF0Y,UAAU,CAACtV,IAAD,EAAO;SACV4T,IAAL,CAAUtT,KAAV,CAAgBN,IAAhB;WACO,IAAP;;;EAGFkB,OAAO,CAACiE,GAAD,EAAM;SACNwmC,QAAL,CAAcxmC,GAAG,CAACpF,EAAJ,GAAS,CAAvB,IAA4BoF,GAAG,CAACzE,MAAhC;;QACI,EAAE,KAAKkrC,QAAP,KAAoB,CAApB,IAAyB,KAAKtJ,MAAlC,EAA0C;WACnC4K,SAAL;;aACQ,KAAK5K,MAAL,GAAc,KAAtB;;;;EAIJhiC,KAAK,CAACqrB,QAAD,EAAWnZ,EAAX,EAAe;;UAEZ26B,GAAG,GAAG,IAAIzxC,KAAJ,CAAW;;;CAAX,CAAZ;IAKA0xC,OAAO,CAACC,IAAR,CAAaF,GAAG,CAACG,KAAjB;SAEKC,IAAL,CAAU3hB,EAAE,CAAC4hB,iBAAH,CAAqB7hB,QAArB,CAAV;SACK5tB,GAAL;WACO,KAAKy3B,IAAL,CAAU,KAAV,EAAiBhjB,EAAjB,CAAP;;;EAGFzU,GAAG,GAAG;SACCwuC,UAAL;SACKkB,KAAL,GAAa,KAAKtoC,GAAL,EAAb;;SACK,IAAIlJ,GAAT,IAAgB,KAAKoO,IAArB,EAA2B;UACrBnO,GAAG,GAAG,KAAKmO,IAAL,CAAUpO,GAAV,CAAV;;UACI,OAAOC,GAAP,KAAe,QAAnB,EAA6B;QAC3BA,GAAG,GAAG,IAAIgC,MAAJ,CAAWhC,GAAX,CAAN;;;UAGEwxC,KAAK,GAAG,KAAKvoC,GAAL,CAASjJ,GAAT,CAAZ;MACAwxC,KAAK,CAAC3vC,GAAN;WAEK0vC,KAAL,CAAWztC,IAAX,CAAgB/D,GAAhB,IAAuByxC,KAAvB;;;SAGGD,KAAL,CAAW1vC,GAAX;;SAEK,IAAIyZ,IAAT,IAAiB,KAAK0c,aAAtB,EAAqC;YAC7B3F,IAAI,GAAG,KAAK2F,aAAL,CAAmB1c,IAAnB,CAAb;MACA+W,IAAI,CAAC9tB,QAAL;;;SAGGygC,UAAL;SACKoE,WAAL;;SAEK7/B,KAAL,CAAW1H,GAAX;;SACK0H,KAAL,CAAWzF,IAAX,CAAgB0F,KAAhB,CAAsB3H,GAAtB;;SACK0H,KAAL,CAAWzF,IAAX,CAAgB6rC,KAAhB,CAAsB9tC,GAAtB;;SACK0pC,WAAL;;QAEI,KAAKhiC,KAAL,CAAWzF,IAAX,CAAgBksC,iBAApB,EAAuC;WAChCzmC,KAAL,CAAWzF,IAAX,CAAgBksC,iBAAhB,CAAkCnuC,GAAlC;;;QAGE,KAAK6C,SAAT,EAAoB;WACbA,SAAL,CAAe7C,GAAf;;;QAGE,KAAK6tC,QAAL,KAAkB,CAAtB,EAAyB;aAChB,KAAKsB,SAAL,EAAP;KADF,MAEO;aACG,KAAK5K,MAAL,GAAc,IAAtB;;;;EAIJ4K,SAAS,GAAG;;UAEJS,UAAU,GAAG,KAAKhtC,OAAxB;;SACKM,MAAL,CAAY,MAAZ;;SACKA,MAAL,CAAa,KAAI,KAAK0qC,QAAL,CAAc/uC,MAAd,GAAuB,CAAE,EAA1C;;SACKqE,MAAL,CAAY,qBAAZ;;SAEK,IAAIP,MAAT,IAAmB,KAAKirC,QAAxB,EAAkC;MAChCjrC,MAAM,GAAI,aAAYA,MAAO,EAApB,CAAsBlD,KAAtB,CAA4B,CAAC,EAA7B,CAAT;;WACKyD,MAAL,CAAYP,MAAM,GAAG,WAArB;KATQ;;;UAaJktC,OAAO,GAAG;MACd/C,IAAI,EAAE,KAAKc,QAAL,CAAc/uC,MAAd,GAAuB,CADf;MAEdixC,IAAI,EAAE,KAAKpoC,KAFG;MAGdqoC,IAAI,EAAE,KAAKL,KAHG;MAIdM,EAAE,EAAE,CAAC,KAAKvhC,GAAN,EAAW,KAAKA,GAAhB;KAJN;;QAMI,KAAK5L,SAAT,EAAoB;MAClBgtC,OAAO,CAACI,OAAR,GAAkB,KAAKptC,SAAL,CAAe0E,UAAjC;;;SAGGrE,MAAL,CAAY,SAAZ;;SACKA,MAAL,CAAYjE,SAAS,CAACC,OAAV,CAAkB2wC,OAAlB,CAAZ;;SAEK3sC,MAAL,CAAY,WAAZ;;SACKA,MAAL,CAAa,GAAE0sC,UAAW,EAA1B;;SACK1sC,MAAL,CAAY,OAAZ,EA5BU;;;WA+BH,KAAKlE,IAAL,CAAU,IAAV,CAAP;;;EAGFtB,QAAQ,GAAG;WACF,sBAAP;;;;;AAIJ,MAAMwyC,KAAK,GAAGC,OAAO,IAAI;EACvB7xC,MAAM,CAAC29B,MAAP,CAAcsR,WAAW,CAAC6C,SAA1B,EAAqCD,OAArC;CADF;;AAIAD,KAAK,CAACG,UAAD,CAAL;AACAH,KAAK,CAACI,WAAD,CAAL;AACAJ,KAAK,CAACK,UAAD,CAAL;AACAL,KAAK,CAACM,SAAD,CAAL;AACAN,KAAK,CAACO,WAAD,CAAL;AACAP,KAAK,CAACQ,gBAAD,CAAL;AACAR,KAAK,CAACS,YAAD,CAAL;AACAT,KAAK,CAACU,aAAD,CAAL;AACAV,KAAK,CAACW,aAAD,CAAL;AACAX,KAAK,CAACY,gBAAD,CAAL;AAEAvD,WAAW,CAAC3W,WAAZ,GAA0BA,WAA1B;;;;"}