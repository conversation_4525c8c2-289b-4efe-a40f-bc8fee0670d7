import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_StartAssociationsOnceCommand, se_StartAssociationsOnceCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class StartAssociationsOnceCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSSM", "StartAssociationsOnce", {})
    .n("SSMClient", "StartAssociationsOnceCommand")
    .f(void 0, void 0)
    .ser(se_StartAssociationsOnceCommand)
    .de(de_StartAssociationsOnceCommand)
    .build() {
}
