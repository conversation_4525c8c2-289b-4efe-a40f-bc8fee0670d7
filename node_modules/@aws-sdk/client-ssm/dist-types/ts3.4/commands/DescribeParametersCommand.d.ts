import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeParametersRequest,
  DescribeParametersResult,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeParametersCommandInput
  extends DescribeParametersRequest {}
export interface DescribeParametersCommandOutput
  extends DescribeParametersResult,
    __MetadataBearer {}
declare const DescribeParametersCommand_base: {
  new (
    input: DescribeParametersCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeParametersCommandInput,
    DescribeParametersCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [DescribeParametersCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeParametersCommandInput,
    DescribeParametersCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeParametersCommand extends DescribeParametersCommand_base {
  protected static __types: {
    api: {
      input: DescribeParametersRequest;
      output: DescribeParametersResult;
    };
    sdk: {
      input: DescribeParametersCommandInput;
      output: DescribeParametersCommandOutput;
    };
  };
}
