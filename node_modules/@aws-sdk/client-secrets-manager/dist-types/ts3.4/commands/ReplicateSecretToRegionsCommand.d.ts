import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ReplicateSecretToRegionsRequest,
  ReplicateSecretToRegionsResponse,
} from "../models/models_0";
import {
  SecretsManagerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SecretsManagerClient";
export { __MetadataBearer };
export { $Command };
export interface ReplicateSecretToRegionsCommandInput
  extends ReplicateSecretToRegionsRequest {}
export interface ReplicateSecretToRegionsCommandOutput
  extends ReplicateSecretToRegionsResponse,
    __MetadataBearer {}
declare const ReplicateSecretToRegionsCommand_base: {
  new (
    input: ReplicateSecretToRegionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ReplicateSecretToRegionsCommandInput,
    ReplicateSecretToRegionsCommandOutput,
    SecretsManagerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ReplicateSecretToRegionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ReplicateSecretToRegionsCommandInput,
    ReplicateSecretToRegionsCommandOutput,
    SecretsManagerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ReplicateSecretToRegionsCommand extends ReplicateSecretToRegionsCommand_base {
  protected static __types: {
    api: {
      input: ReplicateSecretToRegionsRequest;
      output: ReplicateSecretToRegionsResponse;
    };
    sdk: {
      input: ReplicateSecretToRegionsCommandInput;
      output: ReplicateSecretToRegionsCommandOutput;
    };
  };
}
