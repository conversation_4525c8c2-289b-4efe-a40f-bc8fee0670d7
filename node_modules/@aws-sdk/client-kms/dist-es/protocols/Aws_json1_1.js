import { loadRest<PERSON>sonErrorCode, parse<PERSON>sonBody as parseBody, parseJsonErrorBody as parseErrorBody } from "@aws-sdk/core";
import { HttpRequest as __HttpRequest } from "@smithy/protocol-http";
import { _json, collectBody, decorateServiceException as __decorateServiceException, expectBoolean as __expectBoolean, expectInt32 as __expectInt32, expectNonNull as __expectNonNull, expectNumber as __expectNumber, expectString as __expectString, parseEpochTimestamp as __parseEpochTimestamp, take, withBaseException, } from "@smithy/smithy-client";
import { KMSServiceException as __BaseException } from "../models/KMSServiceException";
import { AlreadyExistsException, CloudHsmClusterInUseException, CloudHsmClusterInvalidConfigurationException, CloudHsmClusterNotActiveException, CloudHsmClusterNotFoundException, CloudHsmClusterNotRelatedException, ConflictException, CustomKeyStoreHasCMKsException, CustomKeyStoreInvalidStateException, CustomKeyStoreNameInUseException, CustomKeyStoreNotFoundException, DependencyTimeoutException, DisabledException, DryRunOperationException, ExpiredImportTokenException, IncorrectKeyException, IncorrectKeyMaterialException, IncorrectTrustAnchorException, InvalidAliasNameException, InvalidArnException, InvalidCiphertextException, InvalidGrantIdException, InvalidGrantTokenException, InvalidImportTokenException, InvalidKeyUsageException, InvalidMarkerException, KeyUnavailableException, KMSInternalException, KMSInvalidMacException, KMSInvalidSignatureException, KMSInvalidStateException, LimitExceededException, MalformedPolicyDocumentException, NotFoundException, TagException, UnsupportedOperationException, XksKeyAlreadyInUseException, XksKeyInvalidConfigurationException, XksKeyNotFoundException, XksProxyIncorrectAuthenticationCredentialException, XksProxyInvalidConfigurationException, XksProxyInvalidResponseException, XksProxyUriEndpointInUseException, XksProxyUriInUseException, XksProxyUriUnreachableException, XksProxyVpcEndpointServiceInUseException, XksProxyVpcEndpointServiceInvalidConfigurationException, XksProxyVpcEndpointServiceNotFoundException, } from "../models/models_0";
export const se_CancelKeyDeletionCommand = async (input, context) => {
    const headers = sharedHeaders("CancelKeyDeletion");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ConnectCustomKeyStoreCommand = async (input, context) => {
    const headers = sharedHeaders("ConnectCustomKeyStore");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_CreateAliasCommand = async (input, context) => {
    const headers = sharedHeaders("CreateAlias");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_CreateCustomKeyStoreCommand = async (input, context) => {
    const headers = sharedHeaders("CreateCustomKeyStore");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_CreateGrantCommand = async (input, context) => {
    const headers = sharedHeaders("CreateGrant");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_CreateKeyCommand = async (input, context) => {
    const headers = sharedHeaders("CreateKey");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_DecryptCommand = async (input, context) => {
    const headers = sharedHeaders("Decrypt");
    let body;
    body = JSON.stringify(se_DecryptRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_DeleteAliasCommand = async (input, context) => {
    const headers = sharedHeaders("DeleteAlias");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_DeleteCustomKeyStoreCommand = async (input, context) => {
    const headers = sharedHeaders("DeleteCustomKeyStore");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_DeleteImportedKeyMaterialCommand = async (input, context) => {
    const headers = sharedHeaders("DeleteImportedKeyMaterial");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_DeriveSharedSecretCommand = async (input, context) => {
    const headers = sharedHeaders("DeriveSharedSecret");
    let body;
    body = JSON.stringify(se_DeriveSharedSecretRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_DescribeCustomKeyStoresCommand = async (input, context) => {
    const headers = sharedHeaders("DescribeCustomKeyStores");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_DescribeKeyCommand = async (input, context) => {
    const headers = sharedHeaders("DescribeKey");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_DisableKeyCommand = async (input, context) => {
    const headers = sharedHeaders("DisableKey");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_DisableKeyRotationCommand = async (input, context) => {
    const headers = sharedHeaders("DisableKeyRotation");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_DisconnectCustomKeyStoreCommand = async (input, context) => {
    const headers = sharedHeaders("DisconnectCustomKeyStore");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_EnableKeyCommand = async (input, context) => {
    const headers = sharedHeaders("EnableKey");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_EnableKeyRotationCommand = async (input, context) => {
    const headers = sharedHeaders("EnableKeyRotation");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_EncryptCommand = async (input, context) => {
    const headers = sharedHeaders("Encrypt");
    let body;
    body = JSON.stringify(se_EncryptRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_GenerateDataKeyCommand = async (input, context) => {
    const headers = sharedHeaders("GenerateDataKey");
    let body;
    body = JSON.stringify(se_GenerateDataKeyRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_GenerateDataKeyPairCommand = async (input, context) => {
    const headers = sharedHeaders("GenerateDataKeyPair");
    let body;
    body = JSON.stringify(se_GenerateDataKeyPairRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_GenerateDataKeyPairWithoutPlaintextCommand = async (input, context) => {
    const headers = sharedHeaders("GenerateDataKeyPairWithoutPlaintext");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_GenerateDataKeyWithoutPlaintextCommand = async (input, context) => {
    const headers = sharedHeaders("GenerateDataKeyWithoutPlaintext");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_GenerateMacCommand = async (input, context) => {
    const headers = sharedHeaders("GenerateMac");
    let body;
    body = JSON.stringify(se_GenerateMacRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_GenerateRandomCommand = async (input, context) => {
    const headers = sharedHeaders("GenerateRandom");
    let body;
    body = JSON.stringify(se_GenerateRandomRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_GetKeyPolicyCommand = async (input, context) => {
    const headers = sharedHeaders("GetKeyPolicy");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_GetKeyRotationStatusCommand = async (input, context) => {
    const headers = sharedHeaders("GetKeyRotationStatus");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_GetParametersForImportCommand = async (input, context) => {
    const headers = sharedHeaders("GetParametersForImport");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_GetPublicKeyCommand = async (input, context) => {
    const headers = sharedHeaders("GetPublicKey");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ImportKeyMaterialCommand = async (input, context) => {
    const headers = sharedHeaders("ImportKeyMaterial");
    let body;
    body = JSON.stringify(se_ImportKeyMaterialRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ListAliasesCommand = async (input, context) => {
    const headers = sharedHeaders("ListAliases");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ListGrantsCommand = async (input, context) => {
    const headers = sharedHeaders("ListGrants");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ListKeyPoliciesCommand = async (input, context) => {
    const headers = sharedHeaders("ListKeyPolicies");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ListKeyRotationsCommand = async (input, context) => {
    const headers = sharedHeaders("ListKeyRotations");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ListKeysCommand = async (input, context) => {
    const headers = sharedHeaders("ListKeys");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ListResourceTagsCommand = async (input, context) => {
    const headers = sharedHeaders("ListResourceTags");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ListRetirableGrantsCommand = async (input, context) => {
    const headers = sharedHeaders("ListRetirableGrants");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_PutKeyPolicyCommand = async (input, context) => {
    const headers = sharedHeaders("PutKeyPolicy");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ReEncryptCommand = async (input, context) => {
    const headers = sharedHeaders("ReEncrypt");
    let body;
    body = JSON.stringify(se_ReEncryptRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ReplicateKeyCommand = async (input, context) => {
    const headers = sharedHeaders("ReplicateKey");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_RetireGrantCommand = async (input, context) => {
    const headers = sharedHeaders("RetireGrant");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_RevokeGrantCommand = async (input, context) => {
    const headers = sharedHeaders("RevokeGrant");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_RotateKeyOnDemandCommand = async (input, context) => {
    const headers = sharedHeaders("RotateKeyOnDemand");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_ScheduleKeyDeletionCommand = async (input, context) => {
    const headers = sharedHeaders("ScheduleKeyDeletion");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_SignCommand = async (input, context) => {
    const headers = sharedHeaders("Sign");
    let body;
    body = JSON.stringify(se_SignRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_TagResourceCommand = async (input, context) => {
    const headers = sharedHeaders("TagResource");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_UntagResourceCommand = async (input, context) => {
    const headers = sharedHeaders("UntagResource");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_UpdateAliasCommand = async (input, context) => {
    const headers = sharedHeaders("UpdateAlias");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_UpdateCustomKeyStoreCommand = async (input, context) => {
    const headers = sharedHeaders("UpdateCustomKeyStore");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_UpdateKeyDescriptionCommand = async (input, context) => {
    const headers = sharedHeaders("UpdateKeyDescription");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_UpdatePrimaryRegionCommand = async (input, context) => {
    const headers = sharedHeaders("UpdatePrimaryRegion");
    let body;
    body = JSON.stringify(_json(input));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_VerifyCommand = async (input, context) => {
    const headers = sharedHeaders("Verify");
    let body;
    body = JSON.stringify(se_VerifyRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const se_VerifyMacCommand = async (input, context) => {
    const headers = sharedHeaders("VerifyMac");
    let body;
    body = JSON.stringify(se_VerifyMacRequest(input, context));
    return buildHttpRpcRequest(context, headers, "/", undefined, body);
};
export const de_CancelKeyDeletionCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ConnectCustomKeyStoreCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_CreateAliasCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_CreateCustomKeyStoreCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_CreateGrantCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_CreateKeyCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_CreateKeyResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_DecryptCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_DecryptResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_DeleteAliasCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_DeleteCustomKeyStoreCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_DeleteImportedKeyMaterialCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_DeriveSharedSecretCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_DeriveSharedSecretResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_DescribeCustomKeyStoresCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_DescribeCustomKeyStoresResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_DescribeKeyCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_DescribeKeyResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_DisableKeyCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_DisableKeyRotationCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_DisconnectCustomKeyStoreCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_EnableKeyCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_EnableKeyRotationCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_EncryptCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_EncryptResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_GenerateDataKeyCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_GenerateDataKeyResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_GenerateDataKeyPairCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_GenerateDataKeyPairResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_GenerateDataKeyPairWithoutPlaintextCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_GenerateDataKeyPairWithoutPlaintextResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_GenerateDataKeyWithoutPlaintextCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_GenerateDataKeyWithoutPlaintextResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_GenerateMacCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_GenerateMacResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_GenerateRandomCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_GenerateRandomResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_GetKeyPolicyCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_GetKeyRotationStatusCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_GetKeyRotationStatusResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_GetParametersForImportCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_GetParametersForImportResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_GetPublicKeyCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_GetPublicKeyResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ImportKeyMaterialCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ListAliasesCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_ListAliasesResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ListGrantsCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_ListGrantsResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ListKeyPoliciesCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ListKeyRotationsCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_ListKeyRotationsResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ListKeysCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ListResourceTagsCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ListRetirableGrantsCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_ListGrantsResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_PutKeyPolicyCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_ReEncryptCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_ReEncryptResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ReplicateKeyCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_ReplicateKeyResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_RetireGrantCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_RevokeGrantCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_RotateKeyOnDemandCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_ScheduleKeyDeletionCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_ScheduleKeyDeletionResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_SignCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = de_SignResponse(data, context);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_TagResourceCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_UntagResourceCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_UpdateAliasCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_UpdateCustomKeyStoreCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_UpdateKeyDescriptionCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_UpdatePrimaryRegionCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    await collectBody(output.body, context);
    const response = {
        $metadata: deserializeMetadata(output),
    };
    return response;
};
export const de_VerifyCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
export const de_VerifyMacCommand = async (output, context) => {
    if (output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const data = await parseBody(output.body, context);
    let contents = {};
    contents = _json(data);
    const response = {
        $metadata: deserializeMetadata(output),
        ...contents,
    };
    return response;
};
const de_CommandError = async (output, context) => {
    const parsedOutput = {
        ...output,
        body: await parseErrorBody(output.body, context),
    };
    const errorCode = loadRestJsonErrorCode(output, parsedOutput.body);
    switch (errorCode) {
        case "DependencyTimeoutException":
        case "com.amazonaws.kms#DependencyTimeoutException":
            throw await de_DependencyTimeoutExceptionRes(parsedOutput, context);
        case "InvalidArnException":
        case "com.amazonaws.kms#InvalidArnException":
            throw await de_InvalidArnExceptionRes(parsedOutput, context);
        case "KMSInternalException":
        case "com.amazonaws.kms#KMSInternalException":
            throw await de_KMSInternalExceptionRes(parsedOutput, context);
        case "KMSInvalidStateException":
        case "com.amazonaws.kms#KMSInvalidStateException":
            throw await de_KMSInvalidStateExceptionRes(parsedOutput, context);
        case "NotFoundException":
        case "com.amazonaws.kms#NotFoundException":
            throw await de_NotFoundExceptionRes(parsedOutput, context);
        case "CloudHsmClusterInvalidConfigurationException":
        case "com.amazonaws.kms#CloudHsmClusterInvalidConfigurationException":
            throw await de_CloudHsmClusterInvalidConfigurationExceptionRes(parsedOutput, context);
        case "CloudHsmClusterNotActiveException":
        case "com.amazonaws.kms#CloudHsmClusterNotActiveException":
            throw await de_CloudHsmClusterNotActiveExceptionRes(parsedOutput, context);
        case "CustomKeyStoreInvalidStateException":
        case "com.amazonaws.kms#CustomKeyStoreInvalidStateException":
            throw await de_CustomKeyStoreInvalidStateExceptionRes(parsedOutput, context);
        case "CustomKeyStoreNotFoundException":
        case "com.amazonaws.kms#CustomKeyStoreNotFoundException":
            throw await de_CustomKeyStoreNotFoundExceptionRes(parsedOutput, context);
        case "AlreadyExistsException":
        case "com.amazonaws.kms#AlreadyExistsException":
            throw await de_AlreadyExistsExceptionRes(parsedOutput, context);
        case "InvalidAliasNameException":
        case "com.amazonaws.kms#InvalidAliasNameException":
            throw await de_InvalidAliasNameExceptionRes(parsedOutput, context);
        case "LimitExceededException":
        case "com.amazonaws.kms#LimitExceededException":
            throw await de_LimitExceededExceptionRes(parsedOutput, context);
        case "CloudHsmClusterInUseException":
        case "com.amazonaws.kms#CloudHsmClusterInUseException":
            throw await de_CloudHsmClusterInUseExceptionRes(parsedOutput, context);
        case "CloudHsmClusterNotFoundException":
        case "com.amazonaws.kms#CloudHsmClusterNotFoundException":
            throw await de_CloudHsmClusterNotFoundExceptionRes(parsedOutput, context);
        case "CustomKeyStoreNameInUseException":
        case "com.amazonaws.kms#CustomKeyStoreNameInUseException":
            throw await de_CustomKeyStoreNameInUseExceptionRes(parsedOutput, context);
        case "IncorrectTrustAnchorException":
        case "com.amazonaws.kms#IncorrectTrustAnchorException":
            throw await de_IncorrectTrustAnchorExceptionRes(parsedOutput, context);
        case "XksProxyIncorrectAuthenticationCredentialException":
        case "com.amazonaws.kms#XksProxyIncorrectAuthenticationCredentialException":
            throw await de_XksProxyIncorrectAuthenticationCredentialExceptionRes(parsedOutput, context);
        case "XksProxyInvalidConfigurationException":
        case "com.amazonaws.kms#XksProxyInvalidConfigurationException":
            throw await de_XksProxyInvalidConfigurationExceptionRes(parsedOutput, context);
        case "XksProxyInvalidResponseException":
        case "com.amazonaws.kms#XksProxyInvalidResponseException":
            throw await de_XksProxyInvalidResponseExceptionRes(parsedOutput, context);
        case "XksProxyUriEndpointInUseException":
        case "com.amazonaws.kms#XksProxyUriEndpointInUseException":
            throw await de_XksProxyUriEndpointInUseExceptionRes(parsedOutput, context);
        case "XksProxyUriInUseException":
        case "com.amazonaws.kms#XksProxyUriInUseException":
            throw await de_XksProxyUriInUseExceptionRes(parsedOutput, context);
        case "XksProxyUriUnreachableException":
        case "com.amazonaws.kms#XksProxyUriUnreachableException":
            throw await de_XksProxyUriUnreachableExceptionRes(parsedOutput, context);
        case "XksProxyVpcEndpointServiceInUseException":
        case "com.amazonaws.kms#XksProxyVpcEndpointServiceInUseException":
            throw await de_XksProxyVpcEndpointServiceInUseExceptionRes(parsedOutput, context);
        case "XksProxyVpcEndpointServiceInvalidConfigurationException":
        case "com.amazonaws.kms#XksProxyVpcEndpointServiceInvalidConfigurationException":
            throw await de_XksProxyVpcEndpointServiceInvalidConfigurationExceptionRes(parsedOutput, context);
        case "XksProxyVpcEndpointServiceNotFoundException":
        case "com.amazonaws.kms#XksProxyVpcEndpointServiceNotFoundException":
            throw await de_XksProxyVpcEndpointServiceNotFoundExceptionRes(parsedOutput, context);
        case "DisabledException":
        case "com.amazonaws.kms#DisabledException":
            throw await de_DisabledExceptionRes(parsedOutput, context);
        case "DryRunOperationException":
        case "com.amazonaws.kms#DryRunOperationException":
            throw await de_DryRunOperationExceptionRes(parsedOutput, context);
        case "InvalidGrantTokenException":
        case "com.amazonaws.kms#InvalidGrantTokenException":
            throw await de_InvalidGrantTokenExceptionRes(parsedOutput, context);
        case "MalformedPolicyDocumentException":
        case "com.amazonaws.kms#MalformedPolicyDocumentException":
            throw await de_MalformedPolicyDocumentExceptionRes(parsedOutput, context);
        case "TagException":
        case "com.amazonaws.kms#TagException":
            throw await de_TagExceptionRes(parsedOutput, context);
        case "UnsupportedOperationException":
        case "com.amazonaws.kms#UnsupportedOperationException":
            throw await de_UnsupportedOperationExceptionRes(parsedOutput, context);
        case "XksKeyAlreadyInUseException":
        case "com.amazonaws.kms#XksKeyAlreadyInUseException":
            throw await de_XksKeyAlreadyInUseExceptionRes(parsedOutput, context);
        case "XksKeyInvalidConfigurationException":
        case "com.amazonaws.kms#XksKeyInvalidConfigurationException":
            throw await de_XksKeyInvalidConfigurationExceptionRes(parsedOutput, context);
        case "XksKeyNotFoundException":
        case "com.amazonaws.kms#XksKeyNotFoundException":
            throw await de_XksKeyNotFoundExceptionRes(parsedOutput, context);
        case "IncorrectKeyException":
        case "com.amazonaws.kms#IncorrectKeyException":
            throw await de_IncorrectKeyExceptionRes(parsedOutput, context);
        case "InvalidCiphertextException":
        case "com.amazonaws.kms#InvalidCiphertextException":
            throw await de_InvalidCiphertextExceptionRes(parsedOutput, context);
        case "InvalidKeyUsageException":
        case "com.amazonaws.kms#InvalidKeyUsageException":
            throw await de_InvalidKeyUsageExceptionRes(parsedOutput, context);
        case "KeyUnavailableException":
        case "com.amazonaws.kms#KeyUnavailableException":
            throw await de_KeyUnavailableExceptionRes(parsedOutput, context);
        case "CustomKeyStoreHasCMKsException":
        case "com.amazonaws.kms#CustomKeyStoreHasCMKsException":
            throw await de_CustomKeyStoreHasCMKsExceptionRes(parsedOutput, context);
        case "InvalidMarkerException":
        case "com.amazonaws.kms#InvalidMarkerException":
            throw await de_InvalidMarkerExceptionRes(parsedOutput, context);
        case "ExpiredImportTokenException":
        case "com.amazonaws.kms#ExpiredImportTokenException":
            throw await de_ExpiredImportTokenExceptionRes(parsedOutput, context);
        case "IncorrectKeyMaterialException":
        case "com.amazonaws.kms#IncorrectKeyMaterialException":
            throw await de_IncorrectKeyMaterialExceptionRes(parsedOutput, context);
        case "InvalidImportTokenException":
        case "com.amazonaws.kms#InvalidImportTokenException":
            throw await de_InvalidImportTokenExceptionRes(parsedOutput, context);
        case "InvalidGrantIdException":
        case "com.amazonaws.kms#InvalidGrantIdException":
            throw await de_InvalidGrantIdExceptionRes(parsedOutput, context);
        case "ConflictException":
        case "com.amazonaws.kms#ConflictException":
            throw await de_ConflictExceptionRes(parsedOutput, context);
        case "CloudHsmClusterNotRelatedException":
        case "com.amazonaws.kms#CloudHsmClusterNotRelatedException":
            throw await de_CloudHsmClusterNotRelatedExceptionRes(parsedOutput, context);
        case "KMSInvalidSignatureException":
        case "com.amazonaws.kms#KMSInvalidSignatureException":
            throw await de_KMSInvalidSignatureExceptionRes(parsedOutput, context);
        case "KMSInvalidMacException":
        case "com.amazonaws.kms#KMSInvalidMacException":
            throw await de_KMSInvalidMacExceptionRes(parsedOutput, context);
        default:
            const parsedBody = parsedOutput.body;
            return throwDefaultError({
                output,
                parsedBody,
                errorCode,
            });
    }
};
const de_AlreadyExistsExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new AlreadyExistsException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_CloudHsmClusterInUseExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new CloudHsmClusterInUseException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_CloudHsmClusterInvalidConfigurationExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new CloudHsmClusterInvalidConfigurationException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_CloudHsmClusterNotActiveExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new CloudHsmClusterNotActiveException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_CloudHsmClusterNotFoundExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new CloudHsmClusterNotFoundException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_CloudHsmClusterNotRelatedExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new CloudHsmClusterNotRelatedException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_ConflictExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new ConflictException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_CustomKeyStoreHasCMKsExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new CustomKeyStoreHasCMKsException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_CustomKeyStoreInvalidStateExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new CustomKeyStoreInvalidStateException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_CustomKeyStoreNameInUseExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new CustomKeyStoreNameInUseException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_CustomKeyStoreNotFoundExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new CustomKeyStoreNotFoundException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_DependencyTimeoutExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new DependencyTimeoutException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_DisabledExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new DisabledException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_DryRunOperationExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new DryRunOperationException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_ExpiredImportTokenExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new ExpiredImportTokenException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_IncorrectKeyExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new IncorrectKeyException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_IncorrectKeyMaterialExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new IncorrectKeyMaterialException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_IncorrectTrustAnchorExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new IncorrectTrustAnchorException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_InvalidAliasNameExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new InvalidAliasNameException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_InvalidArnExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new InvalidArnException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_InvalidCiphertextExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new InvalidCiphertextException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_InvalidGrantIdExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new InvalidGrantIdException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_InvalidGrantTokenExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new InvalidGrantTokenException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_InvalidImportTokenExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new InvalidImportTokenException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_InvalidKeyUsageExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new InvalidKeyUsageException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_InvalidMarkerExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new InvalidMarkerException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_KeyUnavailableExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new KeyUnavailableException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_KMSInternalExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new KMSInternalException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_KMSInvalidMacExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new KMSInvalidMacException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_KMSInvalidSignatureExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new KMSInvalidSignatureException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_KMSInvalidStateExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new KMSInvalidStateException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_LimitExceededExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new LimitExceededException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_MalformedPolicyDocumentExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new MalformedPolicyDocumentException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_NotFoundExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new NotFoundException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_TagExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new TagException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_UnsupportedOperationExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new UnsupportedOperationException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksKeyAlreadyInUseExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksKeyAlreadyInUseException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksKeyInvalidConfigurationExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksKeyInvalidConfigurationException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksKeyNotFoundExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksKeyNotFoundException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksProxyIncorrectAuthenticationCredentialExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksProxyIncorrectAuthenticationCredentialException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksProxyInvalidConfigurationExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksProxyInvalidConfigurationException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksProxyInvalidResponseExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksProxyInvalidResponseException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksProxyUriEndpointInUseExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksProxyUriEndpointInUseException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksProxyUriInUseExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksProxyUriInUseException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksProxyUriUnreachableExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksProxyUriUnreachableException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksProxyVpcEndpointServiceInUseExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksProxyVpcEndpointServiceInUseException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksProxyVpcEndpointServiceInvalidConfigurationExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksProxyVpcEndpointServiceInvalidConfigurationException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const de_XksProxyVpcEndpointServiceNotFoundExceptionRes = async (parsedOutput, context) => {
    const body = parsedOutput.body;
    const deserialized = _json(body);
    const exception = new XksProxyVpcEndpointServiceNotFoundException({
        $metadata: deserializeMetadata(parsedOutput),
        ...deserialized,
    });
    return __decorateServiceException(exception, body);
};
const se_DecryptRequest = (input, context) => {
    return take(input, {
        CiphertextBlob: context.base64Encoder,
        DryRun: [],
        EncryptionAlgorithm: [],
        EncryptionContext: _json,
        GrantTokens: _json,
        KeyId: [],
        Recipient: (_) => se_RecipientInfo(_, context),
    });
};
const se_DeriveSharedSecretRequest = (input, context) => {
    return take(input, {
        DryRun: [],
        GrantTokens: _json,
        KeyAgreementAlgorithm: [],
        KeyId: [],
        PublicKey: context.base64Encoder,
        Recipient: (_) => se_RecipientInfo(_, context),
    });
};
const se_EncryptRequest = (input, context) => {
    return take(input, {
        DryRun: [],
        EncryptionAlgorithm: [],
        EncryptionContext: _json,
        GrantTokens: _json,
        KeyId: [],
        Plaintext: context.base64Encoder,
    });
};
const se_GenerateDataKeyPairRequest = (input, context) => {
    return take(input, {
        DryRun: [],
        EncryptionContext: _json,
        GrantTokens: _json,
        KeyId: [],
        KeyPairSpec: [],
        Recipient: (_) => se_RecipientInfo(_, context),
    });
};
const se_GenerateDataKeyRequest = (input, context) => {
    return take(input, {
        DryRun: [],
        EncryptionContext: _json,
        GrantTokens: _json,
        KeyId: [],
        KeySpec: [],
        NumberOfBytes: [],
        Recipient: (_) => se_RecipientInfo(_, context),
    });
};
const se_GenerateMacRequest = (input, context) => {
    return take(input, {
        DryRun: [],
        GrantTokens: _json,
        KeyId: [],
        MacAlgorithm: [],
        Message: context.base64Encoder,
    });
};
const se_GenerateRandomRequest = (input, context) => {
    return take(input, {
        CustomKeyStoreId: [],
        NumberOfBytes: [],
        Recipient: (_) => se_RecipientInfo(_, context),
    });
};
const se_ImportKeyMaterialRequest = (input, context) => {
    return take(input, {
        EncryptedKeyMaterial: context.base64Encoder,
        ExpirationModel: [],
        ImportToken: context.base64Encoder,
        ImportType: [],
        KeyId: [],
        KeyMaterialDescription: [],
        KeyMaterialId: [],
        ValidTo: (_) => _.getTime() / 1_000,
    });
};
const se_RecipientInfo = (input, context) => {
    return take(input, {
        AttestationDocument: context.base64Encoder,
        KeyEncryptionAlgorithm: [],
    });
};
const se_ReEncryptRequest = (input, context) => {
    return take(input, {
        CiphertextBlob: context.base64Encoder,
        DestinationEncryptionAlgorithm: [],
        DestinationEncryptionContext: _json,
        DestinationKeyId: [],
        DryRun: [],
        GrantTokens: _json,
        SourceEncryptionAlgorithm: [],
        SourceEncryptionContext: _json,
        SourceKeyId: [],
    });
};
const se_SignRequest = (input, context) => {
    return take(input, {
        DryRun: [],
        GrantTokens: _json,
        KeyId: [],
        Message: context.base64Encoder,
        MessageType: [],
        SigningAlgorithm: [],
    });
};
const se_VerifyMacRequest = (input, context) => {
    return take(input, {
        DryRun: [],
        GrantTokens: _json,
        KeyId: [],
        Mac: context.base64Encoder,
        MacAlgorithm: [],
        Message: context.base64Encoder,
    });
};
const se_VerifyRequest = (input, context) => {
    return take(input, {
        DryRun: [],
        GrantTokens: _json,
        KeyId: [],
        Message: context.base64Encoder,
        MessageType: [],
        Signature: context.base64Encoder,
        SigningAlgorithm: [],
    });
};
const de_AliasList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_AliasListEntry(entry, context);
    });
    return retVal;
};
const de_AliasListEntry = (output, context) => {
    return take(output, {
        AliasArn: __expectString,
        AliasName: __expectString,
        CreationDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        LastUpdatedDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        TargetKeyId: __expectString,
    });
};
const de_CreateKeyResponse = (output, context) => {
    return take(output, {
        KeyMetadata: (_) => de_KeyMetadata(_, context),
    });
};
const de_CustomKeyStoresList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_CustomKeyStoresListEntry(entry, context);
    });
    return retVal;
};
const de_CustomKeyStoresListEntry = (output, context) => {
    return take(output, {
        CloudHsmClusterId: __expectString,
        ConnectionErrorCode: __expectString,
        ConnectionState: __expectString,
        CreationDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        CustomKeyStoreId: __expectString,
        CustomKeyStoreName: __expectString,
        CustomKeyStoreType: __expectString,
        TrustAnchorCertificate: __expectString,
        XksProxyConfiguration: _json,
    });
};
const de_DecryptResponse = (output, context) => {
    return take(output, {
        CiphertextForRecipient: context.base64Decoder,
        EncryptionAlgorithm: __expectString,
        KeyId: __expectString,
        KeyMaterialId: __expectString,
        Plaintext: context.base64Decoder,
    });
};
const de_DeriveSharedSecretResponse = (output, context) => {
    return take(output, {
        CiphertextForRecipient: context.base64Decoder,
        KeyAgreementAlgorithm: __expectString,
        KeyId: __expectString,
        KeyOrigin: __expectString,
        SharedSecret: context.base64Decoder,
    });
};
const de_DescribeCustomKeyStoresResponse = (output, context) => {
    return take(output, {
        CustomKeyStores: (_) => de_CustomKeyStoresList(_, context),
        NextMarker: __expectString,
        Truncated: __expectBoolean,
    });
};
const de_DescribeKeyResponse = (output, context) => {
    return take(output, {
        KeyMetadata: (_) => de_KeyMetadata(_, context),
    });
};
const de_EncryptResponse = (output, context) => {
    return take(output, {
        CiphertextBlob: context.base64Decoder,
        EncryptionAlgorithm: __expectString,
        KeyId: __expectString,
    });
};
const de_GenerateDataKeyPairResponse = (output, context) => {
    return take(output, {
        CiphertextForRecipient: context.base64Decoder,
        KeyId: __expectString,
        KeyMaterialId: __expectString,
        KeyPairSpec: __expectString,
        PrivateKeyCiphertextBlob: context.base64Decoder,
        PrivateKeyPlaintext: context.base64Decoder,
        PublicKey: context.base64Decoder,
    });
};
const de_GenerateDataKeyPairWithoutPlaintextResponse = (output, context) => {
    return take(output, {
        KeyId: __expectString,
        KeyMaterialId: __expectString,
        KeyPairSpec: __expectString,
        PrivateKeyCiphertextBlob: context.base64Decoder,
        PublicKey: context.base64Decoder,
    });
};
const de_GenerateDataKeyResponse = (output, context) => {
    return take(output, {
        CiphertextBlob: context.base64Decoder,
        CiphertextForRecipient: context.base64Decoder,
        KeyId: __expectString,
        KeyMaterialId: __expectString,
        Plaintext: context.base64Decoder,
    });
};
const de_GenerateDataKeyWithoutPlaintextResponse = (output, context) => {
    return take(output, {
        CiphertextBlob: context.base64Decoder,
        KeyId: __expectString,
        KeyMaterialId: __expectString,
    });
};
const de_GenerateMacResponse = (output, context) => {
    return take(output, {
        KeyId: __expectString,
        Mac: context.base64Decoder,
        MacAlgorithm: __expectString,
    });
};
const de_GenerateRandomResponse = (output, context) => {
    return take(output, {
        CiphertextForRecipient: context.base64Decoder,
        Plaintext: context.base64Decoder,
    });
};
const de_GetKeyRotationStatusResponse = (output, context) => {
    return take(output, {
        KeyId: __expectString,
        KeyRotationEnabled: __expectBoolean,
        NextRotationDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        OnDemandRotationStartDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        RotationPeriodInDays: __expectInt32,
    });
};
const de_GetParametersForImportResponse = (output, context) => {
    return take(output, {
        ImportToken: context.base64Decoder,
        KeyId: __expectString,
        ParametersValidTo: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        PublicKey: context.base64Decoder,
    });
};
const de_GetPublicKeyResponse = (output, context) => {
    return take(output, {
        CustomerMasterKeySpec: __expectString,
        EncryptionAlgorithms: _json,
        KeyAgreementAlgorithms: _json,
        KeyId: __expectString,
        KeySpec: __expectString,
        KeyUsage: __expectString,
        PublicKey: context.base64Decoder,
        SigningAlgorithms: _json,
    });
};
const de_GrantList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_GrantListEntry(entry, context);
    });
    return retVal;
};
const de_GrantListEntry = (output, context) => {
    return take(output, {
        Constraints: _json,
        CreationDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        GrantId: __expectString,
        GranteePrincipal: __expectString,
        IssuingAccount: __expectString,
        KeyId: __expectString,
        Name: __expectString,
        Operations: _json,
        RetiringPrincipal: __expectString,
    });
};
const de_KeyMetadata = (output, context) => {
    return take(output, {
        AWSAccountId: __expectString,
        Arn: __expectString,
        CloudHsmClusterId: __expectString,
        CreationDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        CurrentKeyMaterialId: __expectString,
        CustomKeyStoreId: __expectString,
        CustomerMasterKeySpec: __expectString,
        DeletionDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        Description: __expectString,
        Enabled: __expectBoolean,
        EncryptionAlgorithms: _json,
        ExpirationModel: __expectString,
        KeyAgreementAlgorithms: _json,
        KeyId: __expectString,
        KeyManager: __expectString,
        KeySpec: __expectString,
        KeyState: __expectString,
        KeyUsage: __expectString,
        MacAlgorithms: _json,
        MultiRegion: __expectBoolean,
        MultiRegionConfiguration: _json,
        Origin: __expectString,
        PendingDeletionWindowInDays: __expectInt32,
        SigningAlgorithms: _json,
        ValidTo: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        XksKeyConfiguration: _json,
    });
};
const de_ListAliasesResponse = (output, context) => {
    return take(output, {
        Aliases: (_) => de_AliasList(_, context),
        NextMarker: __expectString,
        Truncated: __expectBoolean,
    });
};
const de_ListGrantsResponse = (output, context) => {
    return take(output, {
        Grants: (_) => de_GrantList(_, context),
        NextMarker: __expectString,
        Truncated: __expectBoolean,
    });
};
const de_ListKeyRotationsResponse = (output, context) => {
    return take(output, {
        NextMarker: __expectString,
        Rotations: (_) => de_RotationsList(_, context),
        Truncated: __expectBoolean,
    });
};
const de_ReEncryptResponse = (output, context) => {
    return take(output, {
        CiphertextBlob: context.base64Decoder,
        DestinationEncryptionAlgorithm: __expectString,
        DestinationKeyMaterialId: __expectString,
        KeyId: __expectString,
        SourceEncryptionAlgorithm: __expectString,
        SourceKeyId: __expectString,
        SourceKeyMaterialId: __expectString,
    });
};
const de_ReplicateKeyResponse = (output, context) => {
    return take(output, {
        ReplicaKeyMetadata: (_) => de_KeyMetadata(_, context),
        ReplicaPolicy: __expectString,
        ReplicaTags: _json,
    });
};
const de_RotationsList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_RotationsListEntry(entry, context);
    });
    return retVal;
};
const de_RotationsListEntry = (output, context) => {
    return take(output, {
        ExpirationModel: __expectString,
        ImportState: __expectString,
        KeyId: __expectString,
        KeyMaterialDescription: __expectString,
        KeyMaterialId: __expectString,
        KeyMaterialState: __expectString,
        RotationDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        RotationType: __expectString,
        ValidTo: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
    });
};
const de_ScheduleKeyDeletionResponse = (output, context) => {
    return take(output, {
        DeletionDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        KeyId: __expectString,
        KeyState: __expectString,
        PendingWindowInDays: __expectInt32,
    });
};
const de_SignResponse = (output, context) => {
    return take(output, {
        KeyId: __expectString,
        Signature: context.base64Decoder,
        SigningAlgorithm: __expectString,
    });
};
const deserializeMetadata = (output) => ({
    httpStatusCode: output.statusCode,
    requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
    extendedRequestId: output.headers["x-amz-id-2"],
    cfId: output.headers["x-amz-cf-id"],
});
const collectBodyString = (streamBody, context) => collectBody(streamBody, context).then((body) => context.utf8Encoder(body));
const throwDefaultError = withBaseException(__BaseException);
const buildHttpRpcRequest = async (context, headers, path, resolvedHostname, body) => {
    const { hostname, protocol = "https", port, path: basePath } = await context.endpoint();
    const contents = {
        protocol,
        hostname,
        port,
        method: "POST",
        path: basePath.endsWith("/") ? basePath.slice(0, -1) + path : basePath + path,
        headers,
    };
    if (resolvedHostname !== undefined) {
        contents.hostname = resolvedHostname;
    }
    if (body !== undefined) {
        contents.body = body;
    }
    return new __HttpRequest(contents);
};
function sharedHeaders(operation) {
    return {
        "content-type": "application/x-amz-json-1.1",
        "x-amz-target": `TrentService.${operation}`,
    };
}
