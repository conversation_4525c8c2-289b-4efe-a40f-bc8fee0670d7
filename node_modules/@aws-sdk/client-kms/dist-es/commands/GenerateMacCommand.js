import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { GenerateMacRequestFilterSensitiveLog } from "../models/models_0";
import { de_GenerateMacCommand, se_GenerateMacCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class GenerateMacCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("TrentService", "GenerateMac", {})
    .n("KMSClient", "GenerateMacCommand")
    .f(GenerateMacRequestFilterSensitiveLog, void 0)
    .ser(se_GenerateMacCommand)
    .de(de_GenerateMacCommand)
    .build() {
}
