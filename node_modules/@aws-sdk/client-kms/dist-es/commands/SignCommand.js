import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { SignRequestFilterSensitiveLog } from "../models/models_0";
import { de_SignCommand, se_SignCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class SignCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("TrentService", "Sign", {})
    .n("KMSClient", "SignCommand")
    .f(SignRequestFilterSensitiveLog, void 0)
    .ser(se_SignCommand)
    .de(de_SignCommand)
    .build() {
}
