import { SENSITIVE_STRING } from "@smithy/smithy-client";
import { KMSServiceException as __BaseException } from "./KMSServiceException";
export const AlgorithmSpec = {
    RSAES_OAEP_SHA_1: "RSAES_OAEP_SHA_1",
    RSAES_OAEP_SHA_256: "RSAES_OAEP_SHA_256",
    RSAES_PKCS1_V1_5: "RSAES_PKCS1_V1_5",
    RSA_AES_KEY_WRAP_SHA_1: "RSA_AES_KEY_WRAP_SHA_1",
    RSA_AES_KEY_WRAP_SHA_256: "RSA_AES_KEY_WRAP_SHA_256",
    SM2PKE: "SM2PKE",
};
export class AlreadyExistsException extends __BaseException {
    name = "AlreadyExistsException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "AlreadyExistsException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, AlreadyExistsException.prototype);
    }
}
export class DependencyTimeoutException extends __BaseException {
    name = "DependencyTimeoutException";
    $fault = "server";
    constructor(opts) {
        super({
            name: "DependencyTimeoutException",
            $fault: "server",
            ...opts,
        });
        Object.setPrototypeOf(this, DependencyTimeoutException.prototype);
    }
}
export class InvalidArnException extends __BaseException {
    name = "InvalidArnException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "InvalidArnException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, InvalidArnException.prototype);
    }
}
export class KMSInternalException extends __BaseException {
    name = "KMSInternalException";
    $fault = "server";
    constructor(opts) {
        super({
            name: "KMSInternalException",
            $fault: "server",
            ...opts,
        });
        Object.setPrototypeOf(this, KMSInternalException.prototype);
    }
}
export class KMSInvalidStateException extends __BaseException {
    name = "KMSInvalidStateException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "KMSInvalidStateException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, KMSInvalidStateException.prototype);
    }
}
export class NotFoundException extends __BaseException {
    name = "NotFoundException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "NotFoundException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, NotFoundException.prototype);
    }
}
export class CloudHsmClusterInUseException extends __BaseException {
    name = "CloudHsmClusterInUseException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "CloudHsmClusterInUseException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, CloudHsmClusterInUseException.prototype);
    }
}
export class CloudHsmClusterInvalidConfigurationException extends __BaseException {
    name = "CloudHsmClusterInvalidConfigurationException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "CloudHsmClusterInvalidConfigurationException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, CloudHsmClusterInvalidConfigurationException.prototype);
    }
}
export class CloudHsmClusterNotActiveException extends __BaseException {
    name = "CloudHsmClusterNotActiveException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "CloudHsmClusterNotActiveException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, CloudHsmClusterNotActiveException.prototype);
    }
}
export class CloudHsmClusterNotFoundException extends __BaseException {
    name = "CloudHsmClusterNotFoundException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "CloudHsmClusterNotFoundException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, CloudHsmClusterNotFoundException.prototype);
    }
}
export class CloudHsmClusterNotRelatedException extends __BaseException {
    name = "CloudHsmClusterNotRelatedException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "CloudHsmClusterNotRelatedException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, CloudHsmClusterNotRelatedException.prototype);
    }
}
export class ConflictException extends __BaseException {
    name = "ConflictException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ConflictException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ConflictException.prototype);
    }
}
export class CustomKeyStoreInvalidStateException extends __BaseException {
    name = "CustomKeyStoreInvalidStateException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "CustomKeyStoreInvalidStateException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, CustomKeyStoreInvalidStateException.prototype);
    }
}
export class CustomKeyStoreNotFoundException extends __BaseException {
    name = "CustomKeyStoreNotFoundException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "CustomKeyStoreNotFoundException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, CustomKeyStoreNotFoundException.prototype);
    }
}
export const ConnectionErrorCodeType = {
    CLUSTER_NOT_FOUND: "CLUSTER_NOT_FOUND",
    INSUFFICIENT_CLOUDHSM_HSMS: "INSUFFICIENT_CLOUDHSM_HSMS",
    INSUFFICIENT_FREE_ADDRESSES_IN_SUBNET: "INSUFFICIENT_FREE_ADDRESSES_IN_SUBNET",
    INTERNAL_ERROR: "INTERNAL_ERROR",
    INVALID_CREDENTIALS: "INVALID_CREDENTIALS",
    NETWORK_ERRORS: "NETWORK_ERRORS",
    SUBNET_NOT_FOUND: "SUBNET_NOT_FOUND",
    USER_LOCKED_OUT: "USER_LOCKED_OUT",
    USER_LOGGED_IN: "USER_LOGGED_IN",
    USER_NOT_FOUND: "USER_NOT_FOUND",
    XKS_PROXY_ACCESS_DENIED: "XKS_PROXY_ACCESS_DENIED",
    XKS_PROXY_INVALID_CONFIGURATION: "XKS_PROXY_INVALID_CONFIGURATION",
    XKS_PROXY_INVALID_RESPONSE: "XKS_PROXY_INVALID_RESPONSE",
    XKS_PROXY_INVALID_TLS_CONFIGURATION: "XKS_PROXY_INVALID_TLS_CONFIGURATION",
    XKS_PROXY_NOT_REACHABLE: "XKS_PROXY_NOT_REACHABLE",
    XKS_PROXY_TIMED_OUT: "XKS_PROXY_TIMED_OUT",
    XKS_VPC_ENDPOINT_SERVICE_INVALID_CONFIGURATION: "XKS_VPC_ENDPOINT_SERVICE_INVALID_CONFIGURATION",
    XKS_VPC_ENDPOINT_SERVICE_NOT_FOUND: "XKS_VPC_ENDPOINT_SERVICE_NOT_FOUND",
};
export const ConnectionStateType = {
    CONNECTED: "CONNECTED",
    CONNECTING: "CONNECTING",
    DISCONNECTED: "DISCONNECTED",
    DISCONNECTING: "DISCONNECTING",
    FAILED: "FAILED",
};
export class InvalidAliasNameException extends __BaseException {
    name = "InvalidAliasNameException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "InvalidAliasNameException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, InvalidAliasNameException.prototype);
    }
}
export class LimitExceededException extends __BaseException {
    name = "LimitExceededException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "LimitExceededException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, LimitExceededException.prototype);
    }
}
export const CustomKeyStoreType = {
    AWS_CLOUDHSM: "AWS_CLOUDHSM",
    EXTERNAL_KEY_STORE: "EXTERNAL_KEY_STORE",
};
export const XksProxyConnectivityType = {
    PUBLIC_ENDPOINT: "PUBLIC_ENDPOINT",
    VPC_ENDPOINT_SERVICE: "VPC_ENDPOINT_SERVICE",
};
export class CustomKeyStoreNameInUseException extends __BaseException {
    name = "CustomKeyStoreNameInUseException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "CustomKeyStoreNameInUseException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, CustomKeyStoreNameInUseException.prototype);
    }
}
export class IncorrectTrustAnchorException extends __BaseException {
    name = "IncorrectTrustAnchorException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "IncorrectTrustAnchorException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, IncorrectTrustAnchorException.prototype);
    }
}
export class XksProxyIncorrectAuthenticationCredentialException extends __BaseException {
    name = "XksProxyIncorrectAuthenticationCredentialException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksProxyIncorrectAuthenticationCredentialException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksProxyIncorrectAuthenticationCredentialException.prototype);
    }
}
export class XksProxyInvalidConfigurationException extends __BaseException {
    name = "XksProxyInvalidConfigurationException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksProxyInvalidConfigurationException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksProxyInvalidConfigurationException.prototype);
    }
}
export class XksProxyInvalidResponseException extends __BaseException {
    name = "XksProxyInvalidResponseException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksProxyInvalidResponseException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksProxyInvalidResponseException.prototype);
    }
}
export class XksProxyUriEndpointInUseException extends __BaseException {
    name = "XksProxyUriEndpointInUseException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksProxyUriEndpointInUseException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksProxyUriEndpointInUseException.prototype);
    }
}
export class XksProxyUriInUseException extends __BaseException {
    name = "XksProxyUriInUseException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksProxyUriInUseException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksProxyUriInUseException.prototype);
    }
}
export class XksProxyUriUnreachableException extends __BaseException {
    name = "XksProxyUriUnreachableException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksProxyUriUnreachableException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksProxyUriUnreachableException.prototype);
    }
}
export class XksProxyVpcEndpointServiceInUseException extends __BaseException {
    name = "XksProxyVpcEndpointServiceInUseException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksProxyVpcEndpointServiceInUseException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksProxyVpcEndpointServiceInUseException.prototype);
    }
}
export class XksProxyVpcEndpointServiceInvalidConfigurationException extends __BaseException {
    name = "XksProxyVpcEndpointServiceInvalidConfigurationException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksProxyVpcEndpointServiceInvalidConfigurationException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksProxyVpcEndpointServiceInvalidConfigurationException.prototype);
    }
}
export class XksProxyVpcEndpointServiceNotFoundException extends __BaseException {
    name = "XksProxyVpcEndpointServiceNotFoundException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksProxyVpcEndpointServiceNotFoundException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksProxyVpcEndpointServiceNotFoundException.prototype);
    }
}
export const GrantOperation = {
    CreateGrant: "CreateGrant",
    Decrypt: "Decrypt",
    DeriveSharedSecret: "DeriveSharedSecret",
    DescribeKey: "DescribeKey",
    Encrypt: "Encrypt",
    GenerateDataKey: "GenerateDataKey",
    GenerateDataKeyPair: "GenerateDataKeyPair",
    GenerateDataKeyPairWithoutPlaintext: "GenerateDataKeyPairWithoutPlaintext",
    GenerateDataKeyWithoutPlaintext: "GenerateDataKeyWithoutPlaintext",
    GenerateMac: "GenerateMac",
    GetPublicKey: "GetPublicKey",
    ReEncryptFrom: "ReEncryptFrom",
    ReEncryptTo: "ReEncryptTo",
    RetireGrant: "RetireGrant",
    Sign: "Sign",
    Verify: "Verify",
    VerifyMac: "VerifyMac",
};
export class DisabledException extends __BaseException {
    name = "DisabledException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "DisabledException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, DisabledException.prototype);
    }
}
export class DryRunOperationException extends __BaseException {
    name = "DryRunOperationException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "DryRunOperationException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, DryRunOperationException.prototype);
    }
}
export class InvalidGrantTokenException extends __BaseException {
    name = "InvalidGrantTokenException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "InvalidGrantTokenException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, InvalidGrantTokenException.prototype);
    }
}
export const CustomerMasterKeySpec = {
    ECC_NIST_P256: "ECC_NIST_P256",
    ECC_NIST_P384: "ECC_NIST_P384",
    ECC_NIST_P521: "ECC_NIST_P521",
    ECC_SECG_P256K1: "ECC_SECG_P256K1",
    HMAC_224: "HMAC_224",
    HMAC_256: "HMAC_256",
    HMAC_384: "HMAC_384",
    HMAC_512: "HMAC_512",
    RSA_2048: "RSA_2048",
    RSA_3072: "RSA_3072",
    RSA_4096: "RSA_4096",
    SM2: "SM2",
    SYMMETRIC_DEFAULT: "SYMMETRIC_DEFAULT",
};
export const KeySpec = {
    ECC_NIST_P256: "ECC_NIST_P256",
    ECC_NIST_P384: "ECC_NIST_P384",
    ECC_NIST_P521: "ECC_NIST_P521",
    ECC_SECG_P256K1: "ECC_SECG_P256K1",
    HMAC_224: "HMAC_224",
    HMAC_256: "HMAC_256",
    HMAC_384: "HMAC_384",
    HMAC_512: "HMAC_512",
    ML_DSA_44: "ML_DSA_44",
    ML_DSA_65: "ML_DSA_65",
    ML_DSA_87: "ML_DSA_87",
    RSA_2048: "RSA_2048",
    RSA_3072: "RSA_3072",
    RSA_4096: "RSA_4096",
    SM2: "SM2",
    SYMMETRIC_DEFAULT: "SYMMETRIC_DEFAULT",
};
export const KeyUsageType = {
    ENCRYPT_DECRYPT: "ENCRYPT_DECRYPT",
    GENERATE_VERIFY_MAC: "GENERATE_VERIFY_MAC",
    KEY_AGREEMENT: "KEY_AGREEMENT",
    SIGN_VERIFY: "SIGN_VERIFY",
};
export const OriginType = {
    AWS_CLOUDHSM: "AWS_CLOUDHSM",
    AWS_KMS: "AWS_KMS",
    EXTERNAL: "EXTERNAL",
    EXTERNAL_KEY_STORE: "EXTERNAL_KEY_STORE",
};
export const EncryptionAlgorithmSpec = {
    RSAES_OAEP_SHA_1: "RSAES_OAEP_SHA_1",
    RSAES_OAEP_SHA_256: "RSAES_OAEP_SHA_256",
    SM2PKE: "SM2PKE",
    SYMMETRIC_DEFAULT: "SYMMETRIC_DEFAULT",
};
export const ExpirationModelType = {
    KEY_MATERIAL_DOES_NOT_EXPIRE: "KEY_MATERIAL_DOES_NOT_EXPIRE",
    KEY_MATERIAL_EXPIRES: "KEY_MATERIAL_EXPIRES",
};
export const KeyAgreementAlgorithmSpec = {
    ECDH: "ECDH",
};
export const KeyManagerType = {
    AWS: "AWS",
    CUSTOMER: "CUSTOMER",
};
export const KeyState = {
    Creating: "Creating",
    Disabled: "Disabled",
    Enabled: "Enabled",
    PendingDeletion: "PendingDeletion",
    PendingImport: "PendingImport",
    PendingReplicaDeletion: "PendingReplicaDeletion",
    Unavailable: "Unavailable",
    Updating: "Updating",
};
export const MacAlgorithmSpec = {
    HMAC_SHA_224: "HMAC_SHA_224",
    HMAC_SHA_256: "HMAC_SHA_256",
    HMAC_SHA_384: "HMAC_SHA_384",
    HMAC_SHA_512: "HMAC_SHA_512",
};
export const MultiRegionKeyType = {
    PRIMARY: "PRIMARY",
    REPLICA: "REPLICA",
};
export const SigningAlgorithmSpec = {
    ECDSA_SHA_256: "ECDSA_SHA_256",
    ECDSA_SHA_384: "ECDSA_SHA_384",
    ECDSA_SHA_512: "ECDSA_SHA_512",
    ML_DSA_SHAKE_256: "ML_DSA_SHAKE_256",
    RSASSA_PKCS1_V1_5_SHA_256: "RSASSA_PKCS1_V1_5_SHA_256",
    RSASSA_PKCS1_V1_5_SHA_384: "RSASSA_PKCS1_V1_5_SHA_384",
    RSASSA_PKCS1_V1_5_SHA_512: "RSASSA_PKCS1_V1_5_SHA_512",
    RSASSA_PSS_SHA_256: "RSASSA_PSS_SHA_256",
    RSASSA_PSS_SHA_384: "RSASSA_PSS_SHA_384",
    RSASSA_PSS_SHA_512: "RSASSA_PSS_SHA_512",
    SM2DSA: "SM2DSA",
};
export class MalformedPolicyDocumentException extends __BaseException {
    name = "MalformedPolicyDocumentException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "MalformedPolicyDocumentException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, MalformedPolicyDocumentException.prototype);
    }
}
export class TagException extends __BaseException {
    name = "TagException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "TagException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, TagException.prototype);
    }
}
export class UnsupportedOperationException extends __BaseException {
    name = "UnsupportedOperationException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "UnsupportedOperationException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, UnsupportedOperationException.prototype);
    }
}
export class XksKeyAlreadyInUseException extends __BaseException {
    name = "XksKeyAlreadyInUseException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksKeyAlreadyInUseException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksKeyAlreadyInUseException.prototype);
    }
}
export class XksKeyInvalidConfigurationException extends __BaseException {
    name = "XksKeyInvalidConfigurationException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksKeyInvalidConfigurationException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksKeyInvalidConfigurationException.prototype);
    }
}
export class XksKeyNotFoundException extends __BaseException {
    name = "XksKeyNotFoundException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "XksKeyNotFoundException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, XksKeyNotFoundException.prototype);
    }
}
export class CustomKeyStoreHasCMKsException extends __BaseException {
    name = "CustomKeyStoreHasCMKsException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "CustomKeyStoreHasCMKsException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, CustomKeyStoreHasCMKsException.prototype);
    }
}
export const DataKeyPairSpec = {
    ECC_NIST_P256: "ECC_NIST_P256",
    ECC_NIST_P384: "ECC_NIST_P384",
    ECC_NIST_P521: "ECC_NIST_P521",
    ECC_SECG_P256K1: "ECC_SECG_P256K1",
    RSA_2048: "RSA_2048",
    RSA_3072: "RSA_3072",
    RSA_4096: "RSA_4096",
    SM2: "SM2",
};
export const DataKeySpec = {
    AES_128: "AES_128",
    AES_256: "AES_256",
};
export const KeyEncryptionMechanism = {
    RSAES_OAEP_SHA_256: "RSAES_OAEP_SHA_256",
};
export class IncorrectKeyException extends __BaseException {
    name = "IncorrectKeyException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "IncorrectKeyException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, IncorrectKeyException.prototype);
    }
}
export class InvalidCiphertextException extends __BaseException {
    name = "InvalidCiphertextException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "InvalidCiphertextException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, InvalidCiphertextException.prototype);
    }
}
export class InvalidKeyUsageException extends __BaseException {
    name = "InvalidKeyUsageException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "InvalidKeyUsageException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, InvalidKeyUsageException.prototype);
    }
}
export class KeyUnavailableException extends __BaseException {
    name = "KeyUnavailableException";
    $fault = "server";
    constructor(opts) {
        super({
            name: "KeyUnavailableException",
            $fault: "server",
            ...opts,
        });
        Object.setPrototypeOf(this, KeyUnavailableException.prototype);
    }
}
export class InvalidMarkerException extends __BaseException {
    name = "InvalidMarkerException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "InvalidMarkerException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, InvalidMarkerException.prototype);
    }
}
export class ExpiredImportTokenException extends __BaseException {
    name = "ExpiredImportTokenException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ExpiredImportTokenException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ExpiredImportTokenException.prototype);
    }
}
export const WrappingKeySpec = {
    RSA_2048: "RSA_2048",
    RSA_3072: "RSA_3072",
    RSA_4096: "RSA_4096",
    SM2: "SM2",
};
export const ImportType = {
    EXISTING_KEY_MATERIAL: "EXISTING_KEY_MATERIAL",
    NEW_KEY_MATERIAL: "NEW_KEY_MATERIAL",
};
export class IncorrectKeyMaterialException extends __BaseException {
    name = "IncorrectKeyMaterialException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "IncorrectKeyMaterialException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, IncorrectKeyMaterialException.prototype);
    }
}
export class InvalidImportTokenException extends __BaseException {
    name = "InvalidImportTokenException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "InvalidImportTokenException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, InvalidImportTokenException.prototype);
    }
}
export const ImportState = {
    IMPORTED: "IMPORTED",
    PENDING_IMPORT: "PENDING_IMPORT",
};
export const IncludeKeyMaterial = {
    ALL_KEY_MATERIAL: "ALL_KEY_MATERIAL",
    ROTATIONS_ONLY: "ROTATIONS_ONLY",
};
export class InvalidGrantIdException extends __BaseException {
    name = "InvalidGrantIdException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "InvalidGrantIdException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, InvalidGrantIdException.prototype);
    }
}
export const KeyMaterialState = {
    CURRENT: "CURRENT",
    NON_CURRENT: "NON_CURRENT",
    PENDING_ROTATION: "PENDING_ROTATION",
};
export class KMSInvalidMacException extends __BaseException {
    name = "KMSInvalidMacException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "KMSInvalidMacException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, KMSInvalidMacException.prototype);
    }
}
export class KMSInvalidSignatureException extends __BaseException {
    name = "KMSInvalidSignatureException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "KMSInvalidSignatureException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, KMSInvalidSignatureException.prototype);
    }
}
export const RotationType = {
    AUTOMATIC: "AUTOMATIC",
    ON_DEMAND: "ON_DEMAND",
};
export const MessageType = {
    DIGEST: "DIGEST",
    EXTERNAL_MU: "EXTERNAL_MU",
    RAW: "RAW",
};
export const XksProxyAuthenticationCredentialTypeFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.AccessKeyId && { AccessKeyId: SENSITIVE_STRING }),
    ...(obj.RawSecretAccessKey && { RawSecretAccessKey: SENSITIVE_STRING }),
});
export const CreateCustomKeyStoreRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.KeyStorePassword && { KeyStorePassword: SENSITIVE_STRING }),
    ...(obj.XksProxyAuthenticationCredential && {
        XksProxyAuthenticationCredential: XksProxyAuthenticationCredentialTypeFilterSensitiveLog(obj.XksProxyAuthenticationCredential),
    }),
});
export const XksProxyConfigurationTypeFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.AccessKeyId && { AccessKeyId: SENSITIVE_STRING }),
});
export const CustomKeyStoresListEntryFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.XksProxyConfiguration && {
        XksProxyConfiguration: XksProxyConfigurationTypeFilterSensitiveLog(obj.XksProxyConfiguration),
    }),
});
export const DecryptResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Plaintext && { Plaintext: SENSITIVE_STRING }),
});
export const DeriveSharedSecretResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.SharedSecret && { SharedSecret: SENSITIVE_STRING }),
});
export const DescribeCustomKeyStoresResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.CustomKeyStores && {
        CustomKeyStores: obj.CustomKeyStores.map((item) => CustomKeyStoresListEntryFilterSensitiveLog(item)),
    }),
});
export const EncryptRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Plaintext && { Plaintext: SENSITIVE_STRING }),
});
export const GenerateDataKeyResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Plaintext && { Plaintext: SENSITIVE_STRING }),
});
export const GenerateDataKeyPairResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.PrivateKeyPlaintext && { PrivateKeyPlaintext: SENSITIVE_STRING }),
});
export const GenerateMacRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Message && { Message: SENSITIVE_STRING }),
});
export const GenerateRandomResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Plaintext && { Plaintext: SENSITIVE_STRING }),
});
export const GetParametersForImportResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.PublicKey && { PublicKey: SENSITIVE_STRING }),
});
export const SignRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Message && { Message: SENSITIVE_STRING }),
});
export const UpdateCustomKeyStoreRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.KeyStorePassword && { KeyStorePassword: SENSITIVE_STRING }),
    ...(obj.XksProxyAuthenticationCredential && {
        XksProxyAuthenticationCredential: XksProxyAuthenticationCredentialTypeFilterSensitiveLog(obj.XksProxyAuthenticationCredential),
    }),
});
export const VerifyRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Message && { Message: SENSITIVE_STRING }),
});
export const VerifyMacRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Message && { Message: SENSITIVE_STRING }),
});
