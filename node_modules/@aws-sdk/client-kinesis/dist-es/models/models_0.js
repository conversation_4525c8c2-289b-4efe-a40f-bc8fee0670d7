import { KinesisServiceException as __BaseException } from "./KinesisServiceException";
export class AccessDeniedException extends __BaseException {
    name = "AccessDeniedException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "AccessDeniedException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, AccessDeniedException.prototype);
    }
}
export class InvalidArgumentException extends __BaseException {
    name = "InvalidArgumentException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "InvalidArgumentException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, InvalidArgumentException.prototype);
    }
}
export class LimitExceededException extends __BaseException {
    name = "LimitExceededException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "LimitExceededException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, LimitExceededException.prototype);
    }
}
export class ResourceInUseException extends __BaseException {
    name = "ResourceInUseException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ResourceInUseException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ResourceInUseException.prototype);
    }
}
export class ResourceNotFoundException extends __BaseException {
    name = "ResourceNotFoundException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ResourceNotFoundException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ResourceNotFoundException.prototype);
    }
}
export const ConsumerStatus = {
    ACTIVE: "ACTIVE",
    CREATING: "CREATING",
    DELETING: "DELETING",
};
export const StreamMode = {
    ON_DEMAND: "ON_DEMAND",
    PROVISIONED: "PROVISIONED",
};
export const EncryptionType = {
    KMS: "KMS",
    NONE: "NONE",
};
export const MetricsName = {
    ALL: "ALL",
    INCOMING_BYTES: "IncomingBytes",
    INCOMING_RECORDS: "IncomingRecords",
    ITERATOR_AGE_MILLISECONDS: "IteratorAgeMilliseconds",
    OUTGOING_BYTES: "OutgoingBytes",
    OUTGOING_RECORDS: "OutgoingRecords",
    READ_PROVISIONED_THROUGHPUT_EXCEEDED: "ReadProvisionedThroughputExceeded",
    WRITE_PROVISIONED_THROUGHPUT_EXCEEDED: "WriteProvisionedThroughputExceeded",
};
export const StreamStatus = {
    ACTIVE: "ACTIVE",
    CREATING: "CREATING",
    DELETING: "DELETING",
    UPDATING: "UPDATING",
};
export class ExpiredIteratorException extends __BaseException {
    name = "ExpiredIteratorException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ExpiredIteratorException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ExpiredIteratorException.prototype);
    }
}
export class ExpiredNextTokenException extends __BaseException {
    name = "ExpiredNextTokenException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ExpiredNextTokenException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ExpiredNextTokenException.prototype);
    }
}
export class InternalFailureException extends __BaseException {
    name = "InternalFailureException";
    $fault = "server";
    constructor(opts) {
        super({
            name: "InternalFailureException",
            $fault: "server",
            ...opts,
        });
        Object.setPrototypeOf(this, InternalFailureException.prototype);
    }
}
export class KMSAccessDeniedException extends __BaseException {
    name = "KMSAccessDeniedException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "KMSAccessDeniedException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, KMSAccessDeniedException.prototype);
    }
}
export class KMSDisabledException extends __BaseException {
    name = "KMSDisabledException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "KMSDisabledException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, KMSDisabledException.prototype);
    }
}
export class KMSInvalidStateException extends __BaseException {
    name = "KMSInvalidStateException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "KMSInvalidStateException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, KMSInvalidStateException.prototype);
    }
}
export class KMSNotFoundException extends __BaseException {
    name = "KMSNotFoundException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "KMSNotFoundException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, KMSNotFoundException.prototype);
    }
}
export class KMSOptInRequired extends __BaseException {
    name = "KMSOptInRequired";
    $fault = "client";
    constructor(opts) {
        super({
            name: "KMSOptInRequired",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, KMSOptInRequired.prototype);
    }
}
export class KMSThrottlingException extends __BaseException {
    name = "KMSThrottlingException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "KMSThrottlingException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, KMSThrottlingException.prototype);
    }
}
export class ProvisionedThroughputExceededException extends __BaseException {
    name = "ProvisionedThroughputExceededException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ProvisionedThroughputExceededException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ProvisionedThroughputExceededException.prototype);
    }
}
export const ShardIteratorType = {
    AFTER_SEQUENCE_NUMBER: "AFTER_SEQUENCE_NUMBER",
    AT_SEQUENCE_NUMBER: "AT_SEQUENCE_NUMBER",
    AT_TIMESTAMP: "AT_TIMESTAMP",
    LATEST: "LATEST",
    TRIM_HORIZON: "TRIM_HORIZON",
};
export const ShardFilterType = {
    AFTER_SHARD_ID: "AFTER_SHARD_ID",
    AT_LATEST: "AT_LATEST",
    AT_TIMESTAMP: "AT_TIMESTAMP",
    AT_TRIM_HORIZON: "AT_TRIM_HORIZON",
    FROM_TIMESTAMP: "FROM_TIMESTAMP",
    FROM_TRIM_HORIZON: "FROM_TRIM_HORIZON",
};
export class ValidationException extends __BaseException {
    name = "ValidationException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ValidationException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ValidationException.prototype);
    }
}
export var SubscribeToShardEventStream;
(function (SubscribeToShardEventStream) {
    SubscribeToShardEventStream.visit = (value, visitor) => {
        if (value.SubscribeToShardEvent !== undefined)
            return visitor.SubscribeToShardEvent(value.SubscribeToShardEvent);
        if (value.ResourceNotFoundException !== undefined)
            return visitor.ResourceNotFoundException(value.ResourceNotFoundException);
        if (value.ResourceInUseException !== undefined)
            return visitor.ResourceInUseException(value.ResourceInUseException);
        if (value.KMSDisabledException !== undefined)
            return visitor.KMSDisabledException(value.KMSDisabledException);
        if (value.KMSInvalidStateException !== undefined)
            return visitor.KMSInvalidStateException(value.KMSInvalidStateException);
        if (value.KMSAccessDeniedException !== undefined)
            return visitor.KMSAccessDeniedException(value.KMSAccessDeniedException);
        if (value.KMSNotFoundException !== undefined)
            return visitor.KMSNotFoundException(value.KMSNotFoundException);
        if (value.KMSOptInRequired !== undefined)
            return visitor.KMSOptInRequired(value.KMSOptInRequired);
        if (value.KMSThrottlingException !== undefined)
            return visitor.KMSThrottlingException(value.KMSThrottlingException);
        if (value.InternalFailureException !== undefined)
            return visitor.InternalFailureException(value.InternalFailureException);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(SubscribeToShardEventStream || (SubscribeToShardEventStream = {}));
export const ScalingType = {
    UNIFORM_SCALING: "UNIFORM_SCALING",
};
export const SubscribeToShardEventStreamFilterSensitiveLog = (obj) => {
    if (obj.SubscribeToShardEvent !== undefined)
        return { SubscribeToShardEvent: obj.SubscribeToShardEvent };
    if (obj.ResourceNotFoundException !== undefined)
        return { ResourceNotFoundException: obj.ResourceNotFoundException };
    if (obj.ResourceInUseException !== undefined)
        return { ResourceInUseException: obj.ResourceInUseException };
    if (obj.KMSDisabledException !== undefined)
        return { KMSDisabledException: obj.KMSDisabledException };
    if (obj.KMSInvalidStateException !== undefined)
        return { KMSInvalidStateException: obj.KMSInvalidStateException };
    if (obj.KMSAccessDeniedException !== undefined)
        return { KMSAccessDeniedException: obj.KMSAccessDeniedException };
    if (obj.KMSNotFoundException !== undefined)
        return { KMSNotFoundException: obj.KMSNotFoundException };
    if (obj.KMSOptInRequired !== undefined)
        return { KMSOptInRequired: obj.KMSOptInRequired };
    if (obj.KMSThrottlingException !== undefined)
        return { KMSThrottlingException: obj.KMSThrottlingException };
    if (obj.InternalFailureException !== undefined)
        return { InternalFailureException: obj.InternalFailureException };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const SubscribeToShardOutputFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.EventStream && { EventStream: "STREAMING_CONTENT" }),
});
