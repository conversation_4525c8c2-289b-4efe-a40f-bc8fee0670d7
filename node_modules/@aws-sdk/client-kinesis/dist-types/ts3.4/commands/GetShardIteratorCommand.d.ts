import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  KinesisClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../KinesisClient";
import {
  GetShardIteratorInput,
  GetShardIteratorOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface GetShardIteratorCommandInput extends GetShardIteratorInput {}
export interface GetShardIteratorCommandOutput
  extends GetShardIteratorOutput,
    __MetadataBearer {}
declare const GetShardIteratorCommand_base: {
  new (
    input: GetShardIteratorCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetShardIteratorCommandInput,
    GetShardIteratorCommandOutput,
    KinesisClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetShardIteratorCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetShardIteratorCommandInput,
    GetShardIteratorCommandOutput,
    KinesisClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetShardIteratorCommand extends GetShardIteratorCommand_base {
  protected static __types: {
    api: {
      input: GetShardIteratorInput;
      output: GetShardIteratorOutput;
    };
    sdk: {
      input: GetShardIteratorCommandInput;
      output: GetShardIteratorCommandOutput;
    };
  };
}
